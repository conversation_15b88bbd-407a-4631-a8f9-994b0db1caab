name: CI

on:
  push:
  pull_request:
  workflow_dispatch:
  schedule: [cron: "40 1 * * *"]

permissions:
  contents: read

env:
  RUSTFLAGS: -Dwarnings

jobs:
  pre_ci:
    uses: dtolnay/.github/.github/workflows/pre_ci.yml@master

  test:
    name: Tests
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
        with:
          components: llvm-tools, rustc-dev
      - run: cargo test --all-features --release --tests
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: Cargo.lock
          path: Cargo.lock
        continue-on-error: true

  build:
    name: ${{matrix.name || format('Rust {0}', matrix.rust)}}
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ${{matrix.os || 'ubuntu'}}-latest
    strategy:
      fail-fast: false
      matrix:
        rust: [stable, beta, 1.65.0, 1.61.0]
        include:
          - rust: nightly
            components: rustc-dev
          - rust: nightly
            name: WebAssembly
            target: wasm32-unknown-unknown
          - rust: nightly
            name: WASI preview1
            target: wasm32-wasip1
          - rust: nightly
            name: WASI preview2
            target: wasm32-wasip2
          - rust: nightly
            name: Windows
            os: windows
    env:
      target: ${{matrix.target && format('--target={0}', matrix.target)}}
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{matrix.rust}}
          targets: ${{matrix.target}}
          components: ${{matrix.components}}
      - run: cargo check ${{env.target}} --no-default-features
      - run: cargo check ${{env.target}}
      - run: cargo check ${{env.target}} --features full
      - run: cargo check ${{env.target}} --features 'fold visit visit-mut'
      - run: cargo check ${{env.target}} --features 'full fold visit visit-mut'
      - run: cargo check ${{env.target}} --no-default-features --features derive
      - run: cargo check ${{env.target}} --no-default-features --features 'derive parsing'
      - run: cargo check ${{env.target}} --no-default-features --features 'derive printing'
      - run: cargo check ${{env.target}} --no-default-features --features 'proc-macro parsing printing'
      - run: cargo check ${{env.target}} --no-default-features --features full
      - run: cargo check ${{env.target}} --no-default-features --features 'full parsing'
      - run: cargo check ${{env.target}} --no-default-features --features 'full printing'
      - run: cargo check ${{env.target}} --no-default-features --features 'full parsing printing'
      - run: cargo check ${{env.target}} --no-default-features --features 'fold visit visit-mut parsing printing'
      - run: cargo check ${{env.target}} --no-default-features --features 'full fold visit visit-mut parsing printing'
      - if: matrix.components == 'rustc-dev'
        run: cargo check --benches --all-features --release
      - if: matrix.rust != '1.61.0'
        run: cargo check ${{env.target}} --manifest-path json/Cargo.toml --no-default-features

  examples:
    name: Examples
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
      - run: cargo check --manifest-path examples/dump-syntax/Cargo.toml
      - run: cargo check --manifest-path examples/heapsize/example/Cargo.toml
      - run: cargo check --manifest-path examples/lazy-static/example/Cargo.toml
      - run: cargo check --manifest-path examples/trace-var/example/Cargo.toml

  doc:
    name: Documentation
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    env:
      RUSTDOCFLAGS: -Dwarnings
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
      - uses: dtolnay/install@cargo-docs-rs
      - run: cargo docs-rs
      - run: cargo docs-rs --manifest-path json/Cargo.toml
      - run: cargo test --all-features --doc

  codegen:
    name: Codegen
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - run: cargo run --manifest-path codegen/Cargo.toml
      - run: git diff --exit-code

  minimal:
    name: Minimal versions
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
      - run: cargo generate-lockfile -Z minimal-versions
      - run: cargo check --all-features --locked

  fuzz:
    name: Fuzz
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
      - uses: dtolnay/install@cargo-fuzz
      - run: cargo fuzz check

  miri:
    name: Miri
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: nightly-2025-05-16  # https://github.com/rust-lang/miri/issues/4323
          components: miri, rust-src
      - run: cargo miri setup
      - run: cargo miri test --all-features ${{github.event_name == 'pull_request' && '--tests' || ''}}
        env:
          MIRIFLAGS: -Zmiri-strict-provenance

  clippy:
    name: Clippy
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
        with:
          components: clippy,rustc-dev
      - run: cargo clippy --all-features --tests --benches -- -Dclippy::all -Dclippy::pedantic
      - run: cargo clippy --manifest-path codegen/Cargo.toml -- -Dclippy::all -Dclippy::pedantic

  outdated:
    name: Outdated
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - uses: dtolnay/install@cargo-outdated
      - run: cargo outdated --workspace --exit-code 1
      - run: cargo outdated --manifest-path fuzz/Cargo.toml --exit-code 1
      - run: cargo outdated --manifest-path codegen/Cargo.toml --exit-code 1
