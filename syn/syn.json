{"version": "2.0.104", "types": [{"ident": "<PERSON><PERSON>", "features": {"any": ["derive", "full"]}, "fields": {"extern_token": {"token": "Extern"}, "name": {"option": {"syn": "LitStr"}}}}, {"ident": "AngleBracketedGenericArguments", "features": {"any": ["derive", "full"]}, "fields": {"colon2_token": {"option": {"token": "PathSep"}}, "lt_token": {"token": "Lt"}, "args": {"punctuated": {"element": {"syn": "GenericArgument"}, "punct": "Comma"}}, "gt_token": {"token": "Gt"}}}, {"ident": "Arm", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "pat": {"syn": "<PERSON>"}, "guard": {"option": {"tuple": [{"token": "If"}, {"box": {"syn": "Expr"}}]}}, "fat_arrow_token": {"token": "FatArrow"}, "body": {"box": {"syn": "Expr"}}, "comma": {"option": {"token": "Comma"}}}}, {"ident": "AssocConst", "features": {"any": ["derive", "full"]}, "fields": {"ident": {"proc_macro2": "Ident"}, "generics": {"option": {"syn": "AngleBracketedGenericArguments"}}, "eq_token": {"token": "Eq"}, "value": {"syn": "Expr"}}}, {"ident": "AssocType", "features": {"any": ["derive", "full"]}, "fields": {"ident": {"proc_macro2": "Ident"}, "generics": {"option": {"syn": "AngleBracketedGenericArguments"}}, "eq_token": {"token": "Eq"}, "ty": {"syn": "Type"}}}, {"ident": "AttrStyle", "features": {"any": ["derive", "full"]}, "variants": {"Outer": [], "Inner": [{"token": "Not"}]}}, {"ident": "Attribute", "features": {"any": ["derive", "full"]}, "fields": {"pound_token": {"token": "Pound"}, "style": {"syn": "AttrStyle"}, "bracket_token": {"group": "Bracket"}, "meta": {"syn": "Meta"}}}, {"ident": "BareFnArg", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "name": {"option": {"tuple": [{"proc_macro2": "Ident"}, {"token": "Colon"}]}}, "ty": {"syn": "Type"}}}, {"ident": "BareVariadic", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "name": {"option": {"tuple": [{"proc_macro2": "Ident"}, {"token": "Colon"}]}}, "dots": {"token": "DotDotDot"}, "comma": {"option": {"token": "Comma"}}}}, {"ident": "BinOp", "features": {"any": ["derive", "full"]}, "variants": {"Add": [{"token": "Plus"}], "Sub": [{"token": "Minus"}], "Mul": [{"token": "Star"}], "Div": [{"token": "Slash"}], "Rem": [{"token": "Percent"}], "And": [{"token": "AndAnd"}], "Or": [{"token": "OrOr"}], "BitXor": [{"token": "<PERSON><PERSON>"}], "BitAnd": [{"token": "And"}], "BitOr": [{"token": "Or"}], "Shl": [{"token": "Shl"}], "Shr": [{"token": "Shr"}], "Eq": [{"token": "EqEq"}], "Lt": [{"token": "Lt"}], "Le": [{"token": "Le"}], "Ne": [{"token": "Ne"}], "Ge": [{"token": "Ge"}], "Gt": [{"token": "Gt"}], "AddAssign": [{"token": "PlusEq"}], "SubAssign": [{"token": "MinusEq"}], "MulAssign": [{"token": "StarEq"}], "DivAssign": [{"token": "SlashEq"}], "RemAssign": [{"token": "PercentEq"}], "BitXorAssign": [{"token": "CaretEq"}], "BitAndAssign": [{"token": "AndEq"}], "BitOrAssign": [{"token": "OrEq"}], "ShlAssign": [{"token": "ShlEq"}], "ShrAssign": [{"token": "ShrEq"}]}, "exhaustive": false}, {"ident": "Block", "features": {"any": ["full"]}, "fields": {"brace_token": {"group": "Brace"}, "stmts": {"vec": {"syn": "Stmt"}}}}, {"ident": "BoundLifetimes", "features": {"any": ["derive", "full"]}, "fields": {"for_token": {"token": "For"}, "lt_token": {"token": "Lt"}, "lifetimes": {"punctuated": {"element": {"syn": "GenericParam"}, "punct": "Comma"}}, "gt_token": {"token": "Gt"}}}, {"ident": "Captured<PERSON><PERSON><PERSON>", "features": {"any": ["full"]}, "variants": {"Lifetime": [{"syn": "Lifetime"}], "Ident": [{"proc_macro2": "Ident"}]}, "exhaustive": false}, {"ident": "ConstParam", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "const_token": {"token": "Const"}, "ident": {"proc_macro2": "Ident"}, "colon_token": {"token": "Colon"}, "ty": {"syn": "Type"}, "eq_token": {"option": {"token": "Eq"}}, "default": {"option": {"syn": "Expr"}}}}, {"ident": "Constraint", "features": {"any": ["derive", "full"]}, "fields": {"ident": {"proc_macro2": "Ident"}, "generics": {"option": {"syn": "AngleBracketedGenericArguments"}}, "colon_token": {"token": "Colon"}, "bounds": {"punctuated": {"element": {"syn": "TypeParamBound"}, "punct": "Plus"}}}}, {"ident": "Data", "features": {"any": ["derive"]}, "variants": {"Struct": [{"syn": "DataStruct"}], "Enum": [{"syn": "DataEnum"}], "Union": [{"syn": "DataUnion"}]}}, {"ident": "DataEnum", "features": {"any": ["derive"]}, "fields": {"enum_token": {"token": "Enum"}, "brace_token": {"group": "Brace"}, "variants": {"punctuated": {"element": {"syn": "<PERSON><PERSON><PERSON>"}, "punct": "Comma"}}}}, {"ident": "DataStruct", "features": {"any": ["derive"]}, "fields": {"struct_token": {"token": "Struct"}, "fields": {"syn": "Fields"}, "semi_token": {"option": {"token": "Semi"}}}}, {"ident": "DataUnion", "features": {"any": ["derive"]}, "fields": {"union_token": {"token": "Union"}, "fields": {"syn": "FieldsNamed"}}}, {"ident": "DeriveInput", "features": {"any": ["derive"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "data": {"syn": "Data"}}}, {"ident": "Expr", "features": {"any": ["derive", "full"]}, "variants": {"Array": [{"syn": "ExprArray"}], "Assign": [{"syn": "ExprAssign"}], "Async": [{"syn": "ExprAsync"}], "Await": [{"syn": "ExprAwait"}], "Binary": [{"syn": "ExprBinary"}], "Block": [{"syn": "ExprBlock"}], "Break": [{"syn": "ExprBreak"}], "Call": [{"syn": "ExprCall"}], "Cast": [{"syn": "ExprCast"}], "Closure": [{"syn": "ExprClosure"}], "Const": [{"syn": "ExprConst"}], "Continue": [{"syn": "ExprContinue"}], "Field": [{"syn": "ExprField"}], "ForLoop": [{"syn": "ExprForLoop"}], "Group": [{"syn": "ExprGroup"}], "If": [{"syn": "ExprIf"}], "Index": [{"syn": "ExprIndex"}], "Infer": [{"syn": "ExprInfer"}], "Let": [{"syn": "ExprLet"}], "Lit": [{"syn": "ExprLit"}], "Loop": [{"syn": "ExprLoop"}], "Macro": [{"syn": "ExprMacro"}], "Match": [{"syn": "ExprMatch"}], "MethodCall": [{"syn": "ExprMethodCall"}], "Paren": [{"syn": "ExprParen"}], "Path": [{"syn": "Expr<PERSON>ath"}], "Range": [{"syn": "ExprRange"}], "RawAddr": [{"syn": "ExprRawAddr"}], "Reference": [{"syn": "ExprReference"}], "Repeat": [{"syn": "ExprRepeat"}], "Return": [{"syn": "ExprReturn"}], "Struct": [{"syn": "ExprStruct"}], "Try": [{"syn": "ExprTry"}], "TryBlock": [{"syn": "ExprTryBlock"}], "Tuple": [{"syn": "ExprTuple"}], "Unary": [{"syn": "ExprUnary"}], "Unsafe": [{"syn": "ExprUnsafe"}], "Verbatim": [{"proc_macro2": "TokenStream"}], "While": [{"syn": "<PERSON>pr<PERSON><PERSON><PERSON>"}], "Yield": [{"syn": "ExprYield"}]}, "exhaustive": false}, {"ident": "ExprArray", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "bracket_token": {"group": "Bracket"}, "elems": {"punctuated": {"element": {"syn": "Expr"}, "punct": "Comma"}}}}, {"ident": "ExprAssign", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "left": {"box": {"syn": "Expr"}}, "eq_token": {"token": "Eq"}, "right": {"box": {"syn": "Expr"}}}}, {"ident": "ExprAsync", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "async_token": {"token": "Async"}, "capture": {"option": {"token": "Move"}}, "block": {"syn": "Block"}}}, {"ident": "ExprAwait", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "base": {"box": {"syn": "Expr"}}, "dot_token": {"token": "Dot"}, "await_token": {"token": "Await"}}}, {"ident": "ExprBinary", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "left": {"box": {"syn": "Expr"}}, "op": {"syn": "BinOp"}, "right": {"box": {"syn": "Expr"}}}}, {"ident": "ExprBlock", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "label": {"option": {"syn": "Label"}}, "block": {"syn": "Block"}}}, {"ident": "ExprBreak", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "break_token": {"token": "Break"}, "label": {"option": {"syn": "Lifetime"}}, "expr": {"option": {"box": {"syn": "Expr"}}}}}, {"ident": "ExprCall", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "func": {"box": {"syn": "Expr"}}, "paren_token": {"group": "<PERSON><PERSON>"}, "args": {"punctuated": {"element": {"syn": "Expr"}, "punct": "Comma"}}}}, {"ident": "ExprCast", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "expr": {"box": {"syn": "Expr"}}, "as_token": {"token": "As"}, "ty": {"box": {"syn": "Type"}}}}, {"ident": "ExprClosure", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "lifetimes": {"option": {"syn": "BoundLifetimes"}}, "constness": {"option": {"token": "Const"}}, "movability": {"option": {"token": "Static"}}, "asyncness": {"option": {"token": "Async"}}, "capture": {"option": {"token": "Move"}}, "or1_token": {"token": "Or"}, "inputs": {"punctuated": {"element": {"syn": "<PERSON>"}, "punct": "Comma"}}, "or2_token": {"token": "Or"}, "output": {"syn": "ReturnType"}, "body": {"box": {"syn": "Expr"}}}}, {"ident": "ExprConst", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "const_token": {"token": "Const"}, "block": {"syn": "Block"}}}, {"ident": "ExprContinue", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "continue_token": {"token": "Continue"}, "label": {"option": {"syn": "Lifetime"}}}}, {"ident": "ExprField", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "base": {"box": {"syn": "Expr"}}, "dot_token": {"token": "Dot"}, "member": {"syn": "Member"}}}, {"ident": "ExprForLoop", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "label": {"option": {"syn": "Label"}}, "for_token": {"token": "For"}, "pat": {"box": {"syn": "<PERSON>"}}, "in_token": {"token": "In"}, "expr": {"box": {"syn": "Expr"}}, "body": {"syn": "Block"}}}, {"ident": "ExprGroup", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "group_token": {"group": "Group"}, "expr": {"box": {"syn": "Expr"}}}}, {"ident": "ExprIf", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "if_token": {"token": "If"}, "cond": {"box": {"syn": "Expr"}}, "then_branch": {"syn": "Block"}, "else_branch": {"option": {"tuple": [{"token": "Else"}, {"box": {"syn": "Expr"}}]}}}}, {"ident": "ExprIndex", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "expr": {"box": {"syn": "Expr"}}, "bracket_token": {"group": "Bracket"}, "index": {"box": {"syn": "Expr"}}}}, {"ident": "ExprInfer", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "underscore_token": {"token": "Underscore"}}}, {"ident": "ExprLet", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "let_token": {"token": "Let"}, "pat": {"box": {"syn": "<PERSON>"}}, "eq_token": {"token": "Eq"}, "expr": {"box": {"syn": "Expr"}}}}, {"ident": "ExprLit", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "lit": {"syn": "Lit"}}}, {"ident": "ExprLoop", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "label": {"option": {"syn": "Label"}}, "loop_token": {"token": "Loop"}, "body": {"syn": "Block"}}}, {"ident": "ExprMacro", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "mac": {"syn": "Macro"}}}, {"ident": "ExprMatch", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "match_token": {"token": "Match"}, "expr": {"box": {"syn": "Expr"}}, "brace_token": {"group": "Brace"}, "arms": {"vec": {"syn": "Arm"}}}}, {"ident": "ExprMethodCall", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "receiver": {"box": {"syn": "Expr"}}, "dot_token": {"token": "Dot"}, "method": {"proc_macro2": "Ident"}, "turbofish": {"option": {"syn": "AngleBracketedGenericArguments"}}, "paren_token": {"group": "<PERSON><PERSON>"}, "args": {"punctuated": {"element": {"syn": "Expr"}, "punct": "Comma"}}}}, {"ident": "ExprParen", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "paren_token": {"group": "<PERSON><PERSON>"}, "expr": {"box": {"syn": "Expr"}}}}, {"ident": "Expr<PERSON>ath", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "qself": {"option": {"syn": "QSelf"}}, "path": {"syn": "Path"}}}, {"ident": "ExprRange", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "start": {"option": {"box": {"syn": "Expr"}}}, "limits": {"syn": "RangeLimits"}, "end": {"option": {"box": {"syn": "Expr"}}}}}, {"ident": "ExprRawAddr", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "and_token": {"token": "And"}, "raw": {"token": "Raw"}, "mutability": {"syn": "PointerMutability"}, "expr": {"box": {"syn": "Expr"}}}}, {"ident": "ExprReference", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "and_token": {"token": "And"}, "mutability": {"option": {"token": "<PERSON><PERSON>"}}, "expr": {"box": {"syn": "Expr"}}}}, {"ident": "ExprRepeat", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "bracket_token": {"group": "Bracket"}, "expr": {"box": {"syn": "Expr"}}, "semi_token": {"token": "Semi"}, "len": {"box": {"syn": "Expr"}}}}, {"ident": "ExprReturn", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "return_token": {"token": "Return"}, "expr": {"option": {"box": {"syn": "Expr"}}}}}, {"ident": "ExprStruct", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "qself": {"option": {"syn": "QSelf"}}, "path": {"syn": "Path"}, "brace_token": {"group": "Brace"}, "fields": {"punctuated": {"element": {"syn": "FieldValue"}, "punct": "Comma"}}, "dot2_token": {"option": {"token": "DotDot"}}, "rest": {"option": {"box": {"syn": "Expr"}}}}}, {"ident": "ExprTry", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "expr": {"box": {"syn": "Expr"}}, "question_token": {"token": "Question"}}}, {"ident": "ExprTryBlock", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "try_token": {"token": "Try"}, "block": {"syn": "Block"}}}, {"ident": "ExprTuple", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "paren_token": {"group": "<PERSON><PERSON>"}, "elems": {"punctuated": {"element": {"syn": "Expr"}, "punct": "Comma"}}}}, {"ident": "ExprUnary", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "op": {"syn": "UnOp"}, "expr": {"box": {"syn": "Expr"}}}}, {"ident": "ExprUnsafe", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "unsafe_token": {"token": "Unsafe"}, "block": {"syn": "Block"}}}, {"ident": "<PERSON>pr<PERSON><PERSON><PERSON>", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "label": {"option": {"syn": "Label"}}, "while_token": {"token": "While"}, "cond": {"box": {"syn": "Expr"}}, "body": {"syn": "Block"}}}, {"ident": "ExprYield", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "yield_token": {"token": "Yield"}, "expr": {"option": {"box": {"syn": "Expr"}}}}}, {"ident": "Field", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "mutability": {"syn": "FieldMutability"}, "ident": {"option": {"proc_macro2": "Ident"}}, "colon_token": {"option": {"token": "Colon"}}, "ty": {"syn": "Type"}}}, {"ident": "FieldMutability", "features": {"any": ["derive", "full"]}, "variants": {"None": []}, "exhaustive": false}, {"ident": "FieldPat", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "member": {"syn": "Member"}, "colon_token": {"option": {"token": "Colon"}}, "pat": {"box": {"syn": "<PERSON>"}}}}, {"ident": "FieldValue", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "member": {"syn": "Member"}, "colon_token": {"option": {"token": "Colon"}}, "expr": {"syn": "Expr"}}}, {"ident": "Fields", "features": {"any": ["derive", "full"]}, "variants": {"Named": [{"syn": "FieldsNamed"}], "Unnamed": [{"syn": "<PERSON><PERSON><PERSON><PERSON>"}], "Unit": []}}, {"ident": "FieldsNamed", "features": {"any": ["derive", "full"]}, "fields": {"brace_token": {"group": "Brace"}, "named": {"punctuated": {"element": {"syn": "Field"}, "punct": "Comma"}}}}, {"ident": "<PERSON><PERSON><PERSON><PERSON>", "features": {"any": ["derive", "full"]}, "fields": {"paren_token": {"group": "<PERSON><PERSON>"}, "unnamed": {"punctuated": {"element": {"syn": "Field"}, "punct": "Comma"}}}}, {"ident": "File", "features": {"any": ["full"]}, "fields": {"shebang": {"option": {"std": "String"}}, "attrs": {"vec": {"syn": "Attribute"}}, "items": {"vec": {"syn": "<PERSON><PERSON>"}}}}, {"ident": "FnArg", "features": {"any": ["full"]}, "variants": {"Receiver": [{"syn": "Receiver"}], "Typed": [{"syn": "PatType"}]}}, {"ident": "ForeignItem", "features": {"any": ["full"]}, "variants": {"Fn": [{"syn": "ForeignItemFn"}], "Static": [{"syn": "ForeignItemStatic"}], "Type": [{"syn": "ForeignItemType"}], "Macro": [{"syn": "ForeignItemMacro"}], "Verbatim": [{"proc_macro2": "TokenStream"}]}, "exhaustive": false}, {"ident": "ForeignItemFn", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "sig": {"syn": "Signature"}, "semi_token": {"token": "Semi"}}}, {"ident": "ForeignItemMacro", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "mac": {"syn": "Macro"}, "semi_token": {"option": {"token": "Semi"}}}}, {"ident": "ForeignItemStatic", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "static_token": {"token": "Static"}, "mutability": {"syn": "StaticMutability"}, "ident": {"proc_macro2": "Ident"}, "colon_token": {"token": "Colon"}, "ty": {"box": {"syn": "Type"}}, "semi_token": {"token": "Semi"}}}, {"ident": "ForeignItemType", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "type_token": {"token": "Type"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "semi_token": {"token": "Semi"}}}, {"ident": "GenericArgument", "features": {"any": ["derive", "full"]}, "variants": {"Lifetime": [{"syn": "Lifetime"}], "Type": [{"syn": "Type"}], "Const": [{"syn": "Expr"}], "AssocType": [{"syn": "AssocType"}], "AssocConst": [{"syn": "AssocConst"}], "Constraint": [{"syn": "Constraint"}]}, "exhaustive": false}, {"ident": "GenericParam", "features": {"any": ["derive", "full"]}, "variants": {"Lifetime": [{"syn": "LifetimeParam"}], "Type": [{"syn": "TypeParam"}], "Const": [{"syn": "ConstParam"}]}}, {"ident": "Generics", "features": {"any": ["derive", "full"]}, "fields": {"lt_token": {"option": {"token": "Lt"}}, "params": {"punctuated": {"element": {"syn": "GenericParam"}, "punct": "Comma"}}, "gt_token": {"option": {"token": "Gt"}}, "where_clause": {"option": {"syn": "<PERSON><PERSON><PERSON><PERSON>"}}}}, {"ident": "ImplItem", "features": {"any": ["full"]}, "variants": {"Const": [{"syn": "ImplItemConst"}], "Fn": [{"syn": "ImplItemFn"}], "Type": [{"syn": "ImplItemType"}], "Macro": [{"syn": "ImplItemMacro"}], "Verbatim": [{"proc_macro2": "TokenStream"}]}, "exhaustive": false}, {"ident": "ImplItemConst", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "defaultness": {"option": {"token": "<PERSON><PERSON><PERSON>"}}, "const_token": {"token": "Const"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "colon_token": {"token": "Colon"}, "ty": {"syn": "Type"}, "eq_token": {"token": "Eq"}, "expr": {"syn": "Expr"}, "semi_token": {"token": "Semi"}}}, {"ident": "ImplItemFn", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "defaultness": {"option": {"token": "<PERSON><PERSON><PERSON>"}}, "sig": {"syn": "Signature"}, "block": {"syn": "Block"}}}, {"ident": "ImplItemMacro", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "mac": {"syn": "Macro"}, "semi_token": {"option": {"token": "Semi"}}}}, {"ident": "ImplItemType", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "defaultness": {"option": {"token": "<PERSON><PERSON><PERSON>"}}, "type_token": {"token": "Type"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "eq_token": {"token": "Eq"}, "ty": {"syn": "Type"}, "semi_token": {"token": "Semi"}}}, {"ident": "ImplRestriction", "features": {"any": ["full"]}, "variants": {}, "exhaustive": false}, {"ident": "Index", "features": {"any": ["derive", "full"]}, "fields": {"index": {"std": "u32"}, "span": {"proc_macro2": "Span"}}}, {"ident": "<PERSON><PERSON>", "features": {"any": ["full"]}, "variants": {"Const": [{"syn": "ItemConst"}], "Enum": [{"syn": "ItemEnum"}], "ExternCrate": [{"syn": "ItemExternCrate"}], "Fn": [{"syn": "ItemFn"}], "ForeignMod": [{"syn": "ItemForeignMod"}], "Impl": [{"syn": "ItemImpl"}], "Macro": [{"syn": "<PERSON>emMac<PERSON>"}], "Mod": [{"syn": "ItemMod"}], "Static": [{"syn": "ItemStatic"}], "Struct": [{"syn": "ItemStruct"}], "Trait": [{"syn": "ItemTrait"}], "TraitAlias": [{"syn": "ItemTraitAlias"}], "Type": [{"syn": "ItemType"}], "Union": [{"syn": "ItemUnion"}], "Use": [{"syn": "ItemUse"}], "Verbatim": [{"proc_macro2": "TokenStream"}]}, "exhaustive": false}, {"ident": "ItemConst", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "const_token": {"token": "Const"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "colon_token": {"token": "Colon"}, "ty": {"box": {"syn": "Type"}}, "eq_token": {"token": "Eq"}, "expr": {"box": {"syn": "Expr"}}, "semi_token": {"token": "Semi"}}}, {"ident": "ItemEnum", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "enum_token": {"token": "Enum"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "brace_token": {"group": "Brace"}, "variants": {"punctuated": {"element": {"syn": "<PERSON><PERSON><PERSON>"}, "punct": "Comma"}}}}, {"ident": "ItemExternCrate", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "extern_token": {"token": "Extern"}, "crate_token": {"token": "Crate"}, "ident": {"proc_macro2": "Ident"}, "rename": {"option": {"tuple": [{"token": "As"}, {"proc_macro2": "Ident"}]}}, "semi_token": {"token": "Semi"}}}, {"ident": "ItemFn", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "sig": {"syn": "Signature"}, "block": {"box": {"syn": "Block"}}}}, {"ident": "ItemForeignMod", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "unsafety": {"option": {"token": "Unsafe"}}, "abi": {"syn": "<PERSON><PERSON>"}, "brace_token": {"group": "Brace"}, "items": {"vec": {"syn": "ForeignItem"}}}}, {"ident": "ItemImpl", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "defaultness": {"option": {"token": "<PERSON><PERSON><PERSON>"}}, "unsafety": {"option": {"token": "Unsafe"}}, "impl_token": {"token": "Impl"}, "generics": {"syn": "Generics"}, "trait_": {"option": {"tuple": [{"option": {"token": "Not"}}, {"syn": "Path"}, {"token": "For"}]}}, "self_ty": {"box": {"syn": "Type"}}, "brace_token": {"group": "Brace"}, "items": {"vec": {"syn": "ImplItem"}}}}, {"ident": "<PERSON>emMac<PERSON>", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "ident": {"option": {"proc_macro2": "Ident"}}, "mac": {"syn": "Macro"}, "semi_token": {"option": {"token": "Semi"}}}}, {"ident": "ItemMod", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "unsafety": {"option": {"token": "Unsafe"}}, "mod_token": {"token": "Mod"}, "ident": {"proc_macro2": "Ident"}, "content": {"option": {"tuple": [{"group": "Brace"}, {"vec": {"syn": "<PERSON><PERSON>"}}]}}, "semi": {"option": {"token": "Semi"}}}}, {"ident": "ItemStatic", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "static_token": {"token": "Static"}, "mutability": {"syn": "StaticMutability"}, "ident": {"proc_macro2": "Ident"}, "colon_token": {"token": "Colon"}, "ty": {"box": {"syn": "Type"}}, "eq_token": {"token": "Eq"}, "expr": {"box": {"syn": "Expr"}}, "semi_token": {"token": "Semi"}}}, {"ident": "ItemStruct", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "struct_token": {"token": "Struct"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "fields": {"syn": "Fields"}, "semi_token": {"option": {"token": "Semi"}}}}, {"ident": "ItemTrait", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "unsafety": {"option": {"token": "Unsafe"}}, "auto_token": {"option": {"token": "Auto"}}, "restriction": {"option": {"syn": "ImplRestriction"}}, "trait_token": {"token": "<PERSON><PERSON><PERSON>"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "colon_token": {"option": {"token": "Colon"}}, "supertraits": {"punctuated": {"element": {"syn": "TypeParamBound"}, "punct": "Plus"}}, "brace_token": {"group": "Brace"}, "items": {"vec": {"syn": "TraitItem"}}}}, {"ident": "ItemTraitAlias", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "trait_token": {"token": "<PERSON><PERSON><PERSON>"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "eq_token": {"token": "Eq"}, "bounds": {"punctuated": {"element": {"syn": "TypeParamBound"}, "punct": "Plus"}}, "semi_token": {"token": "Semi"}}}, {"ident": "ItemType", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "type_token": {"token": "Type"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "eq_token": {"token": "Eq"}, "ty": {"box": {"syn": "Type"}}, "semi_token": {"token": "Semi"}}}, {"ident": "ItemUnion", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "union_token": {"token": "Union"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "fields": {"syn": "FieldsNamed"}}}, {"ident": "ItemUse", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "vis": {"syn": "Visibility"}, "use_token": {"token": "Use"}, "leading_colon": {"option": {"token": "PathSep"}}, "tree": {"syn": "UseTree"}, "semi_token": {"token": "Semi"}}}, {"ident": "Label", "features": {"any": ["full"]}, "fields": {"name": {"syn": "Lifetime"}, "colon_token": {"token": "Colon"}}}, {"ident": "Lifetime", "features": {"any": []}, "fields": {"apostrophe": {"proc_macro2": "Span"}, "ident": {"proc_macro2": "Ident"}}}, {"ident": "LifetimeParam", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "lifetime": {"syn": "Lifetime"}, "colon_token": {"option": {"token": "Colon"}}, "bounds": {"punctuated": {"element": {"syn": "Lifetime"}, "punct": "Plus"}}}}, {"ident": "Lit", "features": {"any": []}, "variants": {"Str": [{"syn": "LitStr"}], "ByteStr": [{"syn": "LitByteStr"}], "CStr": [{"syn": "LitCStr"}], "Byte": [{"syn": "LitByte"}], "Char": [{"syn": "LitChar"}], "Int": [{"syn": "LitInt"}], "Float": [{"syn": "LitFloat"}], "Bool": [{"syn": "LitBool"}], "Verbatim": [{"proc_macro2": "Literal"}]}, "exhaustive": false}, {"ident": "LitBool", "features": {"any": []}, "fields": {"value": {"std": "bool"}, "span": {"proc_macro2": "Span"}}}, {"ident": "LitByte", "features": {"any": []}}, {"ident": "LitByteStr", "features": {"any": []}}, {"ident": "LitCStr", "features": {"any": []}}, {"ident": "LitChar", "features": {"any": []}}, {"ident": "LitFloat", "features": {"any": []}}, {"ident": "LitInt", "features": {"any": []}}, {"ident": "LitStr", "features": {"any": []}}, {"ident": "Local", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "let_token": {"token": "Let"}, "pat": {"syn": "<PERSON>"}, "init": {"option": {"syn": "LocalInit"}}, "semi_token": {"token": "Semi"}}}, {"ident": "LocalInit", "features": {"any": ["full"]}, "fields": {"eq_token": {"token": "Eq"}, "expr": {"box": {"syn": "Expr"}}, "diverge": {"option": {"tuple": [{"token": "Else"}, {"box": {"syn": "Expr"}}]}}}}, {"ident": "Macro", "features": {"any": ["derive", "full"]}, "fields": {"path": {"syn": "Path"}, "bang_token": {"token": "Not"}, "delimiter": {"syn": "MacroDelimiter"}, "tokens": {"proc_macro2": "TokenStream"}}}, {"ident": "MacroDelimiter", "features": {"any": ["derive", "full"]}, "variants": {"Paren": [{"group": "<PERSON><PERSON>"}], "Brace": [{"group": "Brace"}], "Bracket": [{"group": "Bracket"}]}}, {"ident": "Member", "features": {"any": ["derive", "full"]}, "variants": {"Named": [{"proc_macro2": "Ident"}], "Unnamed": [{"syn": "Index"}]}}, {"ident": "Meta", "features": {"any": ["derive", "full"]}, "variants": {"Path": [{"syn": "Path"}], "List": [{"syn": "MetaList"}], "NameValue": [{"syn": "MetaNameValue"}]}}, {"ident": "MetaList", "features": {"any": ["derive", "full"]}, "fields": {"path": {"syn": "Path"}, "delimiter": {"syn": "MacroDelimiter"}, "tokens": {"proc_macro2": "TokenStream"}}}, {"ident": "MetaNameValue", "features": {"any": ["derive", "full"]}, "fields": {"path": {"syn": "Path"}, "eq_token": {"token": "Eq"}, "value": {"syn": "Expr"}}}, {"ident": "ParenthesizedGenericArguments", "features": {"any": ["derive", "full"]}, "fields": {"paren_token": {"group": "<PERSON><PERSON>"}, "inputs": {"punctuated": {"element": {"syn": "Type"}, "punct": "Comma"}}, "output": {"syn": "ReturnType"}}}, {"ident": "<PERSON>", "features": {"any": ["full"]}, "variants": {"Const": [{"syn": "ExprConst"}], "Ident": [{"syn": "PatIdent"}], "Lit": [{"syn": "ExprLit"}], "Macro": [{"syn": "ExprMacro"}], "Or": [{"syn": "<PERSON><PERSON><PERSON>"}], "Paren": [{"syn": "<PERSON><PERSON><PERSON><PERSON>"}], "Path": [{"syn": "Expr<PERSON>ath"}], "Range": [{"syn": "ExprRange"}], "Reference": [{"syn": "PatReference"}], "Rest": [{"syn": "PatRest"}], "Slice": [{"syn": "PatSlice"}], "Struct": [{"syn": "PatStruct"}], "Tuple": [{"syn": "<PERSON><PERSON><PERSON><PERSON>"}], "TupleStruct": [{"syn": "PatTupleStruct"}], "Type": [{"syn": "PatType"}], "Verbatim": [{"proc_macro2": "TokenStream"}], "Wild": [{"syn": "Pat<PERSON><PERSON><PERSON>"}]}, "exhaustive": false}, {"ident": "PatIdent", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "by_ref": {"option": {"token": "Ref"}}, "mutability": {"option": {"token": "<PERSON><PERSON>"}}, "ident": {"proc_macro2": "Ident"}, "subpat": {"option": {"tuple": [{"token": "At"}, {"box": {"syn": "<PERSON>"}}]}}}}, {"ident": "<PERSON><PERSON><PERSON>", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "leading_vert": {"option": {"token": "Or"}}, "cases": {"punctuated": {"element": {"syn": "<PERSON>"}, "punct": "Or"}}}}, {"ident": "<PERSON><PERSON><PERSON><PERSON>", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "paren_token": {"group": "<PERSON><PERSON>"}, "pat": {"box": {"syn": "<PERSON>"}}}}, {"ident": "PatReference", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "and_token": {"token": "And"}, "mutability": {"option": {"token": "<PERSON><PERSON>"}}, "pat": {"box": {"syn": "<PERSON>"}}}}, {"ident": "PatRest", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "dot2_token": {"token": "DotDot"}}}, {"ident": "PatSlice", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "bracket_token": {"group": "Bracket"}, "elems": {"punctuated": {"element": {"syn": "<PERSON>"}, "punct": "Comma"}}}}, {"ident": "PatStruct", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "qself": {"option": {"syn": "QSelf"}}, "path": {"syn": "Path"}, "brace_token": {"group": "Brace"}, "fields": {"punctuated": {"element": {"syn": "FieldPat"}, "punct": "Comma"}}, "rest": {"option": {"syn": "PatRest"}}}}, {"ident": "<PERSON><PERSON><PERSON><PERSON>", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "paren_token": {"group": "<PERSON><PERSON>"}, "elems": {"punctuated": {"element": {"syn": "<PERSON>"}, "punct": "Comma"}}}}, {"ident": "PatTupleStruct", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "qself": {"option": {"syn": "QSelf"}}, "path": {"syn": "Path"}, "paren_token": {"group": "<PERSON><PERSON>"}, "elems": {"punctuated": {"element": {"syn": "<PERSON>"}, "punct": "Comma"}}}}, {"ident": "PatType", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "pat": {"box": {"syn": "<PERSON>"}}, "colon_token": {"token": "Colon"}, "ty": {"box": {"syn": "Type"}}}}, {"ident": "Pat<PERSON><PERSON><PERSON>", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "underscore_token": {"token": "Underscore"}}}, {"ident": "Path", "features": {"any": ["derive", "full"]}, "fields": {"leading_colon": {"option": {"token": "PathSep"}}, "segments": {"punctuated": {"element": {"syn": "PathSegment"}, "punct": "PathSep"}}}}, {"ident": "PathArguments", "features": {"any": ["derive", "full"]}, "variants": {"None": [], "AngleBracketed": [{"syn": "AngleBracketedGenericArguments"}], "Parenthesized": [{"syn": "ParenthesizedGenericArguments"}]}}, {"ident": "PathSegment", "features": {"any": ["derive", "full"]}, "fields": {"ident": {"proc_macro2": "Ident"}, "arguments": {"syn": "PathArguments"}}}, {"ident": "PointerMutability", "features": {"any": ["full"]}, "variants": {"Const": [{"token": "Const"}], "Mut": [{"token": "<PERSON><PERSON>"}]}}, {"ident": "PreciseCapture", "features": {"any": ["full"]}, "fields": {"use_token": {"token": "Use"}, "lt_token": {"token": "Lt"}, "params": {"punctuated": {"element": {"syn": "Captured<PERSON><PERSON><PERSON>"}, "punct": "Comma"}}, "gt_token": {"token": "Gt"}}}, {"ident": "PredicateLifetime", "features": {"any": ["derive", "full"]}, "fields": {"lifetime": {"syn": "Lifetime"}, "colon_token": {"token": "Colon"}, "bounds": {"punctuated": {"element": {"syn": "Lifetime"}, "punct": "Plus"}}}}, {"ident": "PredicateType", "features": {"any": ["derive", "full"]}, "fields": {"lifetimes": {"option": {"syn": "BoundLifetimes"}}, "bounded_ty": {"syn": "Type"}, "colon_token": {"token": "Colon"}, "bounds": {"punctuated": {"element": {"syn": "TypeParamBound"}, "punct": "Plus"}}}}, {"ident": "QSelf", "features": {"any": ["derive", "full"]}, "fields": {"lt_token": {"token": "Lt"}, "ty": {"box": {"syn": "Type"}}, "position": {"std": "usize"}, "as_token": {"option": {"token": "As"}}, "gt_token": {"token": "Gt"}}}, {"ident": "RangeLimits", "features": {"any": ["full"]}, "variants": {"HalfOpen": [{"token": "DotDot"}], "Closed": [{"token": "DotDotEq"}]}}, {"ident": "Receiver", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "reference": {"option": {"tuple": [{"token": "And"}, {"option": {"syn": "Lifetime"}}]}}, "mutability": {"option": {"token": "<PERSON><PERSON>"}}, "self_token": {"token": "SelfValue"}, "colon_token": {"option": {"token": "Colon"}}, "ty": {"box": {"syn": "Type"}}}}, {"ident": "ReturnType", "features": {"any": ["derive", "full"]}, "variants": {"Default": [], "Type": [{"token": "RArrow"}, {"box": {"syn": "Type"}}]}}, {"ident": "Signature", "features": {"any": ["full"]}, "fields": {"constness": {"option": {"token": "Const"}}, "asyncness": {"option": {"token": "Async"}}, "unsafety": {"option": {"token": "Unsafe"}}, "abi": {"option": {"syn": "<PERSON><PERSON>"}}, "fn_token": {"token": "Fn"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "paren_token": {"group": "<PERSON><PERSON>"}, "inputs": {"punctuated": {"element": {"syn": "FnArg"}, "punct": "Comma"}}, "variadic": {"option": {"syn": "Variadic"}}, "output": {"syn": "ReturnType"}}}, {"ident": "StaticMutability", "features": {"any": ["full"]}, "variants": {"Mut": [{"token": "<PERSON><PERSON>"}], "None": []}, "exhaustive": false}, {"ident": "Stmt", "features": {"any": ["full"]}, "variants": {"Local": [{"syn": "Local"}], "Item": [{"syn": "<PERSON><PERSON>"}], "Expr": [{"syn": "Expr"}, {"option": {"token": "Semi"}}], "Macro": [{"syn": "StmtMacro"}]}}, {"ident": "StmtMacro", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "mac": {"syn": "Macro"}, "semi_token": {"option": {"token": "Semi"}}}}, {"ident": "TraitBound", "features": {"any": ["derive", "full"]}, "fields": {"paren_token": {"option": {"group": "<PERSON><PERSON>"}}, "modifier": {"syn": "TraitBoundModifier"}, "lifetimes": {"option": {"syn": "BoundLifetimes"}}, "path": {"syn": "Path"}}}, {"ident": "TraitBoundModifier", "features": {"any": ["derive", "full"]}, "variants": {"None": [], "Maybe": [{"token": "Question"}]}}, {"ident": "TraitItem", "features": {"any": ["full"]}, "variants": {"Const": [{"syn": "TraitItemConst"}], "Fn": [{"syn": "TraitItemFn"}], "Type": [{"syn": "TraitItemType"}], "Macro": [{"syn": "TraitItemMacro"}], "Verbatim": [{"proc_macro2": "TokenStream"}]}, "exhaustive": false}, {"ident": "TraitItemConst", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "const_token": {"token": "Const"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "colon_token": {"token": "Colon"}, "ty": {"syn": "Type"}, "default": {"option": {"tuple": [{"token": "Eq"}, {"syn": "Expr"}]}}, "semi_token": {"token": "Semi"}}}, {"ident": "TraitItemFn", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "sig": {"syn": "Signature"}, "default": {"option": {"syn": "Block"}}, "semi_token": {"option": {"token": "Semi"}}}}, {"ident": "TraitItemMacro", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "mac": {"syn": "Macro"}, "semi_token": {"option": {"token": "Semi"}}}}, {"ident": "TraitItemType", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "type_token": {"token": "Type"}, "ident": {"proc_macro2": "Ident"}, "generics": {"syn": "Generics"}, "colon_token": {"option": {"token": "Colon"}}, "bounds": {"punctuated": {"element": {"syn": "TypeParamBound"}, "punct": "Plus"}}, "default": {"option": {"tuple": [{"token": "Eq"}, {"syn": "Type"}]}}, "semi_token": {"token": "Semi"}}}, {"ident": "Type", "features": {"any": ["derive", "full"]}, "variants": {"Array": [{"syn": "TypeArray"}], "BareFn": [{"syn": "TypeBareFn"}], "Group": [{"syn": "TypeGroup"}], "ImplTrait": [{"syn": "TypeImplTrait"}], "Infer": [{"syn": "TypeInfer"}], "Macro": [{"syn": "TypeMacro"}], "Never": [{"syn": "TypeNever"}], "Paren": [{"syn": "TypeParen"}], "Path": [{"syn": "TypePath"}], "Ptr": [{"syn": "TypePtr"}], "Reference": [{"syn": "TypeReference"}], "Slice": [{"syn": "TypeSlice"}], "TraitObject": [{"syn": "TypeTraitObject"}], "Tuple": [{"syn": "TypeTuple"}], "Verbatim": [{"proc_macro2": "TokenStream"}]}, "exhaustive": false}, {"ident": "TypeArray", "features": {"any": ["derive", "full"]}, "fields": {"bracket_token": {"group": "Bracket"}, "elem": {"box": {"syn": "Type"}}, "semi_token": {"token": "Semi"}, "len": {"syn": "Expr"}}}, {"ident": "TypeBareFn", "features": {"any": ["derive", "full"]}, "fields": {"lifetimes": {"option": {"syn": "BoundLifetimes"}}, "unsafety": {"option": {"token": "Unsafe"}}, "abi": {"option": {"syn": "<PERSON><PERSON>"}}, "fn_token": {"token": "Fn"}, "paren_token": {"group": "<PERSON><PERSON>"}, "inputs": {"punctuated": {"element": {"syn": "BareFnArg"}, "punct": "Comma"}}, "variadic": {"option": {"syn": "BareVariadic"}}, "output": {"syn": "ReturnType"}}}, {"ident": "TypeGroup", "features": {"any": ["derive", "full"]}, "fields": {"group_token": {"group": "Group"}, "elem": {"box": {"syn": "Type"}}}}, {"ident": "TypeImplTrait", "features": {"any": ["derive", "full"]}, "fields": {"impl_token": {"token": "Impl"}, "bounds": {"punctuated": {"element": {"syn": "TypeParamBound"}, "punct": "Plus"}}}}, {"ident": "TypeInfer", "features": {"any": ["derive", "full"]}, "fields": {"underscore_token": {"token": "Underscore"}}}, {"ident": "TypeMacro", "features": {"any": ["derive", "full"]}, "fields": {"mac": {"syn": "Macro"}}}, {"ident": "TypeNever", "features": {"any": ["derive", "full"]}, "fields": {"bang_token": {"token": "Not"}}}, {"ident": "TypeParam", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "ident": {"proc_macro2": "Ident"}, "colon_token": {"option": {"token": "Colon"}}, "bounds": {"punctuated": {"element": {"syn": "TypeParamBound"}, "punct": "Plus"}}, "eq_token": {"option": {"token": "Eq"}}, "default": {"option": {"syn": "Type"}}}}, {"ident": "TypeParamBound", "features": {"any": ["derive", "full"]}, "variants": {"Trait": [{"syn": "TraitBound"}], "Lifetime": [{"syn": "Lifetime"}], "PreciseCapture": [{"syn": "PreciseCapture"}], "Verbatim": [{"proc_macro2": "TokenStream"}]}, "exhaustive": false}, {"ident": "TypeParen", "features": {"any": ["derive", "full"]}, "fields": {"paren_token": {"group": "<PERSON><PERSON>"}, "elem": {"box": {"syn": "Type"}}}}, {"ident": "TypePath", "features": {"any": ["derive", "full"]}, "fields": {"qself": {"option": {"syn": "QSelf"}}, "path": {"syn": "Path"}}}, {"ident": "TypePtr", "features": {"any": ["derive", "full"]}, "fields": {"star_token": {"token": "Star"}, "const_token": {"option": {"token": "Const"}}, "mutability": {"option": {"token": "<PERSON><PERSON>"}}, "elem": {"box": {"syn": "Type"}}}}, {"ident": "TypeReference", "features": {"any": ["derive", "full"]}, "fields": {"and_token": {"token": "And"}, "lifetime": {"option": {"syn": "Lifetime"}}, "mutability": {"option": {"token": "<PERSON><PERSON>"}}, "elem": {"box": {"syn": "Type"}}}}, {"ident": "TypeSlice", "features": {"any": ["derive", "full"]}, "fields": {"bracket_token": {"group": "Bracket"}, "elem": {"box": {"syn": "Type"}}}}, {"ident": "TypeTraitObject", "features": {"any": ["derive", "full"]}, "fields": {"dyn_token": {"option": {"token": "<PERSON><PERSON>"}}, "bounds": {"punctuated": {"element": {"syn": "TypeParamBound"}, "punct": "Plus"}}}}, {"ident": "TypeTuple", "features": {"any": ["derive", "full"]}, "fields": {"paren_token": {"group": "<PERSON><PERSON>"}, "elems": {"punctuated": {"element": {"syn": "Type"}, "punct": "Comma"}}}}, {"ident": "UnOp", "features": {"any": ["derive", "full"]}, "variants": {"Deref": [{"token": "Star"}], "Not": [{"token": "Not"}], "Neg": [{"token": "Minus"}]}, "exhaustive": false}, {"ident": "UseGlob", "features": {"any": ["full"]}, "fields": {"star_token": {"token": "Star"}}}, {"ident": "UseGroup", "features": {"any": ["full"]}, "fields": {"brace_token": {"group": "Brace"}, "items": {"punctuated": {"element": {"syn": "UseTree"}, "punct": "Comma"}}}}, {"ident": "UseName", "features": {"any": ["full"]}, "fields": {"ident": {"proc_macro2": "Ident"}}}, {"ident": "UsePath", "features": {"any": ["full"]}, "fields": {"ident": {"proc_macro2": "Ident"}, "colon2_token": {"token": "PathSep"}, "tree": {"box": {"syn": "UseTree"}}}}, {"ident": "UseRename", "features": {"any": ["full"]}, "fields": {"ident": {"proc_macro2": "Ident"}, "as_token": {"token": "As"}, "rename": {"proc_macro2": "Ident"}}}, {"ident": "UseTree", "features": {"any": ["full"]}, "variants": {"Path": [{"syn": "UsePath"}], "Name": [{"syn": "UseName"}], "Rename": [{"syn": "UseRename"}], "Glob": [{"syn": "UseGlob"}], "Group": [{"syn": "UseGroup"}]}}, {"ident": "Variadic", "features": {"any": ["full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "pat": {"option": {"tuple": [{"box": {"syn": "<PERSON>"}}, {"token": "Colon"}]}}, "dots": {"token": "DotDotDot"}, "comma": {"option": {"token": "Comma"}}}}, {"ident": "<PERSON><PERSON><PERSON>", "features": {"any": ["derive", "full"]}, "fields": {"attrs": {"vec": {"syn": "Attribute"}}, "ident": {"proc_macro2": "Ident"}, "fields": {"syn": "Fields"}, "discriminant": {"option": {"tuple": [{"token": "Eq"}, {"syn": "Expr"}]}}}}, {"ident": "VisRestricted", "features": {"any": ["derive", "full"]}, "fields": {"pub_token": {"token": "Pub"}, "paren_token": {"group": "<PERSON><PERSON>"}, "in_token": {"option": {"token": "In"}}, "path": {"box": {"syn": "Path"}}}}, {"ident": "Visibility", "features": {"any": ["derive", "full"]}, "variants": {"Public": [{"token": "Pub"}], "Restricted": [{"syn": "VisRestricted"}], "Inherited": []}}, {"ident": "<PERSON><PERSON><PERSON><PERSON>", "features": {"any": ["derive", "full"]}, "fields": {"where_token": {"token": "Where"}, "predicates": {"punctuated": {"element": {"syn": "WherePredicate"}, "punct": "Comma"}}}}, {"ident": "WherePredicate", "features": {"any": ["derive", "full"]}, "variants": {"Lifetime": [{"syn": "PredicateLifetime"}], "Type": [{"syn": "PredicateType"}]}, "exhaustive": false}], "tokens": {"Abstract": "abstract", "And": "&", "AndAnd": "&&", "AndEq": "&=", "As": "as", "Async": "async", "At": "@", "Auto": "auto", "Await": "await", "Become": "become", "Box": "box", "Break": "break", "Caret": "^", "CaretEq": "^=", "Colon": ":", "Comma": ",", "Const": "const", "Continue": "continue", "Crate": "crate", "Default": "default", "Do": "do", "Dollar": "$", "Dot": ".", "DotDot": "..", "DotDotDot": "...", "DotDotEq": "..=", "Dyn": "dyn", "Else": "else", "Enum": "enum", "Eq": "=", "EqEq": "==", "Extern": "extern", "FatArrow": "=>", "Final": "final", "Fn": "fn", "For": "for", "Ge": ">=", "Gt": ">", "If": "if", "Impl": "impl", "In": "in", "LArrow": "<-", "Le": "<=", "Let": "let", "Loop": "loop", "Lt": "<", "Macro": "macro", "Match": "match", "Minus": "-", "MinusEq": "-=", "Mod": "mod", "Move": "move", "Mut": "mut", "Ne": "!=", "Not": "!", "Or": "|", "OrEq": "|=", "OrOr": "||", "Override": "override", "PathSep": "::", "Percent": "%", "PercentEq": "%=", "Plus": "+", "PlusEq": "+=", "Pound": "#", "Priv": "priv", "Pub": "pub", "Question": "?", "RArrow": "->", "Raw": "raw", "Ref": "ref", "Return": "return", "SelfType": "Self", "SelfValue": "self", "Semi": ";", "Shl": "<<", "ShlEq": "<<=", "Shr": ">>", "ShrEq": ">>=", "Slash": "/", "SlashEq": "/=", "Star": "*", "StarEq": "*=", "Static": "static", "Struct": "struct", "Super": "super", "Tilde": "~", "Trait": "trait", "Try": "try", "Type": "type", "Typeof": "typeof", "Underscore": "_", "Union": "union", "Unsafe": "unsafe", "Unsized": "unsized", "Use": "use", "Virtual": "virtual", "Where": "where", "While": "while", "Yield": "yield"}}