// This file is @generated by syn-internal-codegen.
// It is not intended for manual editing.

#![allow(unreachable_code, unused_variables)]
#![allow(
    clippy::match_wildcard_for_single_variants,
    clippy::needless_match,
    clippy::needless_pass_by_ref_mut,
)]
#[cfg(feature = "full")]
macro_rules! full {
    ($e:expr) => {
        $e
    };
}
#[cfg(all(feature = "derive", not(feature = "full")))]
macro_rules! full {
    ($e:expr) => {
        unreachable!()
    };
}
/// Syntax tree traversal to transform the nodes of an owned syntax tree.
///
/// See the [module documentation] for details.
///
/// [module documentation]: self
pub trait Fold {
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_abi(&mut self, i: crate::Abi) -> crate::Abi {
        fold_abi(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_angle_bracketed_generic_arguments(
        &mut self,
        i: crate::AngleBracketedGenericArguments,
    ) -> crate::AngleBracketedGenericArguments {
        fold_angle_bracketed_generic_arguments(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_arm(&mut self, i: crate::Arm) -> crate::Arm {
        fold_arm(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_assoc_const(&mut self, i: crate::AssocConst) -> crate::AssocConst {
        fold_assoc_const(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_assoc_type(&mut self, i: crate::AssocType) -> crate::AssocType {
        fold_assoc_type(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_attr_style(&mut self, i: crate::AttrStyle) -> crate::AttrStyle {
        fold_attr_style(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_attribute(&mut self, i: crate::Attribute) -> crate::Attribute {
        fold_attribute(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_attributes(&mut self, i: Vec<crate::Attribute>) -> Vec<crate::Attribute> {
        fold_vec(i, self, Self::fold_attribute)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_bare_fn_arg(&mut self, i: crate::BareFnArg) -> crate::BareFnArg {
        fold_bare_fn_arg(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_bare_variadic(&mut self, i: crate::BareVariadic) -> crate::BareVariadic {
        fold_bare_variadic(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_bin_op(&mut self, i: crate::BinOp) -> crate::BinOp {
        fold_bin_op(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_block(&mut self, i: crate::Block) -> crate::Block {
        fold_block(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_bound_lifetimes(
        &mut self,
        i: crate::BoundLifetimes,
    ) -> crate::BoundLifetimes {
        fold_bound_lifetimes(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_captured_param(&mut self, i: crate::CapturedParam) -> crate::CapturedParam {
        fold_captured_param(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_const_param(&mut self, i: crate::ConstParam) -> crate::ConstParam {
        fold_const_param(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_constraint(&mut self, i: crate::Constraint) -> crate::Constraint {
        fold_constraint(self, i)
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn fold_data(&mut self, i: crate::Data) -> crate::Data {
        fold_data(self, i)
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn fold_data_enum(&mut self, i: crate::DataEnum) -> crate::DataEnum {
        fold_data_enum(self, i)
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn fold_data_struct(&mut self, i: crate::DataStruct) -> crate::DataStruct {
        fold_data_struct(self, i)
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn fold_data_union(&mut self, i: crate::DataUnion) -> crate::DataUnion {
        fold_data_union(self, i)
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn fold_derive_input(&mut self, i: crate::DeriveInput) -> crate::DeriveInput {
        fold_derive_input(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr(&mut self, i: crate::Expr) -> crate::Expr {
        fold_expr(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_array(&mut self, i: crate::ExprArray) -> crate::ExprArray {
        fold_expr_array(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_assign(&mut self, i: crate::ExprAssign) -> crate::ExprAssign {
        fold_expr_assign(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_async(&mut self, i: crate::ExprAsync) -> crate::ExprAsync {
        fold_expr_async(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_await(&mut self, i: crate::ExprAwait) -> crate::ExprAwait {
        fold_expr_await(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_binary(&mut self, i: crate::ExprBinary) -> crate::ExprBinary {
        fold_expr_binary(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_block(&mut self, i: crate::ExprBlock) -> crate::ExprBlock {
        fold_expr_block(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_break(&mut self, i: crate::ExprBreak) -> crate::ExprBreak {
        fold_expr_break(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_call(&mut self, i: crate::ExprCall) -> crate::ExprCall {
        fold_expr_call(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_cast(&mut self, i: crate::ExprCast) -> crate::ExprCast {
        fold_expr_cast(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_closure(&mut self, i: crate::ExprClosure) -> crate::ExprClosure {
        fold_expr_closure(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_const(&mut self, i: crate::ExprConst) -> crate::ExprConst {
        fold_expr_const(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_continue(&mut self, i: crate::ExprContinue) -> crate::ExprContinue {
        fold_expr_continue(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_field(&mut self, i: crate::ExprField) -> crate::ExprField {
        fold_expr_field(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_for_loop(&mut self, i: crate::ExprForLoop) -> crate::ExprForLoop {
        fold_expr_for_loop(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_group(&mut self, i: crate::ExprGroup) -> crate::ExprGroup {
        fold_expr_group(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_if(&mut self, i: crate::ExprIf) -> crate::ExprIf {
        fold_expr_if(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_index(&mut self, i: crate::ExprIndex) -> crate::ExprIndex {
        fold_expr_index(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_infer(&mut self, i: crate::ExprInfer) -> crate::ExprInfer {
        fold_expr_infer(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_let(&mut self, i: crate::ExprLet) -> crate::ExprLet {
        fold_expr_let(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_lit(&mut self, i: crate::ExprLit) -> crate::ExprLit {
        fold_expr_lit(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_loop(&mut self, i: crate::ExprLoop) -> crate::ExprLoop {
        fold_expr_loop(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_macro(&mut self, i: crate::ExprMacro) -> crate::ExprMacro {
        fold_expr_macro(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_match(&mut self, i: crate::ExprMatch) -> crate::ExprMatch {
        fold_expr_match(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_method_call(
        &mut self,
        i: crate::ExprMethodCall,
    ) -> crate::ExprMethodCall {
        fold_expr_method_call(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_paren(&mut self, i: crate::ExprParen) -> crate::ExprParen {
        fold_expr_paren(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_path(&mut self, i: crate::ExprPath) -> crate::ExprPath {
        fold_expr_path(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_range(&mut self, i: crate::ExprRange) -> crate::ExprRange {
        fold_expr_range(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_raw_addr(&mut self, i: crate::ExprRawAddr) -> crate::ExprRawAddr {
        fold_expr_raw_addr(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_reference(&mut self, i: crate::ExprReference) -> crate::ExprReference {
        fold_expr_reference(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_repeat(&mut self, i: crate::ExprRepeat) -> crate::ExprRepeat {
        fold_expr_repeat(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_return(&mut self, i: crate::ExprReturn) -> crate::ExprReturn {
        fold_expr_return(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_struct(&mut self, i: crate::ExprStruct) -> crate::ExprStruct {
        fold_expr_struct(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_try(&mut self, i: crate::ExprTry) -> crate::ExprTry {
        fold_expr_try(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_try_block(&mut self, i: crate::ExprTryBlock) -> crate::ExprTryBlock {
        fold_expr_try_block(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_tuple(&mut self, i: crate::ExprTuple) -> crate::ExprTuple {
        fold_expr_tuple(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_expr_unary(&mut self, i: crate::ExprUnary) -> crate::ExprUnary {
        fold_expr_unary(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_unsafe(&mut self, i: crate::ExprUnsafe) -> crate::ExprUnsafe {
        fold_expr_unsafe(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_while(&mut self, i: crate::ExprWhile) -> crate::ExprWhile {
        fold_expr_while(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_expr_yield(&mut self, i: crate::ExprYield) -> crate::ExprYield {
        fold_expr_yield(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_field(&mut self, i: crate::Field) -> crate::Field {
        fold_field(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_field_mutability(
        &mut self,
        i: crate::FieldMutability,
    ) -> crate::FieldMutability {
        fold_field_mutability(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_field_pat(&mut self, i: crate::FieldPat) -> crate::FieldPat {
        fold_field_pat(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_field_value(&mut self, i: crate::FieldValue) -> crate::FieldValue {
        fold_field_value(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_fields(&mut self, i: crate::Fields) -> crate::Fields {
        fold_fields(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_fields_named(&mut self, i: crate::FieldsNamed) -> crate::FieldsNamed {
        fold_fields_named(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_fields_unnamed(&mut self, i: crate::FieldsUnnamed) -> crate::FieldsUnnamed {
        fold_fields_unnamed(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_file(&mut self, i: crate::File) -> crate::File {
        fold_file(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_fn_arg(&mut self, i: crate::FnArg) -> crate::FnArg {
        fold_fn_arg(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_foreign_item(&mut self, i: crate::ForeignItem) -> crate::ForeignItem {
        fold_foreign_item(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_foreign_item_fn(&mut self, i: crate::ForeignItemFn) -> crate::ForeignItemFn {
        fold_foreign_item_fn(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_foreign_item_macro(
        &mut self,
        i: crate::ForeignItemMacro,
    ) -> crate::ForeignItemMacro {
        fold_foreign_item_macro(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_foreign_item_static(
        &mut self,
        i: crate::ForeignItemStatic,
    ) -> crate::ForeignItemStatic {
        fold_foreign_item_static(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_foreign_item_type(
        &mut self,
        i: crate::ForeignItemType,
    ) -> crate::ForeignItemType {
        fold_foreign_item_type(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_generic_argument(
        &mut self,
        i: crate::GenericArgument,
    ) -> crate::GenericArgument {
        fold_generic_argument(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_generic_param(&mut self, i: crate::GenericParam) -> crate::GenericParam {
        fold_generic_param(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_generics(&mut self, i: crate::Generics) -> crate::Generics {
        fold_generics(self, i)
    }
    fn fold_ident(&mut self, i: proc_macro2::Ident) -> proc_macro2::Ident {
        fold_ident(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_impl_item(&mut self, i: crate::ImplItem) -> crate::ImplItem {
        fold_impl_item(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_impl_item_const(&mut self, i: crate::ImplItemConst) -> crate::ImplItemConst {
        fold_impl_item_const(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_impl_item_fn(&mut self, i: crate::ImplItemFn) -> crate::ImplItemFn {
        fold_impl_item_fn(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_impl_item_macro(&mut self, i: crate::ImplItemMacro) -> crate::ImplItemMacro {
        fold_impl_item_macro(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_impl_item_type(&mut self, i: crate::ImplItemType) -> crate::ImplItemType {
        fold_impl_item_type(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_impl_restriction(
        &mut self,
        i: crate::ImplRestriction,
    ) -> crate::ImplRestriction {
        fold_impl_restriction(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_index(&mut self, i: crate::Index) -> crate::Index {
        fold_index(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item(&mut self, i: crate::Item) -> crate::Item {
        fold_item(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_const(&mut self, i: crate::ItemConst) -> crate::ItemConst {
        fold_item_const(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_enum(&mut self, i: crate::ItemEnum) -> crate::ItemEnum {
        fold_item_enum(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_extern_crate(
        &mut self,
        i: crate::ItemExternCrate,
    ) -> crate::ItemExternCrate {
        fold_item_extern_crate(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_fn(&mut self, i: crate::ItemFn) -> crate::ItemFn {
        fold_item_fn(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_foreign_mod(
        &mut self,
        i: crate::ItemForeignMod,
    ) -> crate::ItemForeignMod {
        fold_item_foreign_mod(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_impl(&mut self, i: crate::ItemImpl) -> crate::ItemImpl {
        fold_item_impl(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_macro(&mut self, i: crate::ItemMacro) -> crate::ItemMacro {
        fold_item_macro(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_mod(&mut self, i: crate::ItemMod) -> crate::ItemMod {
        fold_item_mod(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_static(&mut self, i: crate::ItemStatic) -> crate::ItemStatic {
        fold_item_static(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_struct(&mut self, i: crate::ItemStruct) -> crate::ItemStruct {
        fold_item_struct(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_trait(&mut self, i: crate::ItemTrait) -> crate::ItemTrait {
        fold_item_trait(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_trait_alias(
        &mut self,
        i: crate::ItemTraitAlias,
    ) -> crate::ItemTraitAlias {
        fold_item_trait_alias(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_type(&mut self, i: crate::ItemType) -> crate::ItemType {
        fold_item_type(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_union(&mut self, i: crate::ItemUnion) -> crate::ItemUnion {
        fold_item_union(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_item_use(&mut self, i: crate::ItemUse) -> crate::ItemUse {
        fold_item_use(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_label(&mut self, i: crate::Label) -> crate::Label {
        fold_label(self, i)
    }
    fn fold_lifetime(&mut self, i: crate::Lifetime) -> crate::Lifetime {
        fold_lifetime(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_lifetime_param(&mut self, i: crate::LifetimeParam) -> crate::LifetimeParam {
        fold_lifetime_param(self, i)
    }
    fn fold_lit(&mut self, i: crate::Lit) -> crate::Lit {
        fold_lit(self, i)
    }
    fn fold_lit_bool(&mut self, i: crate::LitBool) -> crate::LitBool {
        fold_lit_bool(self, i)
    }
    fn fold_lit_byte(&mut self, i: crate::LitByte) -> crate::LitByte {
        fold_lit_byte(self, i)
    }
    fn fold_lit_byte_str(&mut self, i: crate::LitByteStr) -> crate::LitByteStr {
        fold_lit_byte_str(self, i)
    }
    fn fold_lit_cstr(&mut self, i: crate::LitCStr) -> crate::LitCStr {
        fold_lit_cstr(self, i)
    }
    fn fold_lit_char(&mut self, i: crate::LitChar) -> crate::LitChar {
        fold_lit_char(self, i)
    }
    fn fold_lit_float(&mut self, i: crate::LitFloat) -> crate::LitFloat {
        fold_lit_float(self, i)
    }
    fn fold_lit_int(&mut self, i: crate::LitInt) -> crate::LitInt {
        fold_lit_int(self, i)
    }
    fn fold_lit_str(&mut self, i: crate::LitStr) -> crate::LitStr {
        fold_lit_str(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_local(&mut self, i: crate::Local) -> crate::Local {
        fold_local(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_local_init(&mut self, i: crate::LocalInit) -> crate::LocalInit {
        fold_local_init(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_macro(&mut self, i: crate::Macro) -> crate::Macro {
        fold_macro(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_macro_delimiter(
        &mut self,
        i: crate::MacroDelimiter,
    ) -> crate::MacroDelimiter {
        fold_macro_delimiter(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_member(&mut self, i: crate::Member) -> crate::Member {
        fold_member(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_meta(&mut self, i: crate::Meta) -> crate::Meta {
        fold_meta(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_meta_list(&mut self, i: crate::MetaList) -> crate::MetaList {
        fold_meta_list(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_meta_name_value(&mut self, i: crate::MetaNameValue) -> crate::MetaNameValue {
        fold_meta_name_value(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_parenthesized_generic_arguments(
        &mut self,
        i: crate::ParenthesizedGenericArguments,
    ) -> crate::ParenthesizedGenericArguments {
        fold_parenthesized_generic_arguments(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat(&mut self, i: crate::Pat) -> crate::Pat {
        fold_pat(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_ident(&mut self, i: crate::PatIdent) -> crate::PatIdent {
        fold_pat_ident(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_or(&mut self, i: crate::PatOr) -> crate::PatOr {
        fold_pat_or(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_paren(&mut self, i: crate::PatParen) -> crate::PatParen {
        fold_pat_paren(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_reference(&mut self, i: crate::PatReference) -> crate::PatReference {
        fold_pat_reference(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_rest(&mut self, i: crate::PatRest) -> crate::PatRest {
        fold_pat_rest(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_slice(&mut self, i: crate::PatSlice) -> crate::PatSlice {
        fold_pat_slice(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_struct(&mut self, i: crate::PatStruct) -> crate::PatStruct {
        fold_pat_struct(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_tuple(&mut self, i: crate::PatTuple) -> crate::PatTuple {
        fold_pat_tuple(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_tuple_struct(
        &mut self,
        i: crate::PatTupleStruct,
    ) -> crate::PatTupleStruct {
        fold_pat_tuple_struct(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_type(&mut self, i: crate::PatType) -> crate::PatType {
        fold_pat_type(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pat_wild(&mut self, i: crate::PatWild) -> crate::PatWild {
        fold_pat_wild(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_path(&mut self, i: crate::Path) -> crate::Path {
        fold_path(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_path_arguments(&mut self, i: crate::PathArguments) -> crate::PathArguments {
        fold_path_arguments(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_path_segment(&mut self, i: crate::PathSegment) -> crate::PathSegment {
        fold_path_segment(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_pointer_mutability(
        &mut self,
        i: crate::PointerMutability,
    ) -> crate::PointerMutability {
        fold_pointer_mutability(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_precise_capture(
        &mut self,
        i: crate::PreciseCapture,
    ) -> crate::PreciseCapture {
        fold_precise_capture(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_predicate_lifetime(
        &mut self,
        i: crate::PredicateLifetime,
    ) -> crate::PredicateLifetime {
        fold_predicate_lifetime(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_predicate_type(&mut self, i: crate::PredicateType) -> crate::PredicateType {
        fold_predicate_type(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_qself(&mut self, i: crate::QSelf) -> crate::QSelf {
        fold_qself(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_range_limits(&mut self, i: crate::RangeLimits) -> crate::RangeLimits {
        fold_range_limits(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_receiver(&mut self, i: crate::Receiver) -> crate::Receiver {
        fold_receiver(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_return_type(&mut self, i: crate::ReturnType) -> crate::ReturnType {
        fold_return_type(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_signature(&mut self, i: crate::Signature) -> crate::Signature {
        fold_signature(self, i)
    }
    fn fold_span(&mut self, i: proc_macro2::Span) -> proc_macro2::Span {
        i
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_static_mutability(
        &mut self,
        i: crate::StaticMutability,
    ) -> crate::StaticMutability {
        fold_static_mutability(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_stmt(&mut self, i: crate::Stmt) -> crate::Stmt {
        fold_stmt(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_stmt_macro(&mut self, i: crate::StmtMacro) -> crate::StmtMacro {
        fold_stmt_macro(self, i)
    }
    fn fold_token_stream(
        &mut self,
        i: proc_macro2::TokenStream,
    ) -> proc_macro2::TokenStream {
        i
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_trait_bound(&mut self, i: crate::TraitBound) -> crate::TraitBound {
        fold_trait_bound(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_trait_bound_modifier(
        &mut self,
        i: crate::TraitBoundModifier,
    ) -> crate::TraitBoundModifier {
        fold_trait_bound_modifier(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_trait_item(&mut self, i: crate::TraitItem) -> crate::TraitItem {
        fold_trait_item(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_trait_item_const(
        &mut self,
        i: crate::TraitItemConst,
    ) -> crate::TraitItemConst {
        fold_trait_item_const(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_trait_item_fn(&mut self, i: crate::TraitItemFn) -> crate::TraitItemFn {
        fold_trait_item_fn(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_trait_item_macro(
        &mut self,
        i: crate::TraitItemMacro,
    ) -> crate::TraitItemMacro {
        fold_trait_item_macro(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_trait_item_type(&mut self, i: crate::TraitItemType) -> crate::TraitItemType {
        fold_trait_item_type(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type(&mut self, i: crate::Type) -> crate::Type {
        fold_type(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_array(&mut self, i: crate::TypeArray) -> crate::TypeArray {
        fold_type_array(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_bare_fn(&mut self, i: crate::TypeBareFn) -> crate::TypeBareFn {
        fold_type_bare_fn(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_group(&mut self, i: crate::TypeGroup) -> crate::TypeGroup {
        fold_type_group(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_impl_trait(&mut self, i: crate::TypeImplTrait) -> crate::TypeImplTrait {
        fold_type_impl_trait(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_infer(&mut self, i: crate::TypeInfer) -> crate::TypeInfer {
        fold_type_infer(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_macro(&mut self, i: crate::TypeMacro) -> crate::TypeMacro {
        fold_type_macro(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_never(&mut self, i: crate::TypeNever) -> crate::TypeNever {
        fold_type_never(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_param(&mut self, i: crate::TypeParam) -> crate::TypeParam {
        fold_type_param(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_param_bound(
        &mut self,
        i: crate::TypeParamBound,
    ) -> crate::TypeParamBound {
        fold_type_param_bound(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_paren(&mut self, i: crate::TypeParen) -> crate::TypeParen {
        fold_type_paren(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_path(&mut self, i: crate::TypePath) -> crate::TypePath {
        fold_type_path(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_ptr(&mut self, i: crate::TypePtr) -> crate::TypePtr {
        fold_type_ptr(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_reference(&mut self, i: crate::TypeReference) -> crate::TypeReference {
        fold_type_reference(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_slice(&mut self, i: crate::TypeSlice) -> crate::TypeSlice {
        fold_type_slice(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_trait_object(
        &mut self,
        i: crate::TypeTraitObject,
    ) -> crate::TypeTraitObject {
        fold_type_trait_object(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_type_tuple(&mut self, i: crate::TypeTuple) -> crate::TypeTuple {
        fold_type_tuple(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_un_op(&mut self, i: crate::UnOp) -> crate::UnOp {
        fold_un_op(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_use_glob(&mut self, i: crate::UseGlob) -> crate::UseGlob {
        fold_use_glob(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_use_group(&mut self, i: crate::UseGroup) -> crate::UseGroup {
        fold_use_group(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_use_name(&mut self, i: crate::UseName) -> crate::UseName {
        fold_use_name(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_use_path(&mut self, i: crate::UsePath) -> crate::UsePath {
        fold_use_path(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_use_rename(&mut self, i: crate::UseRename) -> crate::UseRename {
        fold_use_rename(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_use_tree(&mut self, i: crate::UseTree) -> crate::UseTree {
        fold_use_tree(self, i)
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn fold_variadic(&mut self, i: crate::Variadic) -> crate::Variadic {
        fold_variadic(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_variant(&mut self, i: crate::Variant) -> crate::Variant {
        fold_variant(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_vis_restricted(&mut self, i: crate::VisRestricted) -> crate::VisRestricted {
        fold_vis_restricted(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_visibility(&mut self, i: crate::Visibility) -> crate::Visibility {
        fold_visibility(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_where_clause(&mut self, i: crate::WhereClause) -> crate::WhereClause {
        fold_where_clause(self, i)
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn fold_where_predicate(
        &mut self,
        i: crate::WherePredicate,
    ) -> crate::WherePredicate {
        fold_where_predicate(self, i)
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_abi<F>(f: &mut F, node: crate::Abi) -> crate::Abi
where
    F: Fold + ?Sized,
{
    crate::Abi {
        extern_token: node.extern_token,
        name: (node.name).map(|it| f.fold_lit_str(it)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_angle_bracketed_generic_arguments<F>(
    f: &mut F,
    node: crate::AngleBracketedGenericArguments,
) -> crate::AngleBracketedGenericArguments
where
    F: Fold + ?Sized,
{
    crate::AngleBracketedGenericArguments {
        colon2_token: node.colon2_token,
        lt_token: node.lt_token,
        args: crate::punctuated::fold(node.args, f, F::fold_generic_argument),
        gt_token: node.gt_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_arm<F>(f: &mut F, node: crate::Arm) -> crate::Arm
where
    F: Fold + ?Sized,
{
    crate::Arm {
        attrs: f.fold_attributes(node.attrs),
        pat: f.fold_pat(node.pat),
        guard: (node.guard).map(|it| ((it).0, Box::new(f.fold_expr(*(it).1)))),
        fat_arrow_token: node.fat_arrow_token,
        body: Box::new(f.fold_expr(*node.body)),
        comma: node.comma,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_assoc_const<F>(f: &mut F, node: crate::AssocConst) -> crate::AssocConst
where
    F: Fold + ?Sized,
{
    crate::AssocConst {
        ident: f.fold_ident(node.ident),
        generics: (node.generics).map(|it| f.fold_angle_bracketed_generic_arguments(it)),
        eq_token: node.eq_token,
        value: f.fold_expr(node.value),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_assoc_type<F>(f: &mut F, node: crate::AssocType) -> crate::AssocType
where
    F: Fold + ?Sized,
{
    crate::AssocType {
        ident: f.fold_ident(node.ident),
        generics: (node.generics).map(|it| f.fold_angle_bracketed_generic_arguments(it)),
        eq_token: node.eq_token,
        ty: f.fold_type(node.ty),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_attr_style<F>(f: &mut F, node: crate::AttrStyle) -> crate::AttrStyle
where
    F: Fold + ?Sized,
{
    match node {
        crate::AttrStyle::Outer => crate::AttrStyle::Outer,
        crate::AttrStyle::Inner(_binding_0) => crate::AttrStyle::Inner(_binding_0),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_attribute<F>(f: &mut F, node: crate::Attribute) -> crate::Attribute
where
    F: Fold + ?Sized,
{
    crate::Attribute {
        pound_token: node.pound_token,
        style: f.fold_attr_style(node.style),
        bracket_token: node.bracket_token,
        meta: f.fold_meta(node.meta),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_bare_fn_arg<F>(f: &mut F, node: crate::BareFnArg) -> crate::BareFnArg
where
    F: Fold + ?Sized,
{
    crate::BareFnArg {
        attrs: f.fold_attributes(node.attrs),
        name: (node.name).map(|it| (f.fold_ident((it).0), (it).1)),
        ty: f.fold_type(node.ty),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_bare_variadic<F>(f: &mut F, node: crate::BareVariadic) -> crate::BareVariadic
where
    F: Fold + ?Sized,
{
    crate::BareVariadic {
        attrs: f.fold_attributes(node.attrs),
        name: (node.name).map(|it| (f.fold_ident((it).0), (it).1)),
        dots: node.dots,
        comma: node.comma,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_bin_op<F>(f: &mut F, node: crate::BinOp) -> crate::BinOp
where
    F: Fold + ?Sized,
{
    match node {
        crate::BinOp::Add(_binding_0) => crate::BinOp::Add(_binding_0),
        crate::BinOp::Sub(_binding_0) => crate::BinOp::Sub(_binding_0),
        crate::BinOp::Mul(_binding_0) => crate::BinOp::Mul(_binding_0),
        crate::BinOp::Div(_binding_0) => crate::BinOp::Div(_binding_0),
        crate::BinOp::Rem(_binding_0) => crate::BinOp::Rem(_binding_0),
        crate::BinOp::And(_binding_0) => crate::BinOp::And(_binding_0),
        crate::BinOp::Or(_binding_0) => crate::BinOp::Or(_binding_0),
        crate::BinOp::BitXor(_binding_0) => crate::BinOp::BitXor(_binding_0),
        crate::BinOp::BitAnd(_binding_0) => crate::BinOp::BitAnd(_binding_0),
        crate::BinOp::BitOr(_binding_0) => crate::BinOp::BitOr(_binding_0),
        crate::BinOp::Shl(_binding_0) => crate::BinOp::Shl(_binding_0),
        crate::BinOp::Shr(_binding_0) => crate::BinOp::Shr(_binding_0),
        crate::BinOp::Eq(_binding_0) => crate::BinOp::Eq(_binding_0),
        crate::BinOp::Lt(_binding_0) => crate::BinOp::Lt(_binding_0),
        crate::BinOp::Le(_binding_0) => crate::BinOp::Le(_binding_0),
        crate::BinOp::Ne(_binding_0) => crate::BinOp::Ne(_binding_0),
        crate::BinOp::Ge(_binding_0) => crate::BinOp::Ge(_binding_0),
        crate::BinOp::Gt(_binding_0) => crate::BinOp::Gt(_binding_0),
        crate::BinOp::AddAssign(_binding_0) => crate::BinOp::AddAssign(_binding_0),
        crate::BinOp::SubAssign(_binding_0) => crate::BinOp::SubAssign(_binding_0),
        crate::BinOp::MulAssign(_binding_0) => crate::BinOp::MulAssign(_binding_0),
        crate::BinOp::DivAssign(_binding_0) => crate::BinOp::DivAssign(_binding_0),
        crate::BinOp::RemAssign(_binding_0) => crate::BinOp::RemAssign(_binding_0),
        crate::BinOp::BitXorAssign(_binding_0) => crate::BinOp::BitXorAssign(_binding_0),
        crate::BinOp::BitAndAssign(_binding_0) => crate::BinOp::BitAndAssign(_binding_0),
        crate::BinOp::BitOrAssign(_binding_0) => crate::BinOp::BitOrAssign(_binding_0),
        crate::BinOp::ShlAssign(_binding_0) => crate::BinOp::ShlAssign(_binding_0),
        crate::BinOp::ShrAssign(_binding_0) => crate::BinOp::ShrAssign(_binding_0),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_block<F>(f: &mut F, node: crate::Block) -> crate::Block
where
    F: Fold + ?Sized,
{
    crate::Block {
        brace_token: node.brace_token,
        stmts: fold_vec(node.stmts, f, F::fold_stmt),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_bound_lifetimes<F>(
    f: &mut F,
    node: crate::BoundLifetimes,
) -> crate::BoundLifetimes
where
    F: Fold + ?Sized,
{
    crate::BoundLifetimes {
        for_token: node.for_token,
        lt_token: node.lt_token,
        lifetimes: crate::punctuated::fold(node.lifetimes, f, F::fold_generic_param),
        gt_token: node.gt_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_captured_param<F>(
    f: &mut F,
    node: crate::CapturedParam,
) -> crate::CapturedParam
where
    F: Fold + ?Sized,
{
    match node {
        crate::CapturedParam::Lifetime(_binding_0) => {
            crate::CapturedParam::Lifetime(f.fold_lifetime(_binding_0))
        }
        crate::CapturedParam::Ident(_binding_0) => {
            crate::CapturedParam::Ident(f.fold_ident(_binding_0))
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_const_param<F>(f: &mut F, node: crate::ConstParam) -> crate::ConstParam
where
    F: Fold + ?Sized,
{
    crate::ConstParam {
        attrs: f.fold_attributes(node.attrs),
        const_token: node.const_token,
        ident: f.fold_ident(node.ident),
        colon_token: node.colon_token,
        ty: f.fold_type(node.ty),
        eq_token: node.eq_token,
        default: (node.default).map(|it| f.fold_expr(it)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_constraint<F>(f: &mut F, node: crate::Constraint) -> crate::Constraint
where
    F: Fold + ?Sized,
{
    crate::Constraint {
        ident: f.fold_ident(node.ident),
        generics: (node.generics).map(|it| f.fold_angle_bracketed_generic_arguments(it)),
        colon_token: node.colon_token,
        bounds: crate::punctuated::fold(node.bounds, f, F::fold_type_param_bound),
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn fold_data<F>(f: &mut F, node: crate::Data) -> crate::Data
where
    F: Fold + ?Sized,
{
    match node {
        crate::Data::Struct(_binding_0) => {
            crate::Data::Struct(f.fold_data_struct(_binding_0))
        }
        crate::Data::Enum(_binding_0) => crate::Data::Enum(f.fold_data_enum(_binding_0)),
        crate::Data::Union(_binding_0) => {
            crate::Data::Union(f.fold_data_union(_binding_0))
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn fold_data_enum<F>(f: &mut F, node: crate::DataEnum) -> crate::DataEnum
where
    F: Fold + ?Sized,
{
    crate::DataEnum {
        enum_token: node.enum_token,
        brace_token: node.brace_token,
        variants: crate::punctuated::fold(node.variants, f, F::fold_variant),
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn fold_data_struct<F>(f: &mut F, node: crate::DataStruct) -> crate::DataStruct
where
    F: Fold + ?Sized,
{
    crate::DataStruct {
        struct_token: node.struct_token,
        fields: f.fold_fields(node.fields),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn fold_data_union<F>(f: &mut F, node: crate::DataUnion) -> crate::DataUnion
where
    F: Fold + ?Sized,
{
    crate::DataUnion {
        union_token: node.union_token,
        fields: f.fold_fields_named(node.fields),
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn fold_derive_input<F>(f: &mut F, node: crate::DeriveInput) -> crate::DeriveInput
where
    F: Fold + ?Sized,
{
    crate::DeriveInput {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        data: f.fold_data(node.data),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr<F>(f: &mut F, node: crate::Expr) -> crate::Expr
where
    F: Fold + ?Sized,
{
    match node {
        crate::Expr::Array(_binding_0) => {
            crate::Expr::Array(full!(f.fold_expr_array(_binding_0)))
        }
        crate::Expr::Assign(_binding_0) => {
            crate::Expr::Assign(full!(f.fold_expr_assign(_binding_0)))
        }
        crate::Expr::Async(_binding_0) => {
            crate::Expr::Async(full!(f.fold_expr_async(_binding_0)))
        }
        crate::Expr::Await(_binding_0) => {
            crate::Expr::Await(full!(f.fold_expr_await(_binding_0)))
        }
        crate::Expr::Binary(_binding_0) => {
            crate::Expr::Binary(f.fold_expr_binary(_binding_0))
        }
        crate::Expr::Block(_binding_0) => {
            crate::Expr::Block(full!(f.fold_expr_block(_binding_0)))
        }
        crate::Expr::Break(_binding_0) => {
            crate::Expr::Break(full!(f.fold_expr_break(_binding_0)))
        }
        crate::Expr::Call(_binding_0) => crate::Expr::Call(f.fold_expr_call(_binding_0)),
        crate::Expr::Cast(_binding_0) => crate::Expr::Cast(f.fold_expr_cast(_binding_0)),
        crate::Expr::Closure(_binding_0) => {
            crate::Expr::Closure(full!(f.fold_expr_closure(_binding_0)))
        }
        crate::Expr::Const(_binding_0) => {
            crate::Expr::Const(full!(f.fold_expr_const(_binding_0)))
        }
        crate::Expr::Continue(_binding_0) => {
            crate::Expr::Continue(full!(f.fold_expr_continue(_binding_0)))
        }
        crate::Expr::Field(_binding_0) => {
            crate::Expr::Field(f.fold_expr_field(_binding_0))
        }
        crate::Expr::ForLoop(_binding_0) => {
            crate::Expr::ForLoop(full!(f.fold_expr_for_loop(_binding_0)))
        }
        crate::Expr::Group(_binding_0) => {
            crate::Expr::Group(f.fold_expr_group(_binding_0))
        }
        crate::Expr::If(_binding_0) => crate::Expr::If(full!(f.fold_expr_if(_binding_0))),
        crate::Expr::Index(_binding_0) => {
            crate::Expr::Index(f.fold_expr_index(_binding_0))
        }
        crate::Expr::Infer(_binding_0) => {
            crate::Expr::Infer(full!(f.fold_expr_infer(_binding_0)))
        }
        crate::Expr::Let(_binding_0) => {
            crate::Expr::Let(full!(f.fold_expr_let(_binding_0)))
        }
        crate::Expr::Lit(_binding_0) => crate::Expr::Lit(f.fold_expr_lit(_binding_0)),
        crate::Expr::Loop(_binding_0) => {
            crate::Expr::Loop(full!(f.fold_expr_loop(_binding_0)))
        }
        crate::Expr::Macro(_binding_0) => {
            crate::Expr::Macro(f.fold_expr_macro(_binding_0))
        }
        crate::Expr::Match(_binding_0) => {
            crate::Expr::Match(full!(f.fold_expr_match(_binding_0)))
        }
        crate::Expr::MethodCall(_binding_0) => {
            crate::Expr::MethodCall(f.fold_expr_method_call(_binding_0))
        }
        crate::Expr::Paren(_binding_0) => {
            crate::Expr::Paren(f.fold_expr_paren(_binding_0))
        }
        crate::Expr::Path(_binding_0) => crate::Expr::Path(f.fold_expr_path(_binding_0)),
        crate::Expr::Range(_binding_0) => {
            crate::Expr::Range(full!(f.fold_expr_range(_binding_0)))
        }
        crate::Expr::RawAddr(_binding_0) => {
            crate::Expr::RawAddr(full!(f.fold_expr_raw_addr(_binding_0)))
        }
        crate::Expr::Reference(_binding_0) => {
            crate::Expr::Reference(f.fold_expr_reference(_binding_0))
        }
        crate::Expr::Repeat(_binding_0) => {
            crate::Expr::Repeat(full!(f.fold_expr_repeat(_binding_0)))
        }
        crate::Expr::Return(_binding_0) => {
            crate::Expr::Return(full!(f.fold_expr_return(_binding_0)))
        }
        crate::Expr::Struct(_binding_0) => {
            crate::Expr::Struct(f.fold_expr_struct(_binding_0))
        }
        crate::Expr::Try(_binding_0) => {
            crate::Expr::Try(full!(f.fold_expr_try(_binding_0)))
        }
        crate::Expr::TryBlock(_binding_0) => {
            crate::Expr::TryBlock(full!(f.fold_expr_try_block(_binding_0)))
        }
        crate::Expr::Tuple(_binding_0) => {
            crate::Expr::Tuple(f.fold_expr_tuple(_binding_0))
        }
        crate::Expr::Unary(_binding_0) => {
            crate::Expr::Unary(f.fold_expr_unary(_binding_0))
        }
        crate::Expr::Unsafe(_binding_0) => {
            crate::Expr::Unsafe(full!(f.fold_expr_unsafe(_binding_0)))
        }
        crate::Expr::Verbatim(_binding_0) => {
            crate::Expr::Verbatim(f.fold_token_stream(_binding_0))
        }
        crate::Expr::While(_binding_0) => {
            crate::Expr::While(full!(f.fold_expr_while(_binding_0)))
        }
        crate::Expr::Yield(_binding_0) => {
            crate::Expr::Yield(full!(f.fold_expr_yield(_binding_0)))
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_array<F>(f: &mut F, node: crate::ExprArray) -> crate::ExprArray
where
    F: Fold + ?Sized,
{
    crate::ExprArray {
        attrs: f.fold_attributes(node.attrs),
        bracket_token: node.bracket_token,
        elems: crate::punctuated::fold(node.elems, f, F::fold_expr),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_assign<F>(f: &mut F, node: crate::ExprAssign) -> crate::ExprAssign
where
    F: Fold + ?Sized,
{
    crate::ExprAssign {
        attrs: f.fold_attributes(node.attrs),
        left: Box::new(f.fold_expr(*node.left)),
        eq_token: node.eq_token,
        right: Box::new(f.fold_expr(*node.right)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_async<F>(f: &mut F, node: crate::ExprAsync) -> crate::ExprAsync
where
    F: Fold + ?Sized,
{
    crate::ExprAsync {
        attrs: f.fold_attributes(node.attrs),
        async_token: node.async_token,
        capture: node.capture,
        block: f.fold_block(node.block),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_await<F>(f: &mut F, node: crate::ExprAwait) -> crate::ExprAwait
where
    F: Fold + ?Sized,
{
    crate::ExprAwait {
        attrs: f.fold_attributes(node.attrs),
        base: Box::new(f.fold_expr(*node.base)),
        dot_token: node.dot_token,
        await_token: node.await_token,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_binary<F>(f: &mut F, node: crate::ExprBinary) -> crate::ExprBinary
where
    F: Fold + ?Sized,
{
    crate::ExprBinary {
        attrs: f.fold_attributes(node.attrs),
        left: Box::new(f.fold_expr(*node.left)),
        op: f.fold_bin_op(node.op),
        right: Box::new(f.fold_expr(*node.right)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_block<F>(f: &mut F, node: crate::ExprBlock) -> crate::ExprBlock
where
    F: Fold + ?Sized,
{
    crate::ExprBlock {
        attrs: f.fold_attributes(node.attrs),
        label: (node.label).map(|it| f.fold_label(it)),
        block: f.fold_block(node.block),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_break<F>(f: &mut F, node: crate::ExprBreak) -> crate::ExprBreak
where
    F: Fold + ?Sized,
{
    crate::ExprBreak {
        attrs: f.fold_attributes(node.attrs),
        break_token: node.break_token,
        label: (node.label).map(|it| f.fold_lifetime(it)),
        expr: (node.expr).map(|it| Box::new(f.fold_expr(*it))),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_call<F>(f: &mut F, node: crate::ExprCall) -> crate::ExprCall
where
    F: Fold + ?Sized,
{
    crate::ExprCall {
        attrs: f.fold_attributes(node.attrs),
        func: Box::new(f.fold_expr(*node.func)),
        paren_token: node.paren_token,
        args: crate::punctuated::fold(node.args, f, F::fold_expr),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_cast<F>(f: &mut F, node: crate::ExprCast) -> crate::ExprCast
where
    F: Fold + ?Sized,
{
    crate::ExprCast {
        attrs: f.fold_attributes(node.attrs),
        expr: Box::new(f.fold_expr(*node.expr)),
        as_token: node.as_token,
        ty: Box::new(f.fold_type(*node.ty)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_closure<F>(f: &mut F, node: crate::ExprClosure) -> crate::ExprClosure
where
    F: Fold + ?Sized,
{
    crate::ExprClosure {
        attrs: f.fold_attributes(node.attrs),
        lifetimes: (node.lifetimes).map(|it| f.fold_bound_lifetimes(it)),
        constness: node.constness,
        movability: node.movability,
        asyncness: node.asyncness,
        capture: node.capture,
        or1_token: node.or1_token,
        inputs: crate::punctuated::fold(node.inputs, f, F::fold_pat),
        or2_token: node.or2_token,
        output: f.fold_return_type(node.output),
        body: Box::new(f.fold_expr(*node.body)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_const<F>(f: &mut F, node: crate::ExprConst) -> crate::ExprConst
where
    F: Fold + ?Sized,
{
    crate::ExprConst {
        attrs: f.fold_attributes(node.attrs),
        const_token: node.const_token,
        block: f.fold_block(node.block),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_continue<F>(f: &mut F, node: crate::ExprContinue) -> crate::ExprContinue
where
    F: Fold + ?Sized,
{
    crate::ExprContinue {
        attrs: f.fold_attributes(node.attrs),
        continue_token: node.continue_token,
        label: (node.label).map(|it| f.fold_lifetime(it)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_field<F>(f: &mut F, node: crate::ExprField) -> crate::ExprField
where
    F: Fold + ?Sized,
{
    crate::ExprField {
        attrs: f.fold_attributes(node.attrs),
        base: Box::new(f.fold_expr(*node.base)),
        dot_token: node.dot_token,
        member: f.fold_member(node.member),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_for_loop<F>(f: &mut F, node: crate::ExprForLoop) -> crate::ExprForLoop
where
    F: Fold + ?Sized,
{
    crate::ExprForLoop {
        attrs: f.fold_attributes(node.attrs),
        label: (node.label).map(|it| f.fold_label(it)),
        for_token: node.for_token,
        pat: Box::new(f.fold_pat(*node.pat)),
        in_token: node.in_token,
        expr: Box::new(f.fold_expr(*node.expr)),
        body: f.fold_block(node.body),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_group<F>(f: &mut F, node: crate::ExprGroup) -> crate::ExprGroup
where
    F: Fold + ?Sized,
{
    crate::ExprGroup {
        attrs: f.fold_attributes(node.attrs),
        group_token: node.group_token,
        expr: Box::new(f.fold_expr(*node.expr)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_if<F>(f: &mut F, node: crate::ExprIf) -> crate::ExprIf
where
    F: Fold + ?Sized,
{
    crate::ExprIf {
        attrs: f.fold_attributes(node.attrs),
        if_token: node.if_token,
        cond: Box::new(f.fold_expr(*node.cond)),
        then_branch: f.fold_block(node.then_branch),
        else_branch: (node.else_branch)
            .map(|it| ((it).0, Box::new(f.fold_expr(*(it).1)))),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_index<F>(f: &mut F, node: crate::ExprIndex) -> crate::ExprIndex
where
    F: Fold + ?Sized,
{
    crate::ExprIndex {
        attrs: f.fold_attributes(node.attrs),
        expr: Box::new(f.fold_expr(*node.expr)),
        bracket_token: node.bracket_token,
        index: Box::new(f.fold_expr(*node.index)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_infer<F>(f: &mut F, node: crate::ExprInfer) -> crate::ExprInfer
where
    F: Fold + ?Sized,
{
    crate::ExprInfer {
        attrs: f.fold_attributes(node.attrs),
        underscore_token: node.underscore_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_let<F>(f: &mut F, node: crate::ExprLet) -> crate::ExprLet
where
    F: Fold + ?Sized,
{
    crate::ExprLet {
        attrs: f.fold_attributes(node.attrs),
        let_token: node.let_token,
        pat: Box::new(f.fold_pat(*node.pat)),
        eq_token: node.eq_token,
        expr: Box::new(f.fold_expr(*node.expr)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_lit<F>(f: &mut F, node: crate::ExprLit) -> crate::ExprLit
where
    F: Fold + ?Sized,
{
    crate::ExprLit {
        attrs: f.fold_attributes(node.attrs),
        lit: f.fold_lit(node.lit),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_loop<F>(f: &mut F, node: crate::ExprLoop) -> crate::ExprLoop
where
    F: Fold + ?Sized,
{
    crate::ExprLoop {
        attrs: f.fold_attributes(node.attrs),
        label: (node.label).map(|it| f.fold_label(it)),
        loop_token: node.loop_token,
        body: f.fold_block(node.body),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_macro<F>(f: &mut F, node: crate::ExprMacro) -> crate::ExprMacro
where
    F: Fold + ?Sized,
{
    crate::ExprMacro {
        attrs: f.fold_attributes(node.attrs),
        mac: f.fold_macro(node.mac),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_match<F>(f: &mut F, node: crate::ExprMatch) -> crate::ExprMatch
where
    F: Fold + ?Sized,
{
    crate::ExprMatch {
        attrs: f.fold_attributes(node.attrs),
        match_token: node.match_token,
        expr: Box::new(f.fold_expr(*node.expr)),
        brace_token: node.brace_token,
        arms: fold_vec(node.arms, f, F::fold_arm),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_method_call<F>(
    f: &mut F,
    node: crate::ExprMethodCall,
) -> crate::ExprMethodCall
where
    F: Fold + ?Sized,
{
    crate::ExprMethodCall {
        attrs: f.fold_attributes(node.attrs),
        receiver: Box::new(f.fold_expr(*node.receiver)),
        dot_token: node.dot_token,
        method: f.fold_ident(node.method),
        turbofish: (node.turbofish)
            .map(|it| f.fold_angle_bracketed_generic_arguments(it)),
        paren_token: node.paren_token,
        args: crate::punctuated::fold(node.args, f, F::fold_expr),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_paren<F>(f: &mut F, node: crate::ExprParen) -> crate::ExprParen
where
    F: Fold + ?Sized,
{
    crate::ExprParen {
        attrs: f.fold_attributes(node.attrs),
        paren_token: node.paren_token,
        expr: Box::new(f.fold_expr(*node.expr)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_path<F>(f: &mut F, node: crate::ExprPath) -> crate::ExprPath
where
    F: Fold + ?Sized,
{
    crate::ExprPath {
        attrs: f.fold_attributes(node.attrs),
        qself: (node.qself).map(|it| f.fold_qself(it)),
        path: f.fold_path(node.path),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_range<F>(f: &mut F, node: crate::ExprRange) -> crate::ExprRange
where
    F: Fold + ?Sized,
{
    crate::ExprRange {
        attrs: f.fold_attributes(node.attrs),
        start: (node.start).map(|it| Box::new(f.fold_expr(*it))),
        limits: f.fold_range_limits(node.limits),
        end: (node.end).map(|it| Box::new(f.fold_expr(*it))),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_raw_addr<F>(f: &mut F, node: crate::ExprRawAddr) -> crate::ExprRawAddr
where
    F: Fold + ?Sized,
{
    crate::ExprRawAddr {
        attrs: f.fold_attributes(node.attrs),
        and_token: node.and_token,
        raw: node.raw,
        mutability: f.fold_pointer_mutability(node.mutability),
        expr: Box::new(f.fold_expr(*node.expr)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_reference<F>(
    f: &mut F,
    node: crate::ExprReference,
) -> crate::ExprReference
where
    F: Fold + ?Sized,
{
    crate::ExprReference {
        attrs: f.fold_attributes(node.attrs),
        and_token: node.and_token,
        mutability: node.mutability,
        expr: Box::new(f.fold_expr(*node.expr)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_repeat<F>(f: &mut F, node: crate::ExprRepeat) -> crate::ExprRepeat
where
    F: Fold + ?Sized,
{
    crate::ExprRepeat {
        attrs: f.fold_attributes(node.attrs),
        bracket_token: node.bracket_token,
        expr: Box::new(f.fold_expr(*node.expr)),
        semi_token: node.semi_token,
        len: Box::new(f.fold_expr(*node.len)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_return<F>(f: &mut F, node: crate::ExprReturn) -> crate::ExprReturn
where
    F: Fold + ?Sized,
{
    crate::ExprReturn {
        attrs: f.fold_attributes(node.attrs),
        return_token: node.return_token,
        expr: (node.expr).map(|it| Box::new(f.fold_expr(*it))),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_struct<F>(f: &mut F, node: crate::ExprStruct) -> crate::ExprStruct
where
    F: Fold + ?Sized,
{
    crate::ExprStruct {
        attrs: f.fold_attributes(node.attrs),
        qself: (node.qself).map(|it| f.fold_qself(it)),
        path: f.fold_path(node.path),
        brace_token: node.brace_token,
        fields: crate::punctuated::fold(node.fields, f, F::fold_field_value),
        dot2_token: node.dot2_token,
        rest: (node.rest).map(|it| Box::new(f.fold_expr(*it))),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_try<F>(f: &mut F, node: crate::ExprTry) -> crate::ExprTry
where
    F: Fold + ?Sized,
{
    crate::ExprTry {
        attrs: f.fold_attributes(node.attrs),
        expr: Box::new(f.fold_expr(*node.expr)),
        question_token: node.question_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_try_block<F>(
    f: &mut F,
    node: crate::ExprTryBlock,
) -> crate::ExprTryBlock
where
    F: Fold + ?Sized,
{
    crate::ExprTryBlock {
        attrs: f.fold_attributes(node.attrs),
        try_token: node.try_token,
        block: f.fold_block(node.block),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_tuple<F>(f: &mut F, node: crate::ExprTuple) -> crate::ExprTuple
where
    F: Fold + ?Sized,
{
    crate::ExprTuple {
        attrs: f.fold_attributes(node.attrs),
        paren_token: node.paren_token,
        elems: crate::punctuated::fold(node.elems, f, F::fold_expr),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_expr_unary<F>(f: &mut F, node: crate::ExprUnary) -> crate::ExprUnary
where
    F: Fold + ?Sized,
{
    crate::ExprUnary {
        attrs: f.fold_attributes(node.attrs),
        op: f.fold_un_op(node.op),
        expr: Box::new(f.fold_expr(*node.expr)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_unsafe<F>(f: &mut F, node: crate::ExprUnsafe) -> crate::ExprUnsafe
where
    F: Fold + ?Sized,
{
    crate::ExprUnsafe {
        attrs: f.fold_attributes(node.attrs),
        unsafe_token: node.unsafe_token,
        block: f.fold_block(node.block),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_while<F>(f: &mut F, node: crate::ExprWhile) -> crate::ExprWhile
where
    F: Fold + ?Sized,
{
    crate::ExprWhile {
        attrs: f.fold_attributes(node.attrs),
        label: (node.label).map(|it| f.fold_label(it)),
        while_token: node.while_token,
        cond: Box::new(f.fold_expr(*node.cond)),
        body: f.fold_block(node.body),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_expr_yield<F>(f: &mut F, node: crate::ExprYield) -> crate::ExprYield
where
    F: Fold + ?Sized,
{
    crate::ExprYield {
        attrs: f.fold_attributes(node.attrs),
        yield_token: node.yield_token,
        expr: (node.expr).map(|it| Box::new(f.fold_expr(*it))),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_field<F>(f: &mut F, node: crate::Field) -> crate::Field
where
    F: Fold + ?Sized,
{
    crate::Field {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        mutability: f.fold_field_mutability(node.mutability),
        ident: (node.ident).map(|it| f.fold_ident(it)),
        colon_token: node.colon_token,
        ty: f.fold_type(node.ty),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_field_mutability<F>(
    f: &mut F,
    node: crate::FieldMutability,
) -> crate::FieldMutability
where
    F: Fold + ?Sized,
{
    match node {
        crate::FieldMutability::None => crate::FieldMutability::None,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_field_pat<F>(f: &mut F, node: crate::FieldPat) -> crate::FieldPat
where
    F: Fold + ?Sized,
{
    crate::FieldPat {
        attrs: f.fold_attributes(node.attrs),
        member: f.fold_member(node.member),
        colon_token: node.colon_token,
        pat: Box::new(f.fold_pat(*node.pat)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_field_value<F>(f: &mut F, node: crate::FieldValue) -> crate::FieldValue
where
    F: Fold + ?Sized,
{
    crate::FieldValue {
        attrs: f.fold_attributes(node.attrs),
        member: f.fold_member(node.member),
        colon_token: node.colon_token,
        expr: f.fold_expr(node.expr),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_fields<F>(f: &mut F, node: crate::Fields) -> crate::Fields
where
    F: Fold + ?Sized,
{
    match node {
        crate::Fields::Named(_binding_0) => {
            crate::Fields::Named(f.fold_fields_named(_binding_0))
        }
        crate::Fields::Unnamed(_binding_0) => {
            crate::Fields::Unnamed(f.fold_fields_unnamed(_binding_0))
        }
        crate::Fields::Unit => crate::Fields::Unit,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_fields_named<F>(f: &mut F, node: crate::FieldsNamed) -> crate::FieldsNamed
where
    F: Fold + ?Sized,
{
    crate::FieldsNamed {
        brace_token: node.brace_token,
        named: crate::punctuated::fold(node.named, f, F::fold_field),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_fields_unnamed<F>(
    f: &mut F,
    node: crate::FieldsUnnamed,
) -> crate::FieldsUnnamed
where
    F: Fold + ?Sized,
{
    crate::FieldsUnnamed {
        paren_token: node.paren_token,
        unnamed: crate::punctuated::fold(node.unnamed, f, F::fold_field),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_file<F>(f: &mut F, node: crate::File) -> crate::File
where
    F: Fold + ?Sized,
{
    crate::File {
        shebang: node.shebang,
        attrs: f.fold_attributes(node.attrs),
        items: fold_vec(node.items, f, F::fold_item),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_fn_arg<F>(f: &mut F, node: crate::FnArg) -> crate::FnArg
where
    F: Fold + ?Sized,
{
    match node {
        crate::FnArg::Receiver(_binding_0) => {
            crate::FnArg::Receiver(f.fold_receiver(_binding_0))
        }
        crate::FnArg::Typed(_binding_0) => {
            crate::FnArg::Typed(f.fold_pat_type(_binding_0))
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_foreign_item<F>(f: &mut F, node: crate::ForeignItem) -> crate::ForeignItem
where
    F: Fold + ?Sized,
{
    match node {
        crate::ForeignItem::Fn(_binding_0) => {
            crate::ForeignItem::Fn(f.fold_foreign_item_fn(_binding_0))
        }
        crate::ForeignItem::Static(_binding_0) => {
            crate::ForeignItem::Static(f.fold_foreign_item_static(_binding_0))
        }
        crate::ForeignItem::Type(_binding_0) => {
            crate::ForeignItem::Type(f.fold_foreign_item_type(_binding_0))
        }
        crate::ForeignItem::Macro(_binding_0) => {
            crate::ForeignItem::Macro(f.fold_foreign_item_macro(_binding_0))
        }
        crate::ForeignItem::Verbatim(_binding_0) => {
            crate::ForeignItem::Verbatim(f.fold_token_stream(_binding_0))
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_foreign_item_fn<F>(
    f: &mut F,
    node: crate::ForeignItemFn,
) -> crate::ForeignItemFn
where
    F: Fold + ?Sized,
{
    crate::ForeignItemFn {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        sig: f.fold_signature(node.sig),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_foreign_item_macro<F>(
    f: &mut F,
    node: crate::ForeignItemMacro,
) -> crate::ForeignItemMacro
where
    F: Fold + ?Sized,
{
    crate::ForeignItemMacro {
        attrs: f.fold_attributes(node.attrs),
        mac: f.fold_macro(node.mac),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_foreign_item_static<F>(
    f: &mut F,
    node: crate::ForeignItemStatic,
) -> crate::ForeignItemStatic
where
    F: Fold + ?Sized,
{
    crate::ForeignItemStatic {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        static_token: node.static_token,
        mutability: f.fold_static_mutability(node.mutability),
        ident: f.fold_ident(node.ident),
        colon_token: node.colon_token,
        ty: Box::new(f.fold_type(*node.ty)),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_foreign_item_type<F>(
    f: &mut F,
    node: crate::ForeignItemType,
) -> crate::ForeignItemType
where
    F: Fold + ?Sized,
{
    crate::ForeignItemType {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        type_token: node.type_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        semi_token: node.semi_token,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_generic_argument<F>(
    f: &mut F,
    node: crate::GenericArgument,
) -> crate::GenericArgument
where
    F: Fold + ?Sized,
{
    match node {
        crate::GenericArgument::Lifetime(_binding_0) => {
            crate::GenericArgument::Lifetime(f.fold_lifetime(_binding_0))
        }
        crate::GenericArgument::Type(_binding_0) => {
            crate::GenericArgument::Type(f.fold_type(_binding_0))
        }
        crate::GenericArgument::Const(_binding_0) => {
            crate::GenericArgument::Const(f.fold_expr(_binding_0))
        }
        crate::GenericArgument::AssocType(_binding_0) => {
            crate::GenericArgument::AssocType(f.fold_assoc_type(_binding_0))
        }
        crate::GenericArgument::AssocConst(_binding_0) => {
            crate::GenericArgument::AssocConst(f.fold_assoc_const(_binding_0))
        }
        crate::GenericArgument::Constraint(_binding_0) => {
            crate::GenericArgument::Constraint(f.fold_constraint(_binding_0))
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_generic_param<F>(f: &mut F, node: crate::GenericParam) -> crate::GenericParam
where
    F: Fold + ?Sized,
{
    match node {
        crate::GenericParam::Lifetime(_binding_0) => {
            crate::GenericParam::Lifetime(f.fold_lifetime_param(_binding_0))
        }
        crate::GenericParam::Type(_binding_0) => {
            crate::GenericParam::Type(f.fold_type_param(_binding_0))
        }
        crate::GenericParam::Const(_binding_0) => {
            crate::GenericParam::Const(f.fold_const_param(_binding_0))
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_generics<F>(f: &mut F, node: crate::Generics) -> crate::Generics
where
    F: Fold + ?Sized,
{
    crate::Generics {
        lt_token: node.lt_token,
        params: crate::punctuated::fold(node.params, f, F::fold_generic_param),
        gt_token: node.gt_token,
        where_clause: (node.where_clause).map(|it| f.fold_where_clause(it)),
    }
}
pub fn fold_ident<F>(f: &mut F, node: proc_macro2::Ident) -> proc_macro2::Ident
where
    F: Fold + ?Sized,
{
    let mut node = node;
    let span = f.fold_span(node.span());
    node.set_span(span);
    node
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_impl_item<F>(f: &mut F, node: crate::ImplItem) -> crate::ImplItem
where
    F: Fold + ?Sized,
{
    match node {
        crate::ImplItem::Const(_binding_0) => {
            crate::ImplItem::Const(f.fold_impl_item_const(_binding_0))
        }
        crate::ImplItem::Fn(_binding_0) => {
            crate::ImplItem::Fn(f.fold_impl_item_fn(_binding_0))
        }
        crate::ImplItem::Type(_binding_0) => {
            crate::ImplItem::Type(f.fold_impl_item_type(_binding_0))
        }
        crate::ImplItem::Macro(_binding_0) => {
            crate::ImplItem::Macro(f.fold_impl_item_macro(_binding_0))
        }
        crate::ImplItem::Verbatim(_binding_0) => {
            crate::ImplItem::Verbatim(f.fold_token_stream(_binding_0))
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_impl_item_const<F>(
    f: &mut F,
    node: crate::ImplItemConst,
) -> crate::ImplItemConst
where
    F: Fold + ?Sized,
{
    crate::ImplItemConst {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        defaultness: node.defaultness,
        const_token: node.const_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        colon_token: node.colon_token,
        ty: f.fold_type(node.ty),
        eq_token: node.eq_token,
        expr: f.fold_expr(node.expr),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_impl_item_fn<F>(f: &mut F, node: crate::ImplItemFn) -> crate::ImplItemFn
where
    F: Fold + ?Sized,
{
    crate::ImplItemFn {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        defaultness: node.defaultness,
        sig: f.fold_signature(node.sig),
        block: f.fold_block(node.block),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_impl_item_macro<F>(
    f: &mut F,
    node: crate::ImplItemMacro,
) -> crate::ImplItemMacro
where
    F: Fold + ?Sized,
{
    crate::ImplItemMacro {
        attrs: f.fold_attributes(node.attrs),
        mac: f.fold_macro(node.mac),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_impl_item_type<F>(
    f: &mut F,
    node: crate::ImplItemType,
) -> crate::ImplItemType
where
    F: Fold + ?Sized,
{
    crate::ImplItemType {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        defaultness: node.defaultness,
        type_token: node.type_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        eq_token: node.eq_token,
        ty: f.fold_type(node.ty),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_impl_restriction<F>(
    f: &mut F,
    node: crate::ImplRestriction,
) -> crate::ImplRestriction
where
    F: Fold + ?Sized,
{
    match node {}
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_index<F>(f: &mut F, node: crate::Index) -> crate::Index
where
    F: Fold + ?Sized,
{
    crate::Index {
        index: node.index,
        span: f.fold_span(node.span),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item<F>(f: &mut F, node: crate::Item) -> crate::Item
where
    F: Fold + ?Sized,
{
    match node {
        crate::Item::Const(_binding_0) => {
            crate::Item::Const(f.fold_item_const(_binding_0))
        }
        crate::Item::Enum(_binding_0) => crate::Item::Enum(f.fold_item_enum(_binding_0)),
        crate::Item::ExternCrate(_binding_0) => {
            crate::Item::ExternCrate(f.fold_item_extern_crate(_binding_0))
        }
        crate::Item::Fn(_binding_0) => crate::Item::Fn(f.fold_item_fn(_binding_0)),
        crate::Item::ForeignMod(_binding_0) => {
            crate::Item::ForeignMod(f.fold_item_foreign_mod(_binding_0))
        }
        crate::Item::Impl(_binding_0) => crate::Item::Impl(f.fold_item_impl(_binding_0)),
        crate::Item::Macro(_binding_0) => {
            crate::Item::Macro(f.fold_item_macro(_binding_0))
        }
        crate::Item::Mod(_binding_0) => crate::Item::Mod(f.fold_item_mod(_binding_0)),
        crate::Item::Static(_binding_0) => {
            crate::Item::Static(f.fold_item_static(_binding_0))
        }
        crate::Item::Struct(_binding_0) => {
            crate::Item::Struct(f.fold_item_struct(_binding_0))
        }
        crate::Item::Trait(_binding_0) => {
            crate::Item::Trait(f.fold_item_trait(_binding_0))
        }
        crate::Item::TraitAlias(_binding_0) => {
            crate::Item::TraitAlias(f.fold_item_trait_alias(_binding_0))
        }
        crate::Item::Type(_binding_0) => crate::Item::Type(f.fold_item_type(_binding_0)),
        crate::Item::Union(_binding_0) => {
            crate::Item::Union(f.fold_item_union(_binding_0))
        }
        crate::Item::Use(_binding_0) => crate::Item::Use(f.fold_item_use(_binding_0)),
        crate::Item::Verbatim(_binding_0) => {
            crate::Item::Verbatim(f.fold_token_stream(_binding_0))
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_const<F>(f: &mut F, node: crate::ItemConst) -> crate::ItemConst
where
    F: Fold + ?Sized,
{
    crate::ItemConst {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        const_token: node.const_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        colon_token: node.colon_token,
        ty: Box::new(f.fold_type(*node.ty)),
        eq_token: node.eq_token,
        expr: Box::new(f.fold_expr(*node.expr)),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_enum<F>(f: &mut F, node: crate::ItemEnum) -> crate::ItemEnum
where
    F: Fold + ?Sized,
{
    crate::ItemEnum {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        enum_token: node.enum_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        brace_token: node.brace_token,
        variants: crate::punctuated::fold(node.variants, f, F::fold_variant),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_extern_crate<F>(
    f: &mut F,
    node: crate::ItemExternCrate,
) -> crate::ItemExternCrate
where
    F: Fold + ?Sized,
{
    crate::ItemExternCrate {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        extern_token: node.extern_token,
        crate_token: node.crate_token,
        ident: f.fold_ident(node.ident),
        rename: (node.rename).map(|it| ((it).0, f.fold_ident((it).1))),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_fn<F>(f: &mut F, node: crate::ItemFn) -> crate::ItemFn
where
    F: Fold + ?Sized,
{
    crate::ItemFn {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        sig: f.fold_signature(node.sig),
        block: Box::new(f.fold_block(*node.block)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_foreign_mod<F>(
    f: &mut F,
    node: crate::ItemForeignMod,
) -> crate::ItemForeignMod
where
    F: Fold + ?Sized,
{
    crate::ItemForeignMod {
        attrs: f.fold_attributes(node.attrs),
        unsafety: node.unsafety,
        abi: f.fold_abi(node.abi),
        brace_token: node.brace_token,
        items: fold_vec(node.items, f, F::fold_foreign_item),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_impl<F>(f: &mut F, node: crate::ItemImpl) -> crate::ItemImpl
where
    F: Fold + ?Sized,
{
    crate::ItemImpl {
        attrs: f.fold_attributes(node.attrs),
        defaultness: node.defaultness,
        unsafety: node.unsafety,
        impl_token: node.impl_token,
        generics: f.fold_generics(node.generics),
        trait_: (node.trait_).map(|it| ((it).0, f.fold_path((it).1), (it).2)),
        self_ty: Box::new(f.fold_type(*node.self_ty)),
        brace_token: node.brace_token,
        items: fold_vec(node.items, f, F::fold_impl_item),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_macro<F>(f: &mut F, node: crate::ItemMacro) -> crate::ItemMacro
where
    F: Fold + ?Sized,
{
    crate::ItemMacro {
        attrs: f.fold_attributes(node.attrs),
        ident: (node.ident).map(|it| f.fold_ident(it)),
        mac: f.fold_macro(node.mac),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_mod<F>(f: &mut F, node: crate::ItemMod) -> crate::ItemMod
where
    F: Fold + ?Sized,
{
    crate::ItemMod {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        unsafety: node.unsafety,
        mod_token: node.mod_token,
        ident: f.fold_ident(node.ident),
        content: (node.content).map(|it| ((it).0, fold_vec((it).1, f, F::fold_item))),
        semi: node.semi,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_static<F>(f: &mut F, node: crate::ItemStatic) -> crate::ItemStatic
where
    F: Fold + ?Sized,
{
    crate::ItemStatic {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        static_token: node.static_token,
        mutability: f.fold_static_mutability(node.mutability),
        ident: f.fold_ident(node.ident),
        colon_token: node.colon_token,
        ty: Box::new(f.fold_type(*node.ty)),
        eq_token: node.eq_token,
        expr: Box::new(f.fold_expr(*node.expr)),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_struct<F>(f: &mut F, node: crate::ItemStruct) -> crate::ItemStruct
where
    F: Fold + ?Sized,
{
    crate::ItemStruct {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        struct_token: node.struct_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        fields: f.fold_fields(node.fields),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_trait<F>(f: &mut F, node: crate::ItemTrait) -> crate::ItemTrait
where
    F: Fold + ?Sized,
{
    crate::ItemTrait {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        unsafety: node.unsafety,
        auto_token: node.auto_token,
        restriction: (node.restriction).map(|it| f.fold_impl_restriction(it)),
        trait_token: node.trait_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        colon_token: node.colon_token,
        supertraits: crate::punctuated::fold(
            node.supertraits,
            f,
            F::fold_type_param_bound,
        ),
        brace_token: node.brace_token,
        items: fold_vec(node.items, f, F::fold_trait_item),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_trait_alias<F>(
    f: &mut F,
    node: crate::ItemTraitAlias,
) -> crate::ItemTraitAlias
where
    F: Fold + ?Sized,
{
    crate::ItemTraitAlias {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        trait_token: node.trait_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        eq_token: node.eq_token,
        bounds: crate::punctuated::fold(node.bounds, f, F::fold_type_param_bound),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_type<F>(f: &mut F, node: crate::ItemType) -> crate::ItemType
where
    F: Fold + ?Sized,
{
    crate::ItemType {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        type_token: node.type_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        eq_token: node.eq_token,
        ty: Box::new(f.fold_type(*node.ty)),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_union<F>(f: &mut F, node: crate::ItemUnion) -> crate::ItemUnion
where
    F: Fold + ?Sized,
{
    crate::ItemUnion {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        union_token: node.union_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        fields: f.fold_fields_named(node.fields),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_item_use<F>(f: &mut F, node: crate::ItemUse) -> crate::ItemUse
where
    F: Fold + ?Sized,
{
    crate::ItemUse {
        attrs: f.fold_attributes(node.attrs),
        vis: f.fold_visibility(node.vis),
        use_token: node.use_token,
        leading_colon: node.leading_colon,
        tree: f.fold_use_tree(node.tree),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_label<F>(f: &mut F, node: crate::Label) -> crate::Label
where
    F: Fold + ?Sized,
{
    crate::Label {
        name: f.fold_lifetime(node.name),
        colon_token: node.colon_token,
    }
}
pub fn fold_lifetime<F>(f: &mut F, node: crate::Lifetime) -> crate::Lifetime
where
    F: Fold + ?Sized,
{
    crate::Lifetime {
        apostrophe: f.fold_span(node.apostrophe),
        ident: f.fold_ident(node.ident),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_lifetime_param<F>(
    f: &mut F,
    node: crate::LifetimeParam,
) -> crate::LifetimeParam
where
    F: Fold + ?Sized,
{
    crate::LifetimeParam {
        attrs: f.fold_attributes(node.attrs),
        lifetime: f.fold_lifetime(node.lifetime),
        colon_token: node.colon_token,
        bounds: crate::punctuated::fold(node.bounds, f, F::fold_lifetime),
    }
}
pub fn fold_lit<F>(f: &mut F, node: crate::Lit) -> crate::Lit
where
    F: Fold + ?Sized,
{
    match node {
        crate::Lit::Str(_binding_0) => crate::Lit::Str(f.fold_lit_str(_binding_0)),
        crate::Lit::ByteStr(_binding_0) => {
            crate::Lit::ByteStr(f.fold_lit_byte_str(_binding_0))
        }
        crate::Lit::CStr(_binding_0) => crate::Lit::CStr(f.fold_lit_cstr(_binding_0)),
        crate::Lit::Byte(_binding_0) => crate::Lit::Byte(f.fold_lit_byte(_binding_0)),
        crate::Lit::Char(_binding_0) => crate::Lit::Char(f.fold_lit_char(_binding_0)),
        crate::Lit::Int(_binding_0) => crate::Lit::Int(f.fold_lit_int(_binding_0)),
        crate::Lit::Float(_binding_0) => crate::Lit::Float(f.fold_lit_float(_binding_0)),
        crate::Lit::Bool(_binding_0) => crate::Lit::Bool(f.fold_lit_bool(_binding_0)),
        crate::Lit::Verbatim(_binding_0) => crate::Lit::Verbatim(_binding_0),
    }
}
pub fn fold_lit_bool<F>(f: &mut F, node: crate::LitBool) -> crate::LitBool
where
    F: Fold + ?Sized,
{
    crate::LitBool {
        value: node.value,
        span: f.fold_span(node.span),
    }
}
pub fn fold_lit_byte<F>(f: &mut F, node: crate::LitByte) -> crate::LitByte
where
    F: Fold + ?Sized,
{
    let span = f.fold_span(node.span());
    let mut node = node;
    node.set_span(span);
    node
}
pub fn fold_lit_byte_str<F>(f: &mut F, node: crate::LitByteStr) -> crate::LitByteStr
where
    F: Fold + ?Sized,
{
    let span = f.fold_span(node.span());
    let mut node = node;
    node.set_span(span);
    node
}
pub fn fold_lit_cstr<F>(f: &mut F, node: crate::LitCStr) -> crate::LitCStr
where
    F: Fold + ?Sized,
{
    let span = f.fold_span(node.span());
    let mut node = node;
    node.set_span(span);
    node
}
pub fn fold_lit_char<F>(f: &mut F, node: crate::LitChar) -> crate::LitChar
where
    F: Fold + ?Sized,
{
    let span = f.fold_span(node.span());
    let mut node = node;
    node.set_span(span);
    node
}
pub fn fold_lit_float<F>(f: &mut F, node: crate::LitFloat) -> crate::LitFloat
where
    F: Fold + ?Sized,
{
    let span = f.fold_span(node.span());
    let mut node = node;
    node.set_span(span);
    node
}
pub fn fold_lit_int<F>(f: &mut F, node: crate::LitInt) -> crate::LitInt
where
    F: Fold + ?Sized,
{
    let span = f.fold_span(node.span());
    let mut node = node;
    node.set_span(span);
    node
}
pub fn fold_lit_str<F>(f: &mut F, node: crate::LitStr) -> crate::LitStr
where
    F: Fold + ?Sized,
{
    let span = f.fold_span(node.span());
    let mut node = node;
    node.set_span(span);
    node
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_local<F>(f: &mut F, node: crate::Local) -> crate::Local
where
    F: Fold + ?Sized,
{
    crate::Local {
        attrs: f.fold_attributes(node.attrs),
        let_token: node.let_token,
        pat: f.fold_pat(node.pat),
        init: (node.init).map(|it| f.fold_local_init(it)),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_local_init<F>(f: &mut F, node: crate::LocalInit) -> crate::LocalInit
where
    F: Fold + ?Sized,
{
    crate::LocalInit {
        eq_token: node.eq_token,
        expr: Box::new(f.fold_expr(*node.expr)),
        diverge: (node.diverge).map(|it| ((it).0, Box::new(f.fold_expr(*(it).1)))),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_macro<F>(f: &mut F, node: crate::Macro) -> crate::Macro
where
    F: Fold + ?Sized,
{
    crate::Macro {
        path: f.fold_path(node.path),
        bang_token: node.bang_token,
        delimiter: f.fold_macro_delimiter(node.delimiter),
        tokens: f.fold_token_stream(node.tokens),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_macro_delimiter<F>(
    f: &mut F,
    node: crate::MacroDelimiter,
) -> crate::MacroDelimiter
where
    F: Fold + ?Sized,
{
    match node {
        crate::MacroDelimiter::Paren(_binding_0) => {
            crate::MacroDelimiter::Paren(_binding_0)
        }
        crate::MacroDelimiter::Brace(_binding_0) => {
            crate::MacroDelimiter::Brace(_binding_0)
        }
        crate::MacroDelimiter::Bracket(_binding_0) => {
            crate::MacroDelimiter::Bracket(_binding_0)
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_member<F>(f: &mut F, node: crate::Member) -> crate::Member
where
    F: Fold + ?Sized,
{
    match node {
        crate::Member::Named(_binding_0) => {
            crate::Member::Named(f.fold_ident(_binding_0))
        }
        crate::Member::Unnamed(_binding_0) => {
            crate::Member::Unnamed(f.fold_index(_binding_0))
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_meta<F>(f: &mut F, node: crate::Meta) -> crate::Meta
where
    F: Fold + ?Sized,
{
    match node {
        crate::Meta::Path(_binding_0) => crate::Meta::Path(f.fold_path(_binding_0)),
        crate::Meta::List(_binding_0) => crate::Meta::List(f.fold_meta_list(_binding_0)),
        crate::Meta::NameValue(_binding_0) => {
            crate::Meta::NameValue(f.fold_meta_name_value(_binding_0))
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_meta_list<F>(f: &mut F, node: crate::MetaList) -> crate::MetaList
where
    F: Fold + ?Sized,
{
    crate::MetaList {
        path: f.fold_path(node.path),
        delimiter: f.fold_macro_delimiter(node.delimiter),
        tokens: f.fold_token_stream(node.tokens),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_meta_name_value<F>(
    f: &mut F,
    node: crate::MetaNameValue,
) -> crate::MetaNameValue
where
    F: Fold + ?Sized,
{
    crate::MetaNameValue {
        path: f.fold_path(node.path),
        eq_token: node.eq_token,
        value: f.fold_expr(node.value),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_parenthesized_generic_arguments<F>(
    f: &mut F,
    node: crate::ParenthesizedGenericArguments,
) -> crate::ParenthesizedGenericArguments
where
    F: Fold + ?Sized,
{
    crate::ParenthesizedGenericArguments {
        paren_token: node.paren_token,
        inputs: crate::punctuated::fold(node.inputs, f, F::fold_type),
        output: f.fold_return_type(node.output),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat<F>(f: &mut F, node: crate::Pat) -> crate::Pat
where
    F: Fold + ?Sized,
{
    match node {
        crate::Pat::Const(_binding_0) => crate::Pat::Const(f.fold_expr_const(_binding_0)),
        crate::Pat::Ident(_binding_0) => crate::Pat::Ident(f.fold_pat_ident(_binding_0)),
        crate::Pat::Lit(_binding_0) => crate::Pat::Lit(f.fold_expr_lit(_binding_0)),
        crate::Pat::Macro(_binding_0) => crate::Pat::Macro(f.fold_expr_macro(_binding_0)),
        crate::Pat::Or(_binding_0) => crate::Pat::Or(f.fold_pat_or(_binding_0)),
        crate::Pat::Paren(_binding_0) => crate::Pat::Paren(f.fold_pat_paren(_binding_0)),
        crate::Pat::Path(_binding_0) => crate::Pat::Path(f.fold_expr_path(_binding_0)),
        crate::Pat::Range(_binding_0) => crate::Pat::Range(f.fold_expr_range(_binding_0)),
        crate::Pat::Reference(_binding_0) => {
            crate::Pat::Reference(f.fold_pat_reference(_binding_0))
        }
        crate::Pat::Rest(_binding_0) => crate::Pat::Rest(f.fold_pat_rest(_binding_0)),
        crate::Pat::Slice(_binding_0) => crate::Pat::Slice(f.fold_pat_slice(_binding_0)),
        crate::Pat::Struct(_binding_0) => {
            crate::Pat::Struct(f.fold_pat_struct(_binding_0))
        }
        crate::Pat::Tuple(_binding_0) => crate::Pat::Tuple(f.fold_pat_tuple(_binding_0)),
        crate::Pat::TupleStruct(_binding_0) => {
            crate::Pat::TupleStruct(f.fold_pat_tuple_struct(_binding_0))
        }
        crate::Pat::Type(_binding_0) => crate::Pat::Type(f.fold_pat_type(_binding_0)),
        crate::Pat::Verbatim(_binding_0) => {
            crate::Pat::Verbatim(f.fold_token_stream(_binding_0))
        }
        crate::Pat::Wild(_binding_0) => crate::Pat::Wild(f.fold_pat_wild(_binding_0)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_ident<F>(f: &mut F, node: crate::PatIdent) -> crate::PatIdent
where
    F: Fold + ?Sized,
{
    crate::PatIdent {
        attrs: f.fold_attributes(node.attrs),
        by_ref: node.by_ref,
        mutability: node.mutability,
        ident: f.fold_ident(node.ident),
        subpat: (node.subpat).map(|it| ((it).0, Box::new(f.fold_pat(*(it).1)))),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_or<F>(f: &mut F, node: crate::PatOr) -> crate::PatOr
where
    F: Fold + ?Sized,
{
    crate::PatOr {
        attrs: f.fold_attributes(node.attrs),
        leading_vert: node.leading_vert,
        cases: crate::punctuated::fold(node.cases, f, F::fold_pat),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_paren<F>(f: &mut F, node: crate::PatParen) -> crate::PatParen
where
    F: Fold + ?Sized,
{
    crate::PatParen {
        attrs: f.fold_attributes(node.attrs),
        paren_token: node.paren_token,
        pat: Box::new(f.fold_pat(*node.pat)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_reference<F>(f: &mut F, node: crate::PatReference) -> crate::PatReference
where
    F: Fold + ?Sized,
{
    crate::PatReference {
        attrs: f.fold_attributes(node.attrs),
        and_token: node.and_token,
        mutability: node.mutability,
        pat: Box::new(f.fold_pat(*node.pat)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_rest<F>(f: &mut F, node: crate::PatRest) -> crate::PatRest
where
    F: Fold + ?Sized,
{
    crate::PatRest {
        attrs: f.fold_attributes(node.attrs),
        dot2_token: node.dot2_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_slice<F>(f: &mut F, node: crate::PatSlice) -> crate::PatSlice
where
    F: Fold + ?Sized,
{
    crate::PatSlice {
        attrs: f.fold_attributes(node.attrs),
        bracket_token: node.bracket_token,
        elems: crate::punctuated::fold(node.elems, f, F::fold_pat),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_struct<F>(f: &mut F, node: crate::PatStruct) -> crate::PatStruct
where
    F: Fold + ?Sized,
{
    crate::PatStruct {
        attrs: f.fold_attributes(node.attrs),
        qself: (node.qself).map(|it| f.fold_qself(it)),
        path: f.fold_path(node.path),
        brace_token: node.brace_token,
        fields: crate::punctuated::fold(node.fields, f, F::fold_field_pat),
        rest: (node.rest).map(|it| f.fold_pat_rest(it)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_tuple<F>(f: &mut F, node: crate::PatTuple) -> crate::PatTuple
where
    F: Fold + ?Sized,
{
    crate::PatTuple {
        attrs: f.fold_attributes(node.attrs),
        paren_token: node.paren_token,
        elems: crate::punctuated::fold(node.elems, f, F::fold_pat),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_tuple_struct<F>(
    f: &mut F,
    node: crate::PatTupleStruct,
) -> crate::PatTupleStruct
where
    F: Fold + ?Sized,
{
    crate::PatTupleStruct {
        attrs: f.fold_attributes(node.attrs),
        qself: (node.qself).map(|it| f.fold_qself(it)),
        path: f.fold_path(node.path),
        paren_token: node.paren_token,
        elems: crate::punctuated::fold(node.elems, f, F::fold_pat),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_type<F>(f: &mut F, node: crate::PatType) -> crate::PatType
where
    F: Fold + ?Sized,
{
    crate::PatType {
        attrs: f.fold_attributes(node.attrs),
        pat: Box::new(f.fold_pat(*node.pat)),
        colon_token: node.colon_token,
        ty: Box::new(f.fold_type(*node.ty)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pat_wild<F>(f: &mut F, node: crate::PatWild) -> crate::PatWild
where
    F: Fold + ?Sized,
{
    crate::PatWild {
        attrs: f.fold_attributes(node.attrs),
        underscore_token: node.underscore_token,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_path<F>(f: &mut F, node: crate::Path) -> crate::Path
where
    F: Fold + ?Sized,
{
    crate::Path {
        leading_colon: node.leading_colon,
        segments: crate::punctuated::fold(node.segments, f, F::fold_path_segment),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_path_arguments<F>(
    f: &mut F,
    node: crate::PathArguments,
) -> crate::PathArguments
where
    F: Fold + ?Sized,
{
    match node {
        crate::PathArguments::None => crate::PathArguments::None,
        crate::PathArguments::AngleBracketed(_binding_0) => {
            crate::PathArguments::AngleBracketed(
                f.fold_angle_bracketed_generic_arguments(_binding_0),
            )
        }
        crate::PathArguments::Parenthesized(_binding_0) => {
            crate::PathArguments::Parenthesized(
                f.fold_parenthesized_generic_arguments(_binding_0),
            )
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_path_segment<F>(f: &mut F, node: crate::PathSegment) -> crate::PathSegment
where
    F: Fold + ?Sized,
{
    crate::PathSegment {
        ident: f.fold_ident(node.ident),
        arguments: f.fold_path_arguments(node.arguments),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_pointer_mutability<F>(
    f: &mut F,
    node: crate::PointerMutability,
) -> crate::PointerMutability
where
    F: Fold + ?Sized,
{
    match node {
        crate::PointerMutability::Const(_binding_0) => {
            crate::PointerMutability::Const(_binding_0)
        }
        crate::PointerMutability::Mut(_binding_0) => {
            crate::PointerMutability::Mut(_binding_0)
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_precise_capture<F>(
    f: &mut F,
    node: crate::PreciseCapture,
) -> crate::PreciseCapture
where
    F: Fold + ?Sized,
{
    crate::PreciseCapture {
        use_token: node.use_token,
        lt_token: node.lt_token,
        params: crate::punctuated::fold(node.params, f, F::fold_captured_param),
        gt_token: node.gt_token,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_predicate_lifetime<F>(
    f: &mut F,
    node: crate::PredicateLifetime,
) -> crate::PredicateLifetime
where
    F: Fold + ?Sized,
{
    crate::PredicateLifetime {
        lifetime: f.fold_lifetime(node.lifetime),
        colon_token: node.colon_token,
        bounds: crate::punctuated::fold(node.bounds, f, F::fold_lifetime),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_predicate_type<F>(
    f: &mut F,
    node: crate::PredicateType,
) -> crate::PredicateType
where
    F: Fold + ?Sized,
{
    crate::PredicateType {
        lifetimes: (node.lifetimes).map(|it| f.fold_bound_lifetimes(it)),
        bounded_ty: f.fold_type(node.bounded_ty),
        colon_token: node.colon_token,
        bounds: crate::punctuated::fold(node.bounds, f, F::fold_type_param_bound),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_qself<F>(f: &mut F, node: crate::QSelf) -> crate::QSelf
where
    F: Fold + ?Sized,
{
    crate::QSelf {
        lt_token: node.lt_token,
        ty: Box::new(f.fold_type(*node.ty)),
        position: node.position,
        as_token: node.as_token,
        gt_token: node.gt_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_range_limits<F>(f: &mut F, node: crate::RangeLimits) -> crate::RangeLimits
where
    F: Fold + ?Sized,
{
    match node {
        crate::RangeLimits::HalfOpen(_binding_0) => {
            crate::RangeLimits::HalfOpen(_binding_0)
        }
        crate::RangeLimits::Closed(_binding_0) => crate::RangeLimits::Closed(_binding_0),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_receiver<F>(f: &mut F, node: crate::Receiver) -> crate::Receiver
where
    F: Fold + ?Sized,
{
    crate::Receiver {
        attrs: f.fold_attributes(node.attrs),
        reference: (node.reference)
            .map(|it| ((it).0, ((it).1).map(|it| f.fold_lifetime(it)))),
        mutability: node.mutability,
        self_token: node.self_token,
        colon_token: node.colon_token,
        ty: Box::new(f.fold_type(*node.ty)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_return_type<F>(f: &mut F, node: crate::ReturnType) -> crate::ReturnType
where
    F: Fold + ?Sized,
{
    match node {
        crate::ReturnType::Default => crate::ReturnType::Default,
        crate::ReturnType::Type(_binding_0, _binding_1) => {
            crate::ReturnType::Type(_binding_0, Box::new(f.fold_type(*_binding_1)))
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_signature<F>(f: &mut F, node: crate::Signature) -> crate::Signature
where
    F: Fold + ?Sized,
{
    crate::Signature {
        constness: node.constness,
        asyncness: node.asyncness,
        unsafety: node.unsafety,
        abi: (node.abi).map(|it| f.fold_abi(it)),
        fn_token: node.fn_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        paren_token: node.paren_token,
        inputs: crate::punctuated::fold(node.inputs, f, F::fold_fn_arg),
        variadic: (node.variadic).map(|it| f.fold_variadic(it)),
        output: f.fold_return_type(node.output),
    }
}
pub fn fold_span<F>(f: &mut F, node: proc_macro2::Span) -> proc_macro2::Span
where
    F: Fold + ?Sized,
{
    node
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_static_mutability<F>(
    f: &mut F,
    node: crate::StaticMutability,
) -> crate::StaticMutability
where
    F: Fold + ?Sized,
{
    match node {
        crate::StaticMutability::Mut(_binding_0) => {
            crate::StaticMutability::Mut(_binding_0)
        }
        crate::StaticMutability::None => crate::StaticMutability::None,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_stmt<F>(f: &mut F, node: crate::Stmt) -> crate::Stmt
where
    F: Fold + ?Sized,
{
    match node {
        crate::Stmt::Local(_binding_0) => crate::Stmt::Local(f.fold_local(_binding_0)),
        crate::Stmt::Item(_binding_0) => crate::Stmt::Item(f.fold_item(_binding_0)),
        crate::Stmt::Expr(_binding_0, _binding_1) => {
            crate::Stmt::Expr(f.fold_expr(_binding_0), _binding_1)
        }
        crate::Stmt::Macro(_binding_0) => {
            crate::Stmt::Macro(f.fold_stmt_macro(_binding_0))
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_stmt_macro<F>(f: &mut F, node: crate::StmtMacro) -> crate::StmtMacro
where
    F: Fold + ?Sized,
{
    crate::StmtMacro {
        attrs: f.fold_attributes(node.attrs),
        mac: f.fold_macro(node.mac),
        semi_token: node.semi_token,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_trait_bound<F>(f: &mut F, node: crate::TraitBound) -> crate::TraitBound
where
    F: Fold + ?Sized,
{
    crate::TraitBound {
        paren_token: node.paren_token,
        modifier: f.fold_trait_bound_modifier(node.modifier),
        lifetimes: (node.lifetimes).map(|it| f.fold_bound_lifetimes(it)),
        path: f.fold_path(node.path),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_trait_bound_modifier<F>(
    f: &mut F,
    node: crate::TraitBoundModifier,
) -> crate::TraitBoundModifier
where
    F: Fold + ?Sized,
{
    match node {
        crate::TraitBoundModifier::None => crate::TraitBoundModifier::None,
        crate::TraitBoundModifier::Maybe(_binding_0) => {
            crate::TraitBoundModifier::Maybe(_binding_0)
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_trait_item<F>(f: &mut F, node: crate::TraitItem) -> crate::TraitItem
where
    F: Fold + ?Sized,
{
    match node {
        crate::TraitItem::Const(_binding_0) => {
            crate::TraitItem::Const(f.fold_trait_item_const(_binding_0))
        }
        crate::TraitItem::Fn(_binding_0) => {
            crate::TraitItem::Fn(f.fold_trait_item_fn(_binding_0))
        }
        crate::TraitItem::Type(_binding_0) => {
            crate::TraitItem::Type(f.fold_trait_item_type(_binding_0))
        }
        crate::TraitItem::Macro(_binding_0) => {
            crate::TraitItem::Macro(f.fold_trait_item_macro(_binding_0))
        }
        crate::TraitItem::Verbatim(_binding_0) => {
            crate::TraitItem::Verbatim(f.fold_token_stream(_binding_0))
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_trait_item_const<F>(
    f: &mut F,
    node: crate::TraitItemConst,
) -> crate::TraitItemConst
where
    F: Fold + ?Sized,
{
    crate::TraitItemConst {
        attrs: f.fold_attributes(node.attrs),
        const_token: node.const_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        colon_token: node.colon_token,
        ty: f.fold_type(node.ty),
        default: (node.default).map(|it| ((it).0, f.fold_expr((it).1))),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_trait_item_fn<F>(f: &mut F, node: crate::TraitItemFn) -> crate::TraitItemFn
where
    F: Fold + ?Sized,
{
    crate::TraitItemFn {
        attrs: f.fold_attributes(node.attrs),
        sig: f.fold_signature(node.sig),
        default: (node.default).map(|it| f.fold_block(it)),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_trait_item_macro<F>(
    f: &mut F,
    node: crate::TraitItemMacro,
) -> crate::TraitItemMacro
where
    F: Fold + ?Sized,
{
    crate::TraitItemMacro {
        attrs: f.fold_attributes(node.attrs),
        mac: f.fold_macro(node.mac),
        semi_token: node.semi_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_trait_item_type<F>(
    f: &mut F,
    node: crate::TraitItemType,
) -> crate::TraitItemType
where
    F: Fold + ?Sized,
{
    crate::TraitItemType {
        attrs: f.fold_attributes(node.attrs),
        type_token: node.type_token,
        ident: f.fold_ident(node.ident),
        generics: f.fold_generics(node.generics),
        colon_token: node.colon_token,
        bounds: crate::punctuated::fold(node.bounds, f, F::fold_type_param_bound),
        default: (node.default).map(|it| ((it).0, f.fold_type((it).1))),
        semi_token: node.semi_token,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type<F>(f: &mut F, node: crate::Type) -> crate::Type
where
    F: Fold + ?Sized,
{
    match node {
        crate::Type::Array(_binding_0) => {
            crate::Type::Array(f.fold_type_array(_binding_0))
        }
        crate::Type::BareFn(_binding_0) => {
            crate::Type::BareFn(f.fold_type_bare_fn(_binding_0))
        }
        crate::Type::Group(_binding_0) => {
            crate::Type::Group(f.fold_type_group(_binding_0))
        }
        crate::Type::ImplTrait(_binding_0) => {
            crate::Type::ImplTrait(f.fold_type_impl_trait(_binding_0))
        }
        crate::Type::Infer(_binding_0) => {
            crate::Type::Infer(f.fold_type_infer(_binding_0))
        }
        crate::Type::Macro(_binding_0) => {
            crate::Type::Macro(f.fold_type_macro(_binding_0))
        }
        crate::Type::Never(_binding_0) => {
            crate::Type::Never(f.fold_type_never(_binding_0))
        }
        crate::Type::Paren(_binding_0) => {
            crate::Type::Paren(f.fold_type_paren(_binding_0))
        }
        crate::Type::Path(_binding_0) => crate::Type::Path(f.fold_type_path(_binding_0)),
        crate::Type::Ptr(_binding_0) => crate::Type::Ptr(f.fold_type_ptr(_binding_0)),
        crate::Type::Reference(_binding_0) => {
            crate::Type::Reference(f.fold_type_reference(_binding_0))
        }
        crate::Type::Slice(_binding_0) => {
            crate::Type::Slice(f.fold_type_slice(_binding_0))
        }
        crate::Type::TraitObject(_binding_0) => {
            crate::Type::TraitObject(f.fold_type_trait_object(_binding_0))
        }
        crate::Type::Tuple(_binding_0) => {
            crate::Type::Tuple(f.fold_type_tuple(_binding_0))
        }
        crate::Type::Verbatim(_binding_0) => {
            crate::Type::Verbatim(f.fold_token_stream(_binding_0))
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_array<F>(f: &mut F, node: crate::TypeArray) -> crate::TypeArray
where
    F: Fold + ?Sized,
{
    crate::TypeArray {
        bracket_token: node.bracket_token,
        elem: Box::new(f.fold_type(*node.elem)),
        semi_token: node.semi_token,
        len: f.fold_expr(node.len),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_bare_fn<F>(f: &mut F, node: crate::TypeBareFn) -> crate::TypeBareFn
where
    F: Fold + ?Sized,
{
    crate::TypeBareFn {
        lifetimes: (node.lifetimes).map(|it| f.fold_bound_lifetimes(it)),
        unsafety: node.unsafety,
        abi: (node.abi).map(|it| f.fold_abi(it)),
        fn_token: node.fn_token,
        paren_token: node.paren_token,
        inputs: crate::punctuated::fold(node.inputs, f, F::fold_bare_fn_arg),
        variadic: (node.variadic).map(|it| f.fold_bare_variadic(it)),
        output: f.fold_return_type(node.output),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_group<F>(f: &mut F, node: crate::TypeGroup) -> crate::TypeGroup
where
    F: Fold + ?Sized,
{
    crate::TypeGroup {
        group_token: node.group_token,
        elem: Box::new(f.fold_type(*node.elem)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_impl_trait<F>(
    f: &mut F,
    node: crate::TypeImplTrait,
) -> crate::TypeImplTrait
where
    F: Fold + ?Sized,
{
    crate::TypeImplTrait {
        impl_token: node.impl_token,
        bounds: crate::punctuated::fold(node.bounds, f, F::fold_type_param_bound),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_infer<F>(f: &mut F, node: crate::TypeInfer) -> crate::TypeInfer
where
    F: Fold + ?Sized,
{
    crate::TypeInfer {
        underscore_token: node.underscore_token,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_macro<F>(f: &mut F, node: crate::TypeMacro) -> crate::TypeMacro
where
    F: Fold + ?Sized,
{
    crate::TypeMacro {
        mac: f.fold_macro(node.mac),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_never<F>(f: &mut F, node: crate::TypeNever) -> crate::TypeNever
where
    F: Fold + ?Sized,
{
    crate::TypeNever {
        bang_token: node.bang_token,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_param<F>(f: &mut F, node: crate::TypeParam) -> crate::TypeParam
where
    F: Fold + ?Sized,
{
    crate::TypeParam {
        attrs: f.fold_attributes(node.attrs),
        ident: f.fold_ident(node.ident),
        colon_token: node.colon_token,
        bounds: crate::punctuated::fold(node.bounds, f, F::fold_type_param_bound),
        eq_token: node.eq_token,
        default: (node.default).map(|it| f.fold_type(it)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_param_bound<F>(
    f: &mut F,
    node: crate::TypeParamBound,
) -> crate::TypeParamBound
where
    F: Fold + ?Sized,
{
    match node {
        crate::TypeParamBound::Trait(_binding_0) => {
            crate::TypeParamBound::Trait(f.fold_trait_bound(_binding_0))
        }
        crate::TypeParamBound::Lifetime(_binding_0) => {
            crate::TypeParamBound::Lifetime(f.fold_lifetime(_binding_0))
        }
        crate::TypeParamBound::PreciseCapture(_binding_0) => {
            crate::TypeParamBound::PreciseCapture(
                full!(f.fold_precise_capture(_binding_0)),
            )
        }
        crate::TypeParamBound::Verbatim(_binding_0) => {
            crate::TypeParamBound::Verbatim(f.fold_token_stream(_binding_0))
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_paren<F>(f: &mut F, node: crate::TypeParen) -> crate::TypeParen
where
    F: Fold + ?Sized,
{
    crate::TypeParen {
        paren_token: node.paren_token,
        elem: Box::new(f.fold_type(*node.elem)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_path<F>(f: &mut F, node: crate::TypePath) -> crate::TypePath
where
    F: Fold + ?Sized,
{
    crate::TypePath {
        qself: (node.qself).map(|it| f.fold_qself(it)),
        path: f.fold_path(node.path),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_ptr<F>(f: &mut F, node: crate::TypePtr) -> crate::TypePtr
where
    F: Fold + ?Sized,
{
    crate::TypePtr {
        star_token: node.star_token,
        const_token: node.const_token,
        mutability: node.mutability,
        elem: Box::new(f.fold_type(*node.elem)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_reference<F>(
    f: &mut F,
    node: crate::TypeReference,
) -> crate::TypeReference
where
    F: Fold + ?Sized,
{
    crate::TypeReference {
        and_token: node.and_token,
        lifetime: (node.lifetime).map(|it| f.fold_lifetime(it)),
        mutability: node.mutability,
        elem: Box::new(f.fold_type(*node.elem)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_slice<F>(f: &mut F, node: crate::TypeSlice) -> crate::TypeSlice
where
    F: Fold + ?Sized,
{
    crate::TypeSlice {
        bracket_token: node.bracket_token,
        elem: Box::new(f.fold_type(*node.elem)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_trait_object<F>(
    f: &mut F,
    node: crate::TypeTraitObject,
) -> crate::TypeTraitObject
where
    F: Fold + ?Sized,
{
    crate::TypeTraitObject {
        dyn_token: node.dyn_token,
        bounds: crate::punctuated::fold(node.bounds, f, F::fold_type_param_bound),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_type_tuple<F>(f: &mut F, node: crate::TypeTuple) -> crate::TypeTuple
where
    F: Fold + ?Sized,
{
    crate::TypeTuple {
        paren_token: node.paren_token,
        elems: crate::punctuated::fold(node.elems, f, F::fold_type),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_un_op<F>(f: &mut F, node: crate::UnOp) -> crate::UnOp
where
    F: Fold + ?Sized,
{
    match node {
        crate::UnOp::Deref(_binding_0) => crate::UnOp::Deref(_binding_0),
        crate::UnOp::Not(_binding_0) => crate::UnOp::Not(_binding_0),
        crate::UnOp::Neg(_binding_0) => crate::UnOp::Neg(_binding_0),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_use_glob<F>(f: &mut F, node: crate::UseGlob) -> crate::UseGlob
where
    F: Fold + ?Sized,
{
    crate::UseGlob {
        star_token: node.star_token,
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_use_group<F>(f: &mut F, node: crate::UseGroup) -> crate::UseGroup
where
    F: Fold + ?Sized,
{
    crate::UseGroup {
        brace_token: node.brace_token,
        items: crate::punctuated::fold(node.items, f, F::fold_use_tree),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_use_name<F>(f: &mut F, node: crate::UseName) -> crate::UseName
where
    F: Fold + ?Sized,
{
    crate::UseName {
        ident: f.fold_ident(node.ident),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_use_path<F>(f: &mut F, node: crate::UsePath) -> crate::UsePath
where
    F: Fold + ?Sized,
{
    crate::UsePath {
        ident: f.fold_ident(node.ident),
        colon2_token: node.colon2_token,
        tree: Box::new(f.fold_use_tree(*node.tree)),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_use_rename<F>(f: &mut F, node: crate::UseRename) -> crate::UseRename
where
    F: Fold + ?Sized,
{
    crate::UseRename {
        ident: f.fold_ident(node.ident),
        as_token: node.as_token,
        rename: f.fold_ident(node.rename),
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_use_tree<F>(f: &mut F, node: crate::UseTree) -> crate::UseTree
where
    F: Fold + ?Sized,
{
    match node {
        crate::UseTree::Path(_binding_0) => {
            crate::UseTree::Path(f.fold_use_path(_binding_0))
        }
        crate::UseTree::Name(_binding_0) => {
            crate::UseTree::Name(f.fold_use_name(_binding_0))
        }
        crate::UseTree::Rename(_binding_0) => {
            crate::UseTree::Rename(f.fold_use_rename(_binding_0))
        }
        crate::UseTree::Glob(_binding_0) => {
            crate::UseTree::Glob(f.fold_use_glob(_binding_0))
        }
        crate::UseTree::Group(_binding_0) => {
            crate::UseTree::Group(f.fold_use_group(_binding_0))
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn fold_variadic<F>(f: &mut F, node: crate::Variadic) -> crate::Variadic
where
    F: Fold + ?Sized,
{
    crate::Variadic {
        attrs: f.fold_attributes(node.attrs),
        pat: (node.pat).map(|it| (Box::new(f.fold_pat(*(it).0)), (it).1)),
        dots: node.dots,
        comma: node.comma,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_variant<F>(f: &mut F, node: crate::Variant) -> crate::Variant
where
    F: Fold + ?Sized,
{
    crate::Variant {
        attrs: f.fold_attributes(node.attrs),
        ident: f.fold_ident(node.ident),
        fields: f.fold_fields(node.fields),
        discriminant: (node.discriminant).map(|it| ((it).0, f.fold_expr((it).1))),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_vis_restricted<F>(
    f: &mut F,
    node: crate::VisRestricted,
) -> crate::VisRestricted
where
    F: Fold + ?Sized,
{
    crate::VisRestricted {
        pub_token: node.pub_token,
        paren_token: node.paren_token,
        in_token: node.in_token,
        path: Box::new(f.fold_path(*node.path)),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_visibility<F>(f: &mut F, node: crate::Visibility) -> crate::Visibility
where
    F: Fold + ?Sized,
{
    match node {
        crate::Visibility::Public(_binding_0) => crate::Visibility::Public(_binding_0),
        crate::Visibility::Restricted(_binding_0) => {
            crate::Visibility::Restricted(f.fold_vis_restricted(_binding_0))
        }
        crate::Visibility::Inherited => crate::Visibility::Inherited,
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_where_clause<F>(f: &mut F, node: crate::WhereClause) -> crate::WhereClause
where
    F: Fold + ?Sized,
{
    crate::WhereClause {
        where_token: node.where_token,
        predicates: crate::punctuated::fold(node.predicates, f, F::fold_where_predicate),
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn fold_where_predicate<F>(
    f: &mut F,
    node: crate::WherePredicate,
) -> crate::WherePredicate
where
    F: Fold + ?Sized,
{
    match node {
        crate::WherePredicate::Lifetime(_binding_0) => {
            crate::WherePredicate::Lifetime(f.fold_predicate_lifetime(_binding_0))
        }
        crate::WherePredicate::Type(_binding_0) => {
            crate::WherePredicate::Type(f.fold_predicate_type(_binding_0))
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
fn fold_vec<T, V, F>(vec: Vec<T>, fold: &mut V, mut f: F) -> Vec<T>
where
    V: ?Sized,
    F: FnMut(&mut V, T) -> T,
{
    vec.into_iter().map(|it| f(fold, it)).collect()
}
