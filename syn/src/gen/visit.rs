// This file is @generated by syn-internal-codegen.
// It is not intended for manual editing.

#![allow(unused_variables)]
#![allow(clippy::needless_pass_by_ref_mut)]
#[cfg(any(feature = "full", feature = "derive"))]
use crate::punctuated::Punctuated;
#[cfg(feature = "full")]
macro_rules! full {
    ($e:expr) => {
        $e
    };
}
#[cfg(all(feature = "derive", not(feature = "full")))]
macro_rules! full {
    ($e:expr) => {
        unreachable!()
    };
}
macro_rules! skip {
    ($($tt:tt)*) => {};
}
/// Syntax tree traversal to walk a shared borrow of a syntax tree.
///
/// See the [module documentation] for details.
///
/// [module documentation]: self
pub trait Visit<'ast> {
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_abi(&mut self, i: &'ast crate::Abi) {
        visit_abi(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_angle_bracketed_generic_arguments(
        &mut self,
        i: &'ast crate::AngleBracketedGenericArguments,
    ) {
        visit_angle_bracketed_generic_arguments(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_arm(&mut self, i: &'ast crate::Arm) {
        visit_arm(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_assoc_const(&mut self, i: &'ast crate::AssocConst) {
        visit_assoc_const(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_assoc_type(&mut self, i: &'ast crate::AssocType) {
        visit_assoc_type(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_attr_style(&mut self, i: &'ast crate::AttrStyle) {
        visit_attr_style(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_attribute(&mut self, i: &'ast crate::Attribute) {
        visit_attribute(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_bare_fn_arg(&mut self, i: &'ast crate::BareFnArg) {
        visit_bare_fn_arg(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_bare_variadic(&mut self, i: &'ast crate::BareVariadic) {
        visit_bare_variadic(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_bin_op(&mut self, i: &'ast crate::BinOp) {
        visit_bin_op(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_block(&mut self, i: &'ast crate::Block) {
        visit_block(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_bound_lifetimes(&mut self, i: &'ast crate::BoundLifetimes) {
        visit_bound_lifetimes(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_captured_param(&mut self, i: &'ast crate::CapturedParam) {
        visit_captured_param(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_const_param(&mut self, i: &'ast crate::ConstParam) {
        visit_const_param(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_constraint(&mut self, i: &'ast crate::Constraint) {
        visit_constraint(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_data(&mut self, i: &'ast crate::Data) {
        visit_data(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_data_enum(&mut self, i: &'ast crate::DataEnum) {
        visit_data_enum(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_data_struct(&mut self, i: &'ast crate::DataStruct) {
        visit_data_struct(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_data_union(&mut self, i: &'ast crate::DataUnion) {
        visit_data_union(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_derive_input(&mut self, i: &'ast crate::DeriveInput) {
        visit_derive_input(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr(&mut self, i: &'ast crate::Expr) {
        visit_expr(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_array(&mut self, i: &'ast crate::ExprArray) {
        visit_expr_array(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_assign(&mut self, i: &'ast crate::ExprAssign) {
        visit_expr_assign(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_async(&mut self, i: &'ast crate::ExprAsync) {
        visit_expr_async(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_await(&mut self, i: &'ast crate::ExprAwait) {
        visit_expr_await(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_binary(&mut self, i: &'ast crate::ExprBinary) {
        visit_expr_binary(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_block(&mut self, i: &'ast crate::ExprBlock) {
        visit_expr_block(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_break(&mut self, i: &'ast crate::ExprBreak) {
        visit_expr_break(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_call(&mut self, i: &'ast crate::ExprCall) {
        visit_expr_call(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_cast(&mut self, i: &'ast crate::ExprCast) {
        visit_expr_cast(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_closure(&mut self, i: &'ast crate::ExprClosure) {
        visit_expr_closure(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_const(&mut self, i: &'ast crate::ExprConst) {
        visit_expr_const(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_continue(&mut self, i: &'ast crate::ExprContinue) {
        visit_expr_continue(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_field(&mut self, i: &'ast crate::ExprField) {
        visit_expr_field(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_for_loop(&mut self, i: &'ast crate::ExprForLoop) {
        visit_expr_for_loop(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_group(&mut self, i: &'ast crate::ExprGroup) {
        visit_expr_group(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_if(&mut self, i: &'ast crate::ExprIf) {
        visit_expr_if(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_index(&mut self, i: &'ast crate::ExprIndex) {
        visit_expr_index(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_infer(&mut self, i: &'ast crate::ExprInfer) {
        visit_expr_infer(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_let(&mut self, i: &'ast crate::ExprLet) {
        visit_expr_let(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_lit(&mut self, i: &'ast crate::ExprLit) {
        visit_expr_lit(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_loop(&mut self, i: &'ast crate::ExprLoop) {
        visit_expr_loop(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_macro(&mut self, i: &'ast crate::ExprMacro) {
        visit_expr_macro(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_match(&mut self, i: &'ast crate::ExprMatch) {
        visit_expr_match(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_method_call(&mut self, i: &'ast crate::ExprMethodCall) {
        visit_expr_method_call(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_paren(&mut self, i: &'ast crate::ExprParen) {
        visit_expr_paren(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_path(&mut self, i: &'ast crate::ExprPath) {
        visit_expr_path(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_range(&mut self, i: &'ast crate::ExprRange) {
        visit_expr_range(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_raw_addr(&mut self, i: &'ast crate::ExprRawAddr) {
        visit_expr_raw_addr(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_reference(&mut self, i: &'ast crate::ExprReference) {
        visit_expr_reference(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_repeat(&mut self, i: &'ast crate::ExprRepeat) {
        visit_expr_repeat(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_return(&mut self, i: &'ast crate::ExprReturn) {
        visit_expr_return(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_struct(&mut self, i: &'ast crate::ExprStruct) {
        visit_expr_struct(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_try(&mut self, i: &'ast crate::ExprTry) {
        visit_expr_try(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_try_block(&mut self, i: &'ast crate::ExprTryBlock) {
        visit_expr_try_block(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_tuple(&mut self, i: &'ast crate::ExprTuple) {
        visit_expr_tuple(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_unary(&mut self, i: &'ast crate::ExprUnary) {
        visit_expr_unary(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_unsafe(&mut self, i: &'ast crate::ExprUnsafe) {
        visit_expr_unsafe(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_while(&mut self, i: &'ast crate::ExprWhile) {
        visit_expr_while(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_yield(&mut self, i: &'ast crate::ExprYield) {
        visit_expr_yield(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_field(&mut self, i: &'ast crate::Field) {
        visit_field(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_field_mutability(&mut self, i: &'ast crate::FieldMutability) {
        visit_field_mutability(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_field_pat(&mut self, i: &'ast crate::FieldPat) {
        visit_field_pat(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_field_value(&mut self, i: &'ast crate::FieldValue) {
        visit_field_value(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_fields(&mut self, i: &'ast crate::Fields) {
        visit_fields(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_fields_named(&mut self, i: &'ast crate::FieldsNamed) {
        visit_fields_named(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_fields_unnamed(&mut self, i: &'ast crate::FieldsUnnamed) {
        visit_fields_unnamed(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_file(&mut self, i: &'ast crate::File) {
        visit_file(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_fn_arg(&mut self, i: &'ast crate::FnArg) {
        visit_fn_arg(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item(&mut self, i: &'ast crate::ForeignItem) {
        visit_foreign_item(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item_fn(&mut self, i: &'ast crate::ForeignItemFn) {
        visit_foreign_item_fn(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item_macro(&mut self, i: &'ast crate::ForeignItemMacro) {
        visit_foreign_item_macro(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item_static(&mut self, i: &'ast crate::ForeignItemStatic) {
        visit_foreign_item_static(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item_type(&mut self, i: &'ast crate::ForeignItemType) {
        visit_foreign_item_type(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_generic_argument(&mut self, i: &'ast crate::GenericArgument) {
        visit_generic_argument(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_generic_param(&mut self, i: &'ast crate::GenericParam) {
        visit_generic_param(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_generics(&mut self, i: &'ast crate::Generics) {
        visit_generics(self, i);
    }
    fn visit_ident(&mut self, i: &'ast proc_macro2::Ident) {
        visit_ident(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item(&mut self, i: &'ast crate::ImplItem) {
        visit_impl_item(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item_const(&mut self, i: &'ast crate::ImplItemConst) {
        visit_impl_item_const(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item_fn(&mut self, i: &'ast crate::ImplItemFn) {
        visit_impl_item_fn(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item_macro(&mut self, i: &'ast crate::ImplItemMacro) {
        visit_impl_item_macro(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item_type(&mut self, i: &'ast crate::ImplItemType) {
        visit_impl_item_type(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_restriction(&mut self, i: &'ast crate::ImplRestriction) {
        visit_impl_restriction(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_index(&mut self, i: &'ast crate::Index) {
        visit_index(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item(&mut self, i: &'ast crate::Item) {
        visit_item(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_const(&mut self, i: &'ast crate::ItemConst) {
        visit_item_const(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_enum(&mut self, i: &'ast crate::ItemEnum) {
        visit_item_enum(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_extern_crate(&mut self, i: &'ast crate::ItemExternCrate) {
        visit_item_extern_crate(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_fn(&mut self, i: &'ast crate::ItemFn) {
        visit_item_fn(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_foreign_mod(&mut self, i: &'ast crate::ItemForeignMod) {
        visit_item_foreign_mod(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_impl(&mut self, i: &'ast crate::ItemImpl) {
        visit_item_impl(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_macro(&mut self, i: &'ast crate::ItemMacro) {
        visit_item_macro(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_mod(&mut self, i: &'ast crate::ItemMod) {
        visit_item_mod(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_static(&mut self, i: &'ast crate::ItemStatic) {
        visit_item_static(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_struct(&mut self, i: &'ast crate::ItemStruct) {
        visit_item_struct(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_trait(&mut self, i: &'ast crate::ItemTrait) {
        visit_item_trait(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_trait_alias(&mut self, i: &'ast crate::ItemTraitAlias) {
        visit_item_trait_alias(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_type(&mut self, i: &'ast crate::ItemType) {
        visit_item_type(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_union(&mut self, i: &'ast crate::ItemUnion) {
        visit_item_union(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_use(&mut self, i: &'ast crate::ItemUse) {
        visit_item_use(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_label(&mut self, i: &'ast crate::Label) {
        visit_label(self, i);
    }
    fn visit_lifetime(&mut self, i: &'ast crate::Lifetime) {
        visit_lifetime(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_lifetime_param(&mut self, i: &'ast crate::LifetimeParam) {
        visit_lifetime_param(self, i);
    }
    fn visit_lit(&mut self, i: &'ast crate::Lit) {
        visit_lit(self, i);
    }
    fn visit_lit_bool(&mut self, i: &'ast crate::LitBool) {
        visit_lit_bool(self, i);
    }
    fn visit_lit_byte(&mut self, i: &'ast crate::LitByte) {
        visit_lit_byte(self, i);
    }
    fn visit_lit_byte_str(&mut self, i: &'ast crate::LitByteStr) {
        visit_lit_byte_str(self, i);
    }
    fn visit_lit_cstr(&mut self, i: &'ast crate::LitCStr) {
        visit_lit_cstr(self, i);
    }
    fn visit_lit_char(&mut self, i: &'ast crate::LitChar) {
        visit_lit_char(self, i);
    }
    fn visit_lit_float(&mut self, i: &'ast crate::LitFloat) {
        visit_lit_float(self, i);
    }
    fn visit_lit_int(&mut self, i: &'ast crate::LitInt) {
        visit_lit_int(self, i);
    }
    fn visit_lit_str(&mut self, i: &'ast crate::LitStr) {
        visit_lit_str(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_local(&mut self, i: &'ast crate::Local) {
        visit_local(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_local_init(&mut self, i: &'ast crate::LocalInit) {
        visit_local_init(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_macro(&mut self, i: &'ast crate::Macro) {
        visit_macro(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_macro_delimiter(&mut self, i: &'ast crate::MacroDelimiter) {
        visit_macro_delimiter(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_member(&mut self, i: &'ast crate::Member) {
        visit_member(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_meta(&mut self, i: &'ast crate::Meta) {
        visit_meta(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_meta_list(&mut self, i: &'ast crate::MetaList) {
        visit_meta_list(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_meta_name_value(&mut self, i: &'ast crate::MetaNameValue) {
        visit_meta_name_value(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_parenthesized_generic_arguments(
        &mut self,
        i: &'ast crate::ParenthesizedGenericArguments,
    ) {
        visit_parenthesized_generic_arguments(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat(&mut self, i: &'ast crate::Pat) {
        visit_pat(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_ident(&mut self, i: &'ast crate::PatIdent) {
        visit_pat_ident(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_or(&mut self, i: &'ast crate::PatOr) {
        visit_pat_or(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_paren(&mut self, i: &'ast crate::PatParen) {
        visit_pat_paren(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_reference(&mut self, i: &'ast crate::PatReference) {
        visit_pat_reference(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_rest(&mut self, i: &'ast crate::PatRest) {
        visit_pat_rest(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_slice(&mut self, i: &'ast crate::PatSlice) {
        visit_pat_slice(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_struct(&mut self, i: &'ast crate::PatStruct) {
        visit_pat_struct(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_tuple(&mut self, i: &'ast crate::PatTuple) {
        visit_pat_tuple(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_tuple_struct(&mut self, i: &'ast crate::PatTupleStruct) {
        visit_pat_tuple_struct(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_type(&mut self, i: &'ast crate::PatType) {
        visit_pat_type(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_wild(&mut self, i: &'ast crate::PatWild) {
        visit_pat_wild(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_path(&mut self, i: &'ast crate::Path) {
        visit_path(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_path_arguments(&mut self, i: &'ast crate::PathArguments) {
        visit_path_arguments(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_path_segment(&mut self, i: &'ast crate::PathSegment) {
        visit_path_segment(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pointer_mutability(&mut self, i: &'ast crate::PointerMutability) {
        visit_pointer_mutability(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_precise_capture(&mut self, i: &'ast crate::PreciseCapture) {
        visit_precise_capture(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_predicate_lifetime(&mut self, i: &'ast crate::PredicateLifetime) {
        visit_predicate_lifetime(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_predicate_type(&mut self, i: &'ast crate::PredicateType) {
        visit_predicate_type(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_qself(&mut self, i: &'ast crate::QSelf) {
        visit_qself(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_range_limits(&mut self, i: &'ast crate::RangeLimits) {
        visit_range_limits(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_receiver(&mut self, i: &'ast crate::Receiver) {
        visit_receiver(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_return_type(&mut self, i: &'ast crate::ReturnType) {
        visit_return_type(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_signature(&mut self, i: &'ast crate::Signature) {
        visit_signature(self, i);
    }
    fn visit_span(&mut self, i: &proc_macro2::Span) {}
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_static_mutability(&mut self, i: &'ast crate::StaticMutability) {
        visit_static_mutability(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_stmt(&mut self, i: &'ast crate::Stmt) {
        visit_stmt(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_stmt_macro(&mut self, i: &'ast crate::StmtMacro) {
        visit_stmt_macro(self, i);
    }
    fn visit_token_stream(&mut self, i: &'ast proc_macro2::TokenStream) {}
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_trait_bound(&mut self, i: &'ast crate::TraitBound) {
        visit_trait_bound(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_trait_bound_modifier(&mut self, i: &'ast crate::TraitBoundModifier) {
        visit_trait_bound_modifier(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item(&mut self, i: &'ast crate::TraitItem) {
        visit_trait_item(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item_const(&mut self, i: &'ast crate::TraitItemConst) {
        visit_trait_item_const(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item_fn(&mut self, i: &'ast crate::TraitItemFn) {
        visit_trait_item_fn(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item_macro(&mut self, i: &'ast crate::TraitItemMacro) {
        visit_trait_item_macro(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item_type(&mut self, i: &'ast crate::TraitItemType) {
        visit_trait_item_type(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type(&mut self, i: &'ast crate::Type) {
        visit_type(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_array(&mut self, i: &'ast crate::TypeArray) {
        visit_type_array(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_bare_fn(&mut self, i: &'ast crate::TypeBareFn) {
        visit_type_bare_fn(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_group(&mut self, i: &'ast crate::TypeGroup) {
        visit_type_group(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_impl_trait(&mut self, i: &'ast crate::TypeImplTrait) {
        visit_type_impl_trait(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_infer(&mut self, i: &'ast crate::TypeInfer) {
        visit_type_infer(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_macro(&mut self, i: &'ast crate::TypeMacro) {
        visit_type_macro(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_never(&mut self, i: &'ast crate::TypeNever) {
        visit_type_never(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_param(&mut self, i: &'ast crate::TypeParam) {
        visit_type_param(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_param_bound(&mut self, i: &'ast crate::TypeParamBound) {
        visit_type_param_bound(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_paren(&mut self, i: &'ast crate::TypeParen) {
        visit_type_paren(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_path(&mut self, i: &'ast crate::TypePath) {
        visit_type_path(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_ptr(&mut self, i: &'ast crate::TypePtr) {
        visit_type_ptr(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_reference(&mut self, i: &'ast crate::TypeReference) {
        visit_type_reference(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_slice(&mut self, i: &'ast crate::TypeSlice) {
        visit_type_slice(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_trait_object(&mut self, i: &'ast crate::TypeTraitObject) {
        visit_type_trait_object(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_tuple(&mut self, i: &'ast crate::TypeTuple) {
        visit_type_tuple(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_un_op(&mut self, i: &'ast crate::UnOp) {
        visit_un_op(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_glob(&mut self, i: &'ast crate::UseGlob) {
        visit_use_glob(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_group(&mut self, i: &'ast crate::UseGroup) {
        visit_use_group(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_name(&mut self, i: &'ast crate::UseName) {
        visit_use_name(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_path(&mut self, i: &'ast crate::UsePath) {
        visit_use_path(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_rename(&mut self, i: &'ast crate::UseRename) {
        visit_use_rename(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_tree(&mut self, i: &'ast crate::UseTree) {
        visit_use_tree(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_variadic(&mut self, i: &'ast crate::Variadic) {
        visit_variadic(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_variant(&mut self, i: &'ast crate::Variant) {
        visit_variant(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_vis_restricted(&mut self, i: &'ast crate::VisRestricted) {
        visit_vis_restricted(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_visibility(&mut self, i: &'ast crate::Visibility) {
        visit_visibility(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_where_clause(&mut self, i: &'ast crate::WhereClause) {
        visit_where_clause(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_where_predicate(&mut self, i: &'ast crate::WherePredicate) {
        visit_where_predicate(self, i);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_abi<'ast, V>(v: &mut V, node: &'ast crate::Abi)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.extern_token);
    if let Some(it) = &node.name {
        v.visit_lit_str(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_angle_bracketed_generic_arguments<'ast, V>(
    v: &mut V,
    node: &'ast crate::AngleBracketedGenericArguments,
)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.colon2_token);
    skip!(node.lt_token);
    for el in Punctuated::pairs(&node.args) {
        let it = el.value();
        v.visit_generic_argument(it);
    }
    skip!(node.gt_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_arm<'ast, V>(v: &mut V, node: &'ast crate::Arm)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_pat(&node.pat);
    if let Some(it) = &node.guard {
        skip!((it).0);
        v.visit_expr(&*(it).1);
    }
    skip!(node.fat_arrow_token);
    v.visit_expr(&*node.body);
    skip!(node.comma);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_assoc_const<'ast, V>(v: &mut V, node: &'ast crate::AssocConst)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_ident(&node.ident);
    if let Some(it) = &node.generics {
        v.visit_angle_bracketed_generic_arguments(it);
    }
    skip!(node.eq_token);
    v.visit_expr(&node.value);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_assoc_type<'ast, V>(v: &mut V, node: &'ast crate::AssocType)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_ident(&node.ident);
    if let Some(it) = &node.generics {
        v.visit_angle_bracketed_generic_arguments(it);
    }
    skip!(node.eq_token);
    v.visit_type(&node.ty);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_attr_style<'ast, V>(v: &mut V, node: &'ast crate::AttrStyle)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::AttrStyle::Outer => {}
        crate::AttrStyle::Inner(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_attribute<'ast, V>(v: &mut V, node: &'ast crate::Attribute)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.pound_token);
    v.visit_attr_style(&node.style);
    skip!(node.bracket_token);
    v.visit_meta(&node.meta);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_bare_fn_arg<'ast, V>(v: &mut V, node: &'ast crate::BareFnArg)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.name {
        v.visit_ident(&(it).0);
        skip!((it).1);
    }
    v.visit_type(&node.ty);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_bare_variadic<'ast, V>(v: &mut V, node: &'ast crate::BareVariadic)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.name {
        v.visit_ident(&(it).0);
        skip!((it).1);
    }
    skip!(node.dots);
    skip!(node.comma);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_bin_op<'ast, V>(v: &mut V, node: &'ast crate::BinOp)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::BinOp::Add(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Sub(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Mul(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Div(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Rem(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::And(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Or(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitXor(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitAnd(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitOr(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Shl(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Shr(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Eq(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Lt(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Le(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Ne(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Ge(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Gt(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::AddAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::SubAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::MulAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::DivAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::RemAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitXorAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitAndAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitOrAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::ShlAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::ShrAssign(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_block<'ast, V>(v: &mut V, node: &'ast crate::Block)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.brace_token);
    for it in &node.stmts {
        v.visit_stmt(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_bound_lifetimes<'ast, V>(v: &mut V, node: &'ast crate::BoundLifetimes)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.for_token);
    skip!(node.lt_token);
    for el in Punctuated::pairs(&node.lifetimes) {
        let it = el.value();
        v.visit_generic_param(it);
    }
    skip!(node.gt_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_captured_param<'ast, V>(v: &mut V, node: &'ast crate::CapturedParam)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::CapturedParam::Lifetime(_binding_0) => {
            v.visit_lifetime(_binding_0);
        }
        crate::CapturedParam::Ident(_binding_0) => {
            v.visit_ident(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_const_param<'ast, V>(v: &mut V, node: &'ast crate::ConstParam)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.const_token);
    v.visit_ident(&node.ident);
    skip!(node.colon_token);
    v.visit_type(&node.ty);
    skip!(node.eq_token);
    if let Some(it) = &node.default {
        v.visit_expr(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_constraint<'ast, V>(v: &mut V, node: &'ast crate::Constraint)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_ident(&node.ident);
    if let Some(it) = &node.generics {
        v.visit_angle_bracketed_generic_arguments(it);
    }
    skip!(node.colon_token);
    for el in Punctuated::pairs(&node.bounds) {
        let it = el.value();
        v.visit_type_param_bound(it);
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_data<'ast, V>(v: &mut V, node: &'ast crate::Data)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Data::Struct(_binding_0) => {
            v.visit_data_struct(_binding_0);
        }
        crate::Data::Enum(_binding_0) => {
            v.visit_data_enum(_binding_0);
        }
        crate::Data::Union(_binding_0) => {
            v.visit_data_union(_binding_0);
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_data_enum<'ast, V>(v: &mut V, node: &'ast crate::DataEnum)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.enum_token);
    skip!(node.brace_token);
    for el in Punctuated::pairs(&node.variants) {
        let it = el.value();
        v.visit_variant(it);
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_data_struct<'ast, V>(v: &mut V, node: &'ast crate::DataStruct)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.struct_token);
    v.visit_fields(&node.fields);
    skip!(node.semi_token);
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_data_union<'ast, V>(v: &mut V, node: &'ast crate::DataUnion)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.union_token);
    v.visit_fields_named(&node.fields);
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_derive_input<'ast, V>(v: &mut V, node: &'ast crate::DeriveInput)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    v.visit_data(&node.data);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr<'ast, V>(v: &mut V, node: &'ast crate::Expr)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Expr::Array(_binding_0) => {
            full!(v.visit_expr_array(_binding_0));
        }
        crate::Expr::Assign(_binding_0) => {
            full!(v.visit_expr_assign(_binding_0));
        }
        crate::Expr::Async(_binding_0) => {
            full!(v.visit_expr_async(_binding_0));
        }
        crate::Expr::Await(_binding_0) => {
            full!(v.visit_expr_await(_binding_0));
        }
        crate::Expr::Binary(_binding_0) => {
            v.visit_expr_binary(_binding_0);
        }
        crate::Expr::Block(_binding_0) => {
            full!(v.visit_expr_block(_binding_0));
        }
        crate::Expr::Break(_binding_0) => {
            full!(v.visit_expr_break(_binding_0));
        }
        crate::Expr::Call(_binding_0) => {
            v.visit_expr_call(_binding_0);
        }
        crate::Expr::Cast(_binding_0) => {
            v.visit_expr_cast(_binding_0);
        }
        crate::Expr::Closure(_binding_0) => {
            full!(v.visit_expr_closure(_binding_0));
        }
        crate::Expr::Const(_binding_0) => {
            full!(v.visit_expr_const(_binding_0));
        }
        crate::Expr::Continue(_binding_0) => {
            full!(v.visit_expr_continue(_binding_0));
        }
        crate::Expr::Field(_binding_0) => {
            v.visit_expr_field(_binding_0);
        }
        crate::Expr::ForLoop(_binding_0) => {
            full!(v.visit_expr_for_loop(_binding_0));
        }
        crate::Expr::Group(_binding_0) => {
            v.visit_expr_group(_binding_0);
        }
        crate::Expr::If(_binding_0) => {
            full!(v.visit_expr_if(_binding_0));
        }
        crate::Expr::Index(_binding_0) => {
            v.visit_expr_index(_binding_0);
        }
        crate::Expr::Infer(_binding_0) => {
            full!(v.visit_expr_infer(_binding_0));
        }
        crate::Expr::Let(_binding_0) => {
            full!(v.visit_expr_let(_binding_0));
        }
        crate::Expr::Lit(_binding_0) => {
            v.visit_expr_lit(_binding_0);
        }
        crate::Expr::Loop(_binding_0) => {
            full!(v.visit_expr_loop(_binding_0));
        }
        crate::Expr::Macro(_binding_0) => {
            v.visit_expr_macro(_binding_0);
        }
        crate::Expr::Match(_binding_0) => {
            full!(v.visit_expr_match(_binding_0));
        }
        crate::Expr::MethodCall(_binding_0) => {
            v.visit_expr_method_call(_binding_0);
        }
        crate::Expr::Paren(_binding_0) => {
            v.visit_expr_paren(_binding_0);
        }
        crate::Expr::Path(_binding_0) => {
            v.visit_expr_path(_binding_0);
        }
        crate::Expr::Range(_binding_0) => {
            full!(v.visit_expr_range(_binding_0));
        }
        crate::Expr::RawAddr(_binding_0) => {
            full!(v.visit_expr_raw_addr(_binding_0));
        }
        crate::Expr::Reference(_binding_0) => {
            v.visit_expr_reference(_binding_0);
        }
        crate::Expr::Repeat(_binding_0) => {
            full!(v.visit_expr_repeat(_binding_0));
        }
        crate::Expr::Return(_binding_0) => {
            full!(v.visit_expr_return(_binding_0));
        }
        crate::Expr::Struct(_binding_0) => {
            v.visit_expr_struct(_binding_0);
        }
        crate::Expr::Try(_binding_0) => {
            full!(v.visit_expr_try(_binding_0));
        }
        crate::Expr::TryBlock(_binding_0) => {
            full!(v.visit_expr_try_block(_binding_0));
        }
        crate::Expr::Tuple(_binding_0) => {
            v.visit_expr_tuple(_binding_0);
        }
        crate::Expr::Unary(_binding_0) => {
            v.visit_expr_unary(_binding_0);
        }
        crate::Expr::Unsafe(_binding_0) => {
            full!(v.visit_expr_unsafe(_binding_0));
        }
        crate::Expr::Verbatim(_binding_0) => {
            v.visit_token_stream(_binding_0);
        }
        crate::Expr::While(_binding_0) => {
            full!(v.visit_expr_while(_binding_0));
        }
        crate::Expr::Yield(_binding_0) => {
            full!(v.visit_expr_yield(_binding_0));
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_array<'ast, V>(v: &mut V, node: &'ast crate::ExprArray)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.bracket_token);
    for el in Punctuated::pairs(&node.elems) {
        let it = el.value();
        v.visit_expr(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_assign<'ast, V>(v: &mut V, node: &'ast crate::ExprAssign)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_expr(&*node.left);
    skip!(node.eq_token);
    v.visit_expr(&*node.right);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_async<'ast, V>(v: &mut V, node: &'ast crate::ExprAsync)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.async_token);
    skip!(node.capture);
    v.visit_block(&node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_await<'ast, V>(v: &mut V, node: &'ast crate::ExprAwait)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_expr(&*node.base);
    skip!(node.dot_token);
    skip!(node.await_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_binary<'ast, V>(v: &mut V, node: &'ast crate::ExprBinary)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_expr(&*node.left);
    v.visit_bin_op(&node.op);
    v.visit_expr(&*node.right);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_block<'ast, V>(v: &mut V, node: &'ast crate::ExprBlock)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.label {
        v.visit_label(it);
    }
    v.visit_block(&node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_break<'ast, V>(v: &mut V, node: &'ast crate::ExprBreak)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.break_token);
    if let Some(it) = &node.label {
        v.visit_lifetime(it);
    }
    if let Some(it) = &node.expr {
        v.visit_expr(&**it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_call<'ast, V>(v: &mut V, node: &'ast crate::ExprCall)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_expr(&*node.func);
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.args) {
        let it = el.value();
        v.visit_expr(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_cast<'ast, V>(v: &mut V, node: &'ast crate::ExprCast)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_expr(&*node.expr);
    skip!(node.as_token);
    v.visit_type(&*node.ty);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_closure<'ast, V>(v: &mut V, node: &'ast crate::ExprClosure)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.lifetimes {
        v.visit_bound_lifetimes(it);
    }
    skip!(node.constness);
    skip!(node.movability);
    skip!(node.asyncness);
    skip!(node.capture);
    skip!(node.or1_token);
    for el in Punctuated::pairs(&node.inputs) {
        let it = el.value();
        v.visit_pat(it);
    }
    skip!(node.or2_token);
    v.visit_return_type(&node.output);
    v.visit_expr(&*node.body);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_const<'ast, V>(v: &mut V, node: &'ast crate::ExprConst)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.const_token);
    v.visit_block(&node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_continue<'ast, V>(v: &mut V, node: &'ast crate::ExprContinue)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.continue_token);
    if let Some(it) = &node.label {
        v.visit_lifetime(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_field<'ast, V>(v: &mut V, node: &'ast crate::ExprField)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_expr(&*node.base);
    skip!(node.dot_token);
    v.visit_member(&node.member);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_for_loop<'ast, V>(v: &mut V, node: &'ast crate::ExprForLoop)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.label {
        v.visit_label(it);
    }
    skip!(node.for_token);
    v.visit_pat(&*node.pat);
    skip!(node.in_token);
    v.visit_expr(&*node.expr);
    v.visit_block(&node.body);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_group<'ast, V>(v: &mut V, node: &'ast crate::ExprGroup)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.group_token);
    v.visit_expr(&*node.expr);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_if<'ast, V>(v: &mut V, node: &'ast crate::ExprIf)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.if_token);
    v.visit_expr(&*node.cond);
    v.visit_block(&node.then_branch);
    if let Some(it) = &node.else_branch {
        skip!((it).0);
        v.visit_expr(&*(it).1);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_index<'ast, V>(v: &mut V, node: &'ast crate::ExprIndex)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_expr(&*node.expr);
    skip!(node.bracket_token);
    v.visit_expr(&*node.index);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_infer<'ast, V>(v: &mut V, node: &'ast crate::ExprInfer)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.underscore_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_let<'ast, V>(v: &mut V, node: &'ast crate::ExprLet)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.let_token);
    v.visit_pat(&*node.pat);
    skip!(node.eq_token);
    v.visit_expr(&*node.expr);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_lit<'ast, V>(v: &mut V, node: &'ast crate::ExprLit)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_lit(&node.lit);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_loop<'ast, V>(v: &mut V, node: &'ast crate::ExprLoop)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.label {
        v.visit_label(it);
    }
    skip!(node.loop_token);
    v.visit_block(&node.body);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_macro<'ast, V>(v: &mut V, node: &'ast crate::ExprMacro)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_macro(&node.mac);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_match<'ast, V>(v: &mut V, node: &'ast crate::ExprMatch)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.match_token);
    v.visit_expr(&*node.expr);
    skip!(node.brace_token);
    for it in &node.arms {
        v.visit_arm(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_method_call<'ast, V>(v: &mut V, node: &'ast crate::ExprMethodCall)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_expr(&*node.receiver);
    skip!(node.dot_token);
    v.visit_ident(&node.method);
    if let Some(it) = &node.turbofish {
        v.visit_angle_bracketed_generic_arguments(it);
    }
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.args) {
        let it = el.value();
        v.visit_expr(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_paren<'ast, V>(v: &mut V, node: &'ast crate::ExprParen)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.paren_token);
    v.visit_expr(&*node.expr);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_path<'ast, V>(v: &mut V, node: &'ast crate::ExprPath)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.qself {
        v.visit_qself(it);
    }
    v.visit_path(&node.path);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_range<'ast, V>(v: &mut V, node: &'ast crate::ExprRange)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.start {
        v.visit_expr(&**it);
    }
    v.visit_range_limits(&node.limits);
    if let Some(it) = &node.end {
        v.visit_expr(&**it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_raw_addr<'ast, V>(v: &mut V, node: &'ast crate::ExprRawAddr)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.and_token);
    skip!(node.raw);
    v.visit_pointer_mutability(&node.mutability);
    v.visit_expr(&*node.expr);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_reference<'ast, V>(v: &mut V, node: &'ast crate::ExprReference)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.and_token);
    skip!(node.mutability);
    v.visit_expr(&*node.expr);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_repeat<'ast, V>(v: &mut V, node: &'ast crate::ExprRepeat)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.bracket_token);
    v.visit_expr(&*node.expr);
    skip!(node.semi_token);
    v.visit_expr(&*node.len);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_return<'ast, V>(v: &mut V, node: &'ast crate::ExprReturn)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.return_token);
    if let Some(it) = &node.expr {
        v.visit_expr(&**it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_struct<'ast, V>(v: &mut V, node: &'ast crate::ExprStruct)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.qself {
        v.visit_qself(it);
    }
    v.visit_path(&node.path);
    skip!(node.brace_token);
    for el in Punctuated::pairs(&node.fields) {
        let it = el.value();
        v.visit_field_value(it);
    }
    skip!(node.dot2_token);
    if let Some(it) = &node.rest {
        v.visit_expr(&**it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_try<'ast, V>(v: &mut V, node: &'ast crate::ExprTry)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_expr(&*node.expr);
    skip!(node.question_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_try_block<'ast, V>(v: &mut V, node: &'ast crate::ExprTryBlock)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.try_token);
    v.visit_block(&node.block);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_tuple<'ast, V>(v: &mut V, node: &'ast crate::ExprTuple)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.elems) {
        let it = el.value();
        v.visit_expr(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_unary<'ast, V>(v: &mut V, node: &'ast crate::ExprUnary)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_un_op(&node.op);
    v.visit_expr(&*node.expr);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_unsafe<'ast, V>(v: &mut V, node: &'ast crate::ExprUnsafe)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.unsafe_token);
    v.visit_block(&node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_while<'ast, V>(v: &mut V, node: &'ast crate::ExprWhile)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.label {
        v.visit_label(it);
    }
    skip!(node.while_token);
    v.visit_expr(&*node.cond);
    v.visit_block(&node.body);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_yield<'ast, V>(v: &mut V, node: &'ast crate::ExprYield)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.yield_token);
    if let Some(it) = &node.expr {
        v.visit_expr(&**it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_field<'ast, V>(v: &mut V, node: &'ast crate::Field)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    v.visit_field_mutability(&node.mutability);
    if let Some(it) = &node.ident {
        v.visit_ident(it);
    }
    skip!(node.colon_token);
    v.visit_type(&node.ty);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_field_mutability<'ast, V>(v: &mut V, node: &'ast crate::FieldMutability)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::FieldMutability::None => {}
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_field_pat<'ast, V>(v: &mut V, node: &'ast crate::FieldPat)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_member(&node.member);
    skip!(node.colon_token);
    v.visit_pat(&*node.pat);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_field_value<'ast, V>(v: &mut V, node: &'ast crate::FieldValue)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_member(&node.member);
    skip!(node.colon_token);
    v.visit_expr(&node.expr);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_fields<'ast, V>(v: &mut V, node: &'ast crate::Fields)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Fields::Named(_binding_0) => {
            v.visit_fields_named(_binding_0);
        }
        crate::Fields::Unnamed(_binding_0) => {
            v.visit_fields_unnamed(_binding_0);
        }
        crate::Fields::Unit => {}
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_fields_named<'ast, V>(v: &mut V, node: &'ast crate::FieldsNamed)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.brace_token);
    for el in Punctuated::pairs(&node.named) {
        let it = el.value();
        v.visit_field(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_fields_unnamed<'ast, V>(v: &mut V, node: &'ast crate::FieldsUnnamed)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.unnamed) {
        let it = el.value();
        v.visit_field(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_file<'ast, V>(v: &mut V, node: &'ast crate::File)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.shebang);
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    for it in &node.items {
        v.visit_item(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_fn_arg<'ast, V>(v: &mut V, node: &'ast crate::FnArg)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::FnArg::Receiver(_binding_0) => {
            v.visit_receiver(_binding_0);
        }
        crate::FnArg::Typed(_binding_0) => {
            v.visit_pat_type(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item<'ast, V>(v: &mut V, node: &'ast crate::ForeignItem)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::ForeignItem::Fn(_binding_0) => {
            v.visit_foreign_item_fn(_binding_0);
        }
        crate::ForeignItem::Static(_binding_0) => {
            v.visit_foreign_item_static(_binding_0);
        }
        crate::ForeignItem::Type(_binding_0) => {
            v.visit_foreign_item_type(_binding_0);
        }
        crate::ForeignItem::Macro(_binding_0) => {
            v.visit_foreign_item_macro(_binding_0);
        }
        crate::ForeignItem::Verbatim(_binding_0) => {
            v.visit_token_stream(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item_fn<'ast, V>(v: &mut V, node: &'ast crate::ForeignItemFn)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    v.visit_signature(&node.sig);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item_macro<'ast, V>(v: &mut V, node: &'ast crate::ForeignItemMacro)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_macro(&node.mac);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item_static<'ast, V>(
    v: &mut V,
    node: &'ast crate::ForeignItemStatic,
)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.static_token);
    v.visit_static_mutability(&node.mutability);
    v.visit_ident(&node.ident);
    skip!(node.colon_token);
    v.visit_type(&*node.ty);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item_type<'ast, V>(v: &mut V, node: &'ast crate::ForeignItemType)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.type_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.semi_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_generic_argument<'ast, V>(v: &mut V, node: &'ast crate::GenericArgument)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::GenericArgument::Lifetime(_binding_0) => {
            v.visit_lifetime(_binding_0);
        }
        crate::GenericArgument::Type(_binding_0) => {
            v.visit_type(_binding_0);
        }
        crate::GenericArgument::Const(_binding_0) => {
            v.visit_expr(_binding_0);
        }
        crate::GenericArgument::AssocType(_binding_0) => {
            v.visit_assoc_type(_binding_0);
        }
        crate::GenericArgument::AssocConst(_binding_0) => {
            v.visit_assoc_const(_binding_0);
        }
        crate::GenericArgument::Constraint(_binding_0) => {
            v.visit_constraint(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_generic_param<'ast, V>(v: &mut V, node: &'ast crate::GenericParam)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::GenericParam::Lifetime(_binding_0) => {
            v.visit_lifetime_param(_binding_0);
        }
        crate::GenericParam::Type(_binding_0) => {
            v.visit_type_param(_binding_0);
        }
        crate::GenericParam::Const(_binding_0) => {
            v.visit_const_param(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_generics<'ast, V>(v: &mut V, node: &'ast crate::Generics)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.lt_token);
    for el in Punctuated::pairs(&node.params) {
        let it = el.value();
        v.visit_generic_param(it);
    }
    skip!(node.gt_token);
    if let Some(it) = &node.where_clause {
        v.visit_where_clause(it);
    }
}
pub fn visit_ident<'ast, V>(v: &mut V, node: &'ast proc_macro2::Ident)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_span(&node.span());
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item<'ast, V>(v: &mut V, node: &'ast crate::ImplItem)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::ImplItem::Const(_binding_0) => {
            v.visit_impl_item_const(_binding_0);
        }
        crate::ImplItem::Fn(_binding_0) => {
            v.visit_impl_item_fn(_binding_0);
        }
        crate::ImplItem::Type(_binding_0) => {
            v.visit_impl_item_type(_binding_0);
        }
        crate::ImplItem::Macro(_binding_0) => {
            v.visit_impl_item_macro(_binding_0);
        }
        crate::ImplItem::Verbatim(_binding_0) => {
            v.visit_token_stream(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item_const<'ast, V>(v: &mut V, node: &'ast crate::ImplItemConst)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.defaultness);
    skip!(node.const_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.colon_token);
    v.visit_type(&node.ty);
    skip!(node.eq_token);
    v.visit_expr(&node.expr);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item_fn<'ast, V>(v: &mut V, node: &'ast crate::ImplItemFn)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.defaultness);
    v.visit_signature(&node.sig);
    v.visit_block(&node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item_macro<'ast, V>(v: &mut V, node: &'ast crate::ImplItemMacro)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_macro(&node.mac);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item_type<'ast, V>(v: &mut V, node: &'ast crate::ImplItemType)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.defaultness);
    skip!(node.type_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.eq_token);
    v.visit_type(&node.ty);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_restriction<'ast, V>(v: &mut V, node: &'ast crate::ImplRestriction)
where
    V: Visit<'ast> + ?Sized,
{
    match *node {}
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_index<'ast, V>(v: &mut V, node: &'ast crate::Index)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.index);
    v.visit_span(&node.span);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item<'ast, V>(v: &mut V, node: &'ast crate::Item)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Item::Const(_binding_0) => {
            v.visit_item_const(_binding_0);
        }
        crate::Item::Enum(_binding_0) => {
            v.visit_item_enum(_binding_0);
        }
        crate::Item::ExternCrate(_binding_0) => {
            v.visit_item_extern_crate(_binding_0);
        }
        crate::Item::Fn(_binding_0) => {
            v.visit_item_fn(_binding_0);
        }
        crate::Item::ForeignMod(_binding_0) => {
            v.visit_item_foreign_mod(_binding_0);
        }
        crate::Item::Impl(_binding_0) => {
            v.visit_item_impl(_binding_0);
        }
        crate::Item::Macro(_binding_0) => {
            v.visit_item_macro(_binding_0);
        }
        crate::Item::Mod(_binding_0) => {
            v.visit_item_mod(_binding_0);
        }
        crate::Item::Static(_binding_0) => {
            v.visit_item_static(_binding_0);
        }
        crate::Item::Struct(_binding_0) => {
            v.visit_item_struct(_binding_0);
        }
        crate::Item::Trait(_binding_0) => {
            v.visit_item_trait(_binding_0);
        }
        crate::Item::TraitAlias(_binding_0) => {
            v.visit_item_trait_alias(_binding_0);
        }
        crate::Item::Type(_binding_0) => {
            v.visit_item_type(_binding_0);
        }
        crate::Item::Union(_binding_0) => {
            v.visit_item_union(_binding_0);
        }
        crate::Item::Use(_binding_0) => {
            v.visit_item_use(_binding_0);
        }
        crate::Item::Verbatim(_binding_0) => {
            v.visit_token_stream(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_const<'ast, V>(v: &mut V, node: &'ast crate::ItemConst)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.const_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.colon_token);
    v.visit_type(&*node.ty);
    skip!(node.eq_token);
    v.visit_expr(&*node.expr);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_enum<'ast, V>(v: &mut V, node: &'ast crate::ItemEnum)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.enum_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.brace_token);
    for el in Punctuated::pairs(&node.variants) {
        let it = el.value();
        v.visit_variant(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_extern_crate<'ast, V>(v: &mut V, node: &'ast crate::ItemExternCrate)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.extern_token);
    skip!(node.crate_token);
    v.visit_ident(&node.ident);
    if let Some(it) = &node.rename {
        skip!((it).0);
        v.visit_ident(&(it).1);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_fn<'ast, V>(v: &mut V, node: &'ast crate::ItemFn)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    v.visit_signature(&node.sig);
    v.visit_block(&*node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_foreign_mod<'ast, V>(v: &mut V, node: &'ast crate::ItemForeignMod)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.unsafety);
    v.visit_abi(&node.abi);
    skip!(node.brace_token);
    for it in &node.items {
        v.visit_foreign_item(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_impl<'ast, V>(v: &mut V, node: &'ast crate::ItemImpl)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.defaultness);
    skip!(node.unsafety);
    skip!(node.impl_token);
    v.visit_generics(&node.generics);
    if let Some(it) = &node.trait_ {
        skip!((it).0);
        v.visit_path(&(it).1);
        skip!((it).2);
    }
    v.visit_type(&*node.self_ty);
    skip!(node.brace_token);
    for it in &node.items {
        v.visit_impl_item(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_macro<'ast, V>(v: &mut V, node: &'ast crate::ItemMacro)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.ident {
        v.visit_ident(it);
    }
    v.visit_macro(&node.mac);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_mod<'ast, V>(v: &mut V, node: &'ast crate::ItemMod)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.unsafety);
    skip!(node.mod_token);
    v.visit_ident(&node.ident);
    if let Some(it) = &node.content {
        skip!((it).0);
        for it in &(it).1 {
            v.visit_item(it);
        }
    }
    skip!(node.semi);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_static<'ast, V>(v: &mut V, node: &'ast crate::ItemStatic)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.static_token);
    v.visit_static_mutability(&node.mutability);
    v.visit_ident(&node.ident);
    skip!(node.colon_token);
    v.visit_type(&*node.ty);
    skip!(node.eq_token);
    v.visit_expr(&*node.expr);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_struct<'ast, V>(v: &mut V, node: &'ast crate::ItemStruct)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.struct_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    v.visit_fields(&node.fields);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_trait<'ast, V>(v: &mut V, node: &'ast crate::ItemTrait)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.unsafety);
    skip!(node.auto_token);
    if let Some(it) = &node.restriction {
        v.visit_impl_restriction(it);
    }
    skip!(node.trait_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.colon_token);
    for el in Punctuated::pairs(&node.supertraits) {
        let it = el.value();
        v.visit_type_param_bound(it);
    }
    skip!(node.brace_token);
    for it in &node.items {
        v.visit_trait_item(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_trait_alias<'ast, V>(v: &mut V, node: &'ast crate::ItemTraitAlias)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.trait_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.eq_token);
    for el in Punctuated::pairs(&node.bounds) {
        let it = el.value();
        v.visit_type_param_bound(it);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_type<'ast, V>(v: &mut V, node: &'ast crate::ItemType)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.type_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.eq_token);
    v.visit_type(&*node.ty);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_union<'ast, V>(v: &mut V, node: &'ast crate::ItemUnion)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.union_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    v.visit_fields_named(&node.fields);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_use<'ast, V>(v: &mut V, node: &'ast crate::ItemUse)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_visibility(&node.vis);
    skip!(node.use_token);
    skip!(node.leading_colon);
    v.visit_use_tree(&node.tree);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_label<'ast, V>(v: &mut V, node: &'ast crate::Label)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_lifetime(&node.name);
    skip!(node.colon_token);
}
pub fn visit_lifetime<'ast, V>(v: &mut V, node: &'ast crate::Lifetime)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_span(&node.apostrophe);
    v.visit_ident(&node.ident);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_lifetime_param<'ast, V>(v: &mut V, node: &'ast crate::LifetimeParam)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_lifetime(&node.lifetime);
    skip!(node.colon_token);
    for el in Punctuated::pairs(&node.bounds) {
        let it = el.value();
        v.visit_lifetime(it);
    }
}
pub fn visit_lit<'ast, V>(v: &mut V, node: &'ast crate::Lit)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Lit::Str(_binding_0) => {
            v.visit_lit_str(_binding_0);
        }
        crate::Lit::ByteStr(_binding_0) => {
            v.visit_lit_byte_str(_binding_0);
        }
        crate::Lit::CStr(_binding_0) => {
            v.visit_lit_cstr(_binding_0);
        }
        crate::Lit::Byte(_binding_0) => {
            v.visit_lit_byte(_binding_0);
        }
        crate::Lit::Char(_binding_0) => {
            v.visit_lit_char(_binding_0);
        }
        crate::Lit::Int(_binding_0) => {
            v.visit_lit_int(_binding_0);
        }
        crate::Lit::Float(_binding_0) => {
            v.visit_lit_float(_binding_0);
        }
        crate::Lit::Bool(_binding_0) => {
            v.visit_lit_bool(_binding_0);
        }
        crate::Lit::Verbatim(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
pub fn visit_lit_bool<'ast, V>(v: &mut V, node: &'ast crate::LitBool)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.value);
    v.visit_span(&node.span);
}
pub fn visit_lit_byte<'ast, V>(v: &mut V, node: &'ast crate::LitByte)
where
    V: Visit<'ast> + ?Sized,
{}
pub fn visit_lit_byte_str<'ast, V>(v: &mut V, node: &'ast crate::LitByteStr)
where
    V: Visit<'ast> + ?Sized,
{}
pub fn visit_lit_cstr<'ast, V>(v: &mut V, node: &'ast crate::LitCStr)
where
    V: Visit<'ast> + ?Sized,
{}
pub fn visit_lit_char<'ast, V>(v: &mut V, node: &'ast crate::LitChar)
where
    V: Visit<'ast> + ?Sized,
{}
pub fn visit_lit_float<'ast, V>(v: &mut V, node: &'ast crate::LitFloat)
where
    V: Visit<'ast> + ?Sized,
{}
pub fn visit_lit_int<'ast, V>(v: &mut V, node: &'ast crate::LitInt)
where
    V: Visit<'ast> + ?Sized,
{}
pub fn visit_lit_str<'ast, V>(v: &mut V, node: &'ast crate::LitStr)
where
    V: Visit<'ast> + ?Sized,
{}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_local<'ast, V>(v: &mut V, node: &'ast crate::Local)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.let_token);
    v.visit_pat(&node.pat);
    if let Some(it) = &node.init {
        v.visit_local_init(it);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_local_init<'ast, V>(v: &mut V, node: &'ast crate::LocalInit)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.eq_token);
    v.visit_expr(&*node.expr);
    if let Some(it) = &node.diverge {
        skip!((it).0);
        v.visit_expr(&*(it).1);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_macro<'ast, V>(v: &mut V, node: &'ast crate::Macro)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_path(&node.path);
    skip!(node.bang_token);
    v.visit_macro_delimiter(&node.delimiter);
    v.visit_token_stream(&node.tokens);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_macro_delimiter<'ast, V>(v: &mut V, node: &'ast crate::MacroDelimiter)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::MacroDelimiter::Paren(_binding_0) => {
            skip!(_binding_0);
        }
        crate::MacroDelimiter::Brace(_binding_0) => {
            skip!(_binding_0);
        }
        crate::MacroDelimiter::Bracket(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_member<'ast, V>(v: &mut V, node: &'ast crate::Member)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Member::Named(_binding_0) => {
            v.visit_ident(_binding_0);
        }
        crate::Member::Unnamed(_binding_0) => {
            v.visit_index(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_meta<'ast, V>(v: &mut V, node: &'ast crate::Meta)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Meta::Path(_binding_0) => {
            v.visit_path(_binding_0);
        }
        crate::Meta::List(_binding_0) => {
            v.visit_meta_list(_binding_0);
        }
        crate::Meta::NameValue(_binding_0) => {
            v.visit_meta_name_value(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_meta_list<'ast, V>(v: &mut V, node: &'ast crate::MetaList)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_path(&node.path);
    v.visit_macro_delimiter(&node.delimiter);
    v.visit_token_stream(&node.tokens);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_meta_name_value<'ast, V>(v: &mut V, node: &'ast crate::MetaNameValue)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_path(&node.path);
    skip!(node.eq_token);
    v.visit_expr(&node.value);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_parenthesized_generic_arguments<'ast, V>(
    v: &mut V,
    node: &'ast crate::ParenthesizedGenericArguments,
)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.inputs) {
        let it = el.value();
        v.visit_type(it);
    }
    v.visit_return_type(&node.output);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat<'ast, V>(v: &mut V, node: &'ast crate::Pat)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Pat::Const(_binding_0) => {
            v.visit_expr_const(_binding_0);
        }
        crate::Pat::Ident(_binding_0) => {
            v.visit_pat_ident(_binding_0);
        }
        crate::Pat::Lit(_binding_0) => {
            v.visit_expr_lit(_binding_0);
        }
        crate::Pat::Macro(_binding_0) => {
            v.visit_expr_macro(_binding_0);
        }
        crate::Pat::Or(_binding_0) => {
            v.visit_pat_or(_binding_0);
        }
        crate::Pat::Paren(_binding_0) => {
            v.visit_pat_paren(_binding_0);
        }
        crate::Pat::Path(_binding_0) => {
            v.visit_expr_path(_binding_0);
        }
        crate::Pat::Range(_binding_0) => {
            v.visit_expr_range(_binding_0);
        }
        crate::Pat::Reference(_binding_0) => {
            v.visit_pat_reference(_binding_0);
        }
        crate::Pat::Rest(_binding_0) => {
            v.visit_pat_rest(_binding_0);
        }
        crate::Pat::Slice(_binding_0) => {
            v.visit_pat_slice(_binding_0);
        }
        crate::Pat::Struct(_binding_0) => {
            v.visit_pat_struct(_binding_0);
        }
        crate::Pat::Tuple(_binding_0) => {
            v.visit_pat_tuple(_binding_0);
        }
        crate::Pat::TupleStruct(_binding_0) => {
            v.visit_pat_tuple_struct(_binding_0);
        }
        crate::Pat::Type(_binding_0) => {
            v.visit_pat_type(_binding_0);
        }
        crate::Pat::Verbatim(_binding_0) => {
            v.visit_token_stream(_binding_0);
        }
        crate::Pat::Wild(_binding_0) => {
            v.visit_pat_wild(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_ident<'ast, V>(v: &mut V, node: &'ast crate::PatIdent)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.by_ref);
    skip!(node.mutability);
    v.visit_ident(&node.ident);
    if let Some(it) = &node.subpat {
        skip!((it).0);
        v.visit_pat(&*(it).1);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_or<'ast, V>(v: &mut V, node: &'ast crate::PatOr)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.leading_vert);
    for el in Punctuated::pairs(&node.cases) {
        let it = el.value();
        v.visit_pat(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_paren<'ast, V>(v: &mut V, node: &'ast crate::PatParen)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.paren_token);
    v.visit_pat(&*node.pat);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_reference<'ast, V>(v: &mut V, node: &'ast crate::PatReference)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.and_token);
    skip!(node.mutability);
    v.visit_pat(&*node.pat);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_rest<'ast, V>(v: &mut V, node: &'ast crate::PatRest)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.dot2_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_slice<'ast, V>(v: &mut V, node: &'ast crate::PatSlice)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.bracket_token);
    for el in Punctuated::pairs(&node.elems) {
        let it = el.value();
        v.visit_pat(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_struct<'ast, V>(v: &mut V, node: &'ast crate::PatStruct)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.qself {
        v.visit_qself(it);
    }
    v.visit_path(&node.path);
    skip!(node.brace_token);
    for el in Punctuated::pairs(&node.fields) {
        let it = el.value();
        v.visit_field_pat(it);
    }
    if let Some(it) = &node.rest {
        v.visit_pat_rest(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_tuple<'ast, V>(v: &mut V, node: &'ast crate::PatTuple)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.elems) {
        let it = el.value();
        v.visit_pat(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_tuple_struct<'ast, V>(v: &mut V, node: &'ast crate::PatTupleStruct)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.qself {
        v.visit_qself(it);
    }
    v.visit_path(&node.path);
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.elems) {
        let it = el.value();
        v.visit_pat(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_type<'ast, V>(v: &mut V, node: &'ast crate::PatType)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_pat(&*node.pat);
    skip!(node.colon_token);
    v.visit_type(&*node.ty);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_wild<'ast, V>(v: &mut V, node: &'ast crate::PatWild)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.underscore_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_path<'ast, V>(v: &mut V, node: &'ast crate::Path)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.leading_colon);
    for el in Punctuated::pairs(&node.segments) {
        let it = el.value();
        v.visit_path_segment(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_path_arguments<'ast, V>(v: &mut V, node: &'ast crate::PathArguments)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::PathArguments::None => {}
        crate::PathArguments::AngleBracketed(_binding_0) => {
            v.visit_angle_bracketed_generic_arguments(_binding_0);
        }
        crate::PathArguments::Parenthesized(_binding_0) => {
            v.visit_parenthesized_generic_arguments(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_path_segment<'ast, V>(v: &mut V, node: &'ast crate::PathSegment)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_ident(&node.ident);
    v.visit_path_arguments(&node.arguments);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pointer_mutability<'ast, V>(v: &mut V, node: &'ast crate::PointerMutability)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::PointerMutability::Const(_binding_0) => {
            skip!(_binding_0);
        }
        crate::PointerMutability::Mut(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_precise_capture<'ast, V>(v: &mut V, node: &'ast crate::PreciseCapture)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.use_token);
    skip!(node.lt_token);
    for el in Punctuated::pairs(&node.params) {
        let it = el.value();
        v.visit_captured_param(it);
    }
    skip!(node.gt_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_predicate_lifetime<'ast, V>(v: &mut V, node: &'ast crate::PredicateLifetime)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_lifetime(&node.lifetime);
    skip!(node.colon_token);
    for el in Punctuated::pairs(&node.bounds) {
        let it = el.value();
        v.visit_lifetime(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_predicate_type<'ast, V>(v: &mut V, node: &'ast crate::PredicateType)
where
    V: Visit<'ast> + ?Sized,
{
    if let Some(it) = &node.lifetimes {
        v.visit_bound_lifetimes(it);
    }
    v.visit_type(&node.bounded_ty);
    skip!(node.colon_token);
    for el in Punctuated::pairs(&node.bounds) {
        let it = el.value();
        v.visit_type_param_bound(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_qself<'ast, V>(v: &mut V, node: &'ast crate::QSelf)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.lt_token);
    v.visit_type(&*node.ty);
    skip!(node.position);
    skip!(node.as_token);
    skip!(node.gt_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_range_limits<'ast, V>(v: &mut V, node: &'ast crate::RangeLimits)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::RangeLimits::HalfOpen(_binding_0) => {
            skip!(_binding_0);
        }
        crate::RangeLimits::Closed(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_receiver<'ast, V>(v: &mut V, node: &'ast crate::Receiver)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.reference {
        skip!((it).0);
        if let Some(it) = &(it).1 {
            v.visit_lifetime(it);
        }
    }
    skip!(node.mutability);
    skip!(node.self_token);
    skip!(node.colon_token);
    v.visit_type(&*node.ty);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_return_type<'ast, V>(v: &mut V, node: &'ast crate::ReturnType)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::ReturnType::Default => {}
        crate::ReturnType::Type(_binding_0, _binding_1) => {
            skip!(_binding_0);
            v.visit_type(&**_binding_1);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_signature<'ast, V>(v: &mut V, node: &'ast crate::Signature)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.constness);
    skip!(node.asyncness);
    skip!(node.unsafety);
    if let Some(it) = &node.abi {
        v.visit_abi(it);
    }
    skip!(node.fn_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.inputs) {
        let it = el.value();
        v.visit_fn_arg(it);
    }
    if let Some(it) = &node.variadic {
        v.visit_variadic(it);
    }
    v.visit_return_type(&node.output);
}
pub fn visit_span<'ast, V>(v: &mut V, node: &proc_macro2::Span)
where
    V: Visit<'ast> + ?Sized,
{}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_static_mutability<'ast, V>(v: &mut V, node: &'ast crate::StaticMutability)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::StaticMutability::Mut(_binding_0) => {
            skip!(_binding_0);
        }
        crate::StaticMutability::None => {}
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_stmt<'ast, V>(v: &mut V, node: &'ast crate::Stmt)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Stmt::Local(_binding_0) => {
            v.visit_local(_binding_0);
        }
        crate::Stmt::Item(_binding_0) => {
            v.visit_item(_binding_0);
        }
        crate::Stmt::Expr(_binding_0, _binding_1) => {
            v.visit_expr(_binding_0);
            skip!(_binding_1);
        }
        crate::Stmt::Macro(_binding_0) => {
            v.visit_stmt_macro(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_stmt_macro<'ast, V>(v: &mut V, node: &'ast crate::StmtMacro)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_macro(&node.mac);
    skip!(node.semi_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_trait_bound<'ast, V>(v: &mut V, node: &'ast crate::TraitBound)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.paren_token);
    v.visit_trait_bound_modifier(&node.modifier);
    if let Some(it) = &node.lifetimes {
        v.visit_bound_lifetimes(it);
    }
    v.visit_path(&node.path);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_trait_bound_modifier<'ast, V>(
    v: &mut V,
    node: &'ast crate::TraitBoundModifier,
)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::TraitBoundModifier::None => {}
        crate::TraitBoundModifier::Maybe(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item<'ast, V>(v: &mut V, node: &'ast crate::TraitItem)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::TraitItem::Const(_binding_0) => {
            v.visit_trait_item_const(_binding_0);
        }
        crate::TraitItem::Fn(_binding_0) => {
            v.visit_trait_item_fn(_binding_0);
        }
        crate::TraitItem::Type(_binding_0) => {
            v.visit_trait_item_type(_binding_0);
        }
        crate::TraitItem::Macro(_binding_0) => {
            v.visit_trait_item_macro(_binding_0);
        }
        crate::TraitItem::Verbatim(_binding_0) => {
            v.visit_token_stream(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item_const<'ast, V>(v: &mut V, node: &'ast crate::TraitItemConst)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.const_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.colon_token);
    v.visit_type(&node.ty);
    if let Some(it) = &node.default {
        skip!((it).0);
        v.visit_expr(&(it).1);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item_fn<'ast, V>(v: &mut V, node: &'ast crate::TraitItemFn)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_signature(&node.sig);
    if let Some(it) = &node.default {
        v.visit_block(it);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item_macro<'ast, V>(v: &mut V, node: &'ast crate::TraitItemMacro)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_macro(&node.mac);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item_type<'ast, V>(v: &mut V, node: &'ast crate::TraitItemType)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    skip!(node.type_token);
    v.visit_ident(&node.ident);
    v.visit_generics(&node.generics);
    skip!(node.colon_token);
    for el in Punctuated::pairs(&node.bounds) {
        let it = el.value();
        v.visit_type_param_bound(it);
    }
    if let Some(it) = &node.default {
        skip!((it).0);
        v.visit_type(&(it).1);
    }
    skip!(node.semi_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type<'ast, V>(v: &mut V, node: &'ast crate::Type)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Type::Array(_binding_0) => {
            v.visit_type_array(_binding_0);
        }
        crate::Type::BareFn(_binding_0) => {
            v.visit_type_bare_fn(_binding_0);
        }
        crate::Type::Group(_binding_0) => {
            v.visit_type_group(_binding_0);
        }
        crate::Type::ImplTrait(_binding_0) => {
            v.visit_type_impl_trait(_binding_0);
        }
        crate::Type::Infer(_binding_0) => {
            v.visit_type_infer(_binding_0);
        }
        crate::Type::Macro(_binding_0) => {
            v.visit_type_macro(_binding_0);
        }
        crate::Type::Never(_binding_0) => {
            v.visit_type_never(_binding_0);
        }
        crate::Type::Paren(_binding_0) => {
            v.visit_type_paren(_binding_0);
        }
        crate::Type::Path(_binding_0) => {
            v.visit_type_path(_binding_0);
        }
        crate::Type::Ptr(_binding_0) => {
            v.visit_type_ptr(_binding_0);
        }
        crate::Type::Reference(_binding_0) => {
            v.visit_type_reference(_binding_0);
        }
        crate::Type::Slice(_binding_0) => {
            v.visit_type_slice(_binding_0);
        }
        crate::Type::TraitObject(_binding_0) => {
            v.visit_type_trait_object(_binding_0);
        }
        crate::Type::Tuple(_binding_0) => {
            v.visit_type_tuple(_binding_0);
        }
        crate::Type::Verbatim(_binding_0) => {
            v.visit_token_stream(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_array<'ast, V>(v: &mut V, node: &'ast crate::TypeArray)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.bracket_token);
    v.visit_type(&*node.elem);
    skip!(node.semi_token);
    v.visit_expr(&node.len);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_bare_fn<'ast, V>(v: &mut V, node: &'ast crate::TypeBareFn)
where
    V: Visit<'ast> + ?Sized,
{
    if let Some(it) = &node.lifetimes {
        v.visit_bound_lifetimes(it);
    }
    skip!(node.unsafety);
    if let Some(it) = &node.abi {
        v.visit_abi(it);
    }
    skip!(node.fn_token);
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.inputs) {
        let it = el.value();
        v.visit_bare_fn_arg(it);
    }
    if let Some(it) = &node.variadic {
        v.visit_bare_variadic(it);
    }
    v.visit_return_type(&node.output);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_group<'ast, V>(v: &mut V, node: &'ast crate::TypeGroup)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.group_token);
    v.visit_type(&*node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_impl_trait<'ast, V>(v: &mut V, node: &'ast crate::TypeImplTrait)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.impl_token);
    for el in Punctuated::pairs(&node.bounds) {
        let it = el.value();
        v.visit_type_param_bound(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_infer<'ast, V>(v: &mut V, node: &'ast crate::TypeInfer)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.underscore_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_macro<'ast, V>(v: &mut V, node: &'ast crate::TypeMacro)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_macro(&node.mac);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_never<'ast, V>(v: &mut V, node: &'ast crate::TypeNever)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.bang_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_param<'ast, V>(v: &mut V, node: &'ast crate::TypeParam)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_ident(&node.ident);
    skip!(node.colon_token);
    for el in Punctuated::pairs(&node.bounds) {
        let it = el.value();
        v.visit_type_param_bound(it);
    }
    skip!(node.eq_token);
    if let Some(it) = &node.default {
        v.visit_type(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_param_bound<'ast, V>(v: &mut V, node: &'ast crate::TypeParamBound)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::TypeParamBound::Trait(_binding_0) => {
            v.visit_trait_bound(_binding_0);
        }
        crate::TypeParamBound::Lifetime(_binding_0) => {
            v.visit_lifetime(_binding_0);
        }
        crate::TypeParamBound::PreciseCapture(_binding_0) => {
            full!(v.visit_precise_capture(_binding_0));
        }
        crate::TypeParamBound::Verbatim(_binding_0) => {
            v.visit_token_stream(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_paren<'ast, V>(v: &mut V, node: &'ast crate::TypeParen)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.paren_token);
    v.visit_type(&*node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_path<'ast, V>(v: &mut V, node: &'ast crate::TypePath)
where
    V: Visit<'ast> + ?Sized,
{
    if let Some(it) = &node.qself {
        v.visit_qself(it);
    }
    v.visit_path(&node.path);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_ptr<'ast, V>(v: &mut V, node: &'ast crate::TypePtr)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.star_token);
    skip!(node.const_token);
    skip!(node.mutability);
    v.visit_type(&*node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_reference<'ast, V>(v: &mut V, node: &'ast crate::TypeReference)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.and_token);
    if let Some(it) = &node.lifetime {
        v.visit_lifetime(it);
    }
    skip!(node.mutability);
    v.visit_type(&*node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_slice<'ast, V>(v: &mut V, node: &'ast crate::TypeSlice)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.bracket_token);
    v.visit_type(&*node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_trait_object<'ast, V>(v: &mut V, node: &'ast crate::TypeTraitObject)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.dyn_token);
    for el in Punctuated::pairs(&node.bounds) {
        let it = el.value();
        v.visit_type_param_bound(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_tuple<'ast, V>(v: &mut V, node: &'ast crate::TypeTuple)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.paren_token);
    for el in Punctuated::pairs(&node.elems) {
        let it = el.value();
        v.visit_type(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_un_op<'ast, V>(v: &mut V, node: &'ast crate::UnOp)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::UnOp::Deref(_binding_0) => {
            skip!(_binding_0);
        }
        crate::UnOp::Not(_binding_0) => {
            skip!(_binding_0);
        }
        crate::UnOp::Neg(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_glob<'ast, V>(v: &mut V, node: &'ast crate::UseGlob)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.star_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_group<'ast, V>(v: &mut V, node: &'ast crate::UseGroup)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.brace_token);
    for el in Punctuated::pairs(&node.items) {
        let it = el.value();
        v.visit_use_tree(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_name<'ast, V>(v: &mut V, node: &'ast crate::UseName)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_ident(&node.ident);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_path<'ast, V>(v: &mut V, node: &'ast crate::UsePath)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_ident(&node.ident);
    skip!(node.colon2_token);
    v.visit_use_tree(&*node.tree);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_rename<'ast, V>(v: &mut V, node: &'ast crate::UseRename)
where
    V: Visit<'ast> + ?Sized,
{
    v.visit_ident(&node.ident);
    skip!(node.as_token);
    v.visit_ident(&node.rename);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_tree<'ast, V>(v: &mut V, node: &'ast crate::UseTree)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::UseTree::Path(_binding_0) => {
            v.visit_use_path(_binding_0);
        }
        crate::UseTree::Name(_binding_0) => {
            v.visit_use_name(_binding_0);
        }
        crate::UseTree::Rename(_binding_0) => {
            v.visit_use_rename(_binding_0);
        }
        crate::UseTree::Glob(_binding_0) => {
            v.visit_use_glob(_binding_0);
        }
        crate::UseTree::Group(_binding_0) => {
            v.visit_use_group(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_variadic<'ast, V>(v: &mut V, node: &'ast crate::Variadic)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    if let Some(it) = &node.pat {
        v.visit_pat(&*(it).0);
        skip!((it).1);
    }
    skip!(node.dots);
    skip!(node.comma);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_variant<'ast, V>(v: &mut V, node: &'ast crate::Variant)
where
    V: Visit<'ast> + ?Sized,
{
    for it in &node.attrs {
        v.visit_attribute(it);
    }
    v.visit_ident(&node.ident);
    v.visit_fields(&node.fields);
    if let Some(it) = &node.discriminant {
        skip!((it).0);
        v.visit_expr(&(it).1);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_vis_restricted<'ast, V>(v: &mut V, node: &'ast crate::VisRestricted)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.pub_token);
    skip!(node.paren_token);
    skip!(node.in_token);
    v.visit_path(&*node.path);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_visibility<'ast, V>(v: &mut V, node: &'ast crate::Visibility)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::Visibility::Public(_binding_0) => {
            skip!(_binding_0);
        }
        crate::Visibility::Restricted(_binding_0) => {
            v.visit_vis_restricted(_binding_0);
        }
        crate::Visibility::Inherited => {}
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_where_clause<'ast, V>(v: &mut V, node: &'ast crate::WhereClause)
where
    V: Visit<'ast> + ?Sized,
{
    skip!(node.where_token);
    for el in Punctuated::pairs(&node.predicates) {
        let it = el.value();
        v.visit_where_predicate(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_where_predicate<'ast, V>(v: &mut V, node: &'ast crate::WherePredicate)
where
    V: Visit<'ast> + ?Sized,
{
    match node {
        crate::WherePredicate::Lifetime(_binding_0) => {
            v.visit_predicate_lifetime(_binding_0);
        }
        crate::WherePredicate::Type(_binding_0) => {
            v.visit_predicate_type(_binding_0);
        }
    }
}
