// This file is @generated by syn-internal-codegen.
// It is not intended for manual editing.

#[cfg(any(feature = "derive", feature = "full"))]
use crate::tt::TokenStreamHelper;
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Abi {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Abi {
    fn eq(&self, other: &Self) -> bool {
        self.name == other.name
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::AngleBracketedGenericArguments {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::AngleBracketedGenericArguments {
    fn eq(&self, other: &Self) -> bool {
        self.colon2_token == other.colon2_token && self.args == other.args
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Arm {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Arm {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.pat == other.pat && self.guard == other.guard
            && self.body == other.body && self.comma == other.comma
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::AssocConst {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::AssocConst {
    fn eq(&self, other: &Self) -> bool {
        self.ident == other.ident && self.generics == other.generics
            && self.value == other.value
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::AssocType {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::AssocType {
    fn eq(&self, other: &Self) -> bool {
        self.ident == other.ident && self.generics == other.generics
            && self.ty == other.ty
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::AttrStyle {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::AttrStyle {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::AttrStyle::Outer, crate::AttrStyle::Outer) => true,
            (crate::AttrStyle::Inner(_), crate::AttrStyle::Inner(_)) => true,
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Attribute {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Attribute {
    fn eq(&self, other: &Self) -> bool {
        self.style == other.style && self.meta == other.meta
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::BareFnArg {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::BareFnArg {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.name == other.name && self.ty == other.ty
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::BareVariadic {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::BareVariadic {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.name == other.name && self.comma == other.comma
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::BinOp {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::BinOp {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::BinOp::Add(_), crate::BinOp::Add(_)) => true,
            (crate::BinOp::Sub(_), crate::BinOp::Sub(_)) => true,
            (crate::BinOp::Mul(_), crate::BinOp::Mul(_)) => true,
            (crate::BinOp::Div(_), crate::BinOp::Div(_)) => true,
            (crate::BinOp::Rem(_), crate::BinOp::Rem(_)) => true,
            (crate::BinOp::And(_), crate::BinOp::And(_)) => true,
            (crate::BinOp::Or(_), crate::BinOp::Or(_)) => true,
            (crate::BinOp::BitXor(_), crate::BinOp::BitXor(_)) => true,
            (crate::BinOp::BitAnd(_), crate::BinOp::BitAnd(_)) => true,
            (crate::BinOp::BitOr(_), crate::BinOp::BitOr(_)) => true,
            (crate::BinOp::Shl(_), crate::BinOp::Shl(_)) => true,
            (crate::BinOp::Shr(_), crate::BinOp::Shr(_)) => true,
            (crate::BinOp::Eq(_), crate::BinOp::Eq(_)) => true,
            (crate::BinOp::Lt(_), crate::BinOp::Lt(_)) => true,
            (crate::BinOp::Le(_), crate::BinOp::Le(_)) => true,
            (crate::BinOp::Ne(_), crate::BinOp::Ne(_)) => true,
            (crate::BinOp::Ge(_), crate::BinOp::Ge(_)) => true,
            (crate::BinOp::Gt(_), crate::BinOp::Gt(_)) => true,
            (crate::BinOp::AddAssign(_), crate::BinOp::AddAssign(_)) => true,
            (crate::BinOp::SubAssign(_), crate::BinOp::SubAssign(_)) => true,
            (crate::BinOp::MulAssign(_), crate::BinOp::MulAssign(_)) => true,
            (crate::BinOp::DivAssign(_), crate::BinOp::DivAssign(_)) => true,
            (crate::BinOp::RemAssign(_), crate::BinOp::RemAssign(_)) => true,
            (crate::BinOp::BitXorAssign(_), crate::BinOp::BitXorAssign(_)) => true,
            (crate::BinOp::BitAndAssign(_), crate::BinOp::BitAndAssign(_)) => true,
            (crate::BinOp::BitOrAssign(_), crate::BinOp::BitOrAssign(_)) => true,
            (crate::BinOp::ShlAssign(_), crate::BinOp::ShlAssign(_)) => true,
            (crate::BinOp::ShrAssign(_), crate::BinOp::ShrAssign(_)) => true,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Block {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Block {
    fn eq(&self, other: &Self) -> bool {
        self.stmts == other.stmts
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::BoundLifetimes {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::BoundLifetimes {
    fn eq(&self, other: &Self) -> bool {
        self.lifetimes == other.lifetimes
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::CapturedParam {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::CapturedParam {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (
                crate::CapturedParam::Lifetime(self0),
                crate::CapturedParam::Lifetime(other0),
            ) => self0 == other0,
            (crate::CapturedParam::Ident(self0), crate::CapturedParam::Ident(other0)) => {
                self0 == other0
            }
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ConstParam {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ConstParam {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.ident == other.ident && self.ty == other.ty
            && self.eq_token == other.eq_token && self.default == other.default
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Constraint {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Constraint {
    fn eq(&self, other: &Self) -> bool {
        self.ident == other.ident && self.generics == other.generics
            && self.bounds == other.bounds
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Data {}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Data {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::Data::Struct(self0), crate::Data::Struct(other0)) => self0 == other0,
            (crate::Data::Enum(self0), crate::Data::Enum(other0)) => self0 == other0,
            (crate::Data::Union(self0), crate::Data::Union(other0)) => self0 == other0,
            _ => false,
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::DataEnum {}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::DataEnum {
    fn eq(&self, other: &Self) -> bool {
        self.variants == other.variants
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::DataStruct {}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::DataStruct {
    fn eq(&self, other: &Self) -> bool {
        self.fields == other.fields && self.semi_token == other.semi_token
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::DataUnion {}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::DataUnion {
    fn eq(&self, other: &Self) -> bool {
        self.fields == other.fields
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::DeriveInput {}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::DeriveInput {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.ident == other.ident
            && self.generics == other.generics && self.data == other.data
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Expr {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Expr {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            #[cfg(feature = "full")]
            (crate::Expr::Array(self0), crate::Expr::Array(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Assign(self0), crate::Expr::Assign(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Async(self0), crate::Expr::Async(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Await(self0), crate::Expr::Await(other0)) => self0 == other0,
            (crate::Expr::Binary(self0), crate::Expr::Binary(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Block(self0), crate::Expr::Block(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Break(self0), crate::Expr::Break(other0)) => self0 == other0,
            (crate::Expr::Call(self0), crate::Expr::Call(other0)) => self0 == other0,
            (crate::Expr::Cast(self0), crate::Expr::Cast(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Closure(self0), crate::Expr::Closure(other0)) => {
                self0 == other0
            }
            #[cfg(feature = "full")]
            (crate::Expr::Const(self0), crate::Expr::Const(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Continue(self0), crate::Expr::Continue(other0)) => {
                self0 == other0
            }
            (crate::Expr::Field(self0), crate::Expr::Field(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::ForLoop(self0), crate::Expr::ForLoop(other0)) => {
                self0 == other0
            }
            (crate::Expr::Group(self0), crate::Expr::Group(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::If(self0), crate::Expr::If(other0)) => self0 == other0,
            (crate::Expr::Index(self0), crate::Expr::Index(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Infer(self0), crate::Expr::Infer(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Let(self0), crate::Expr::Let(other0)) => self0 == other0,
            (crate::Expr::Lit(self0), crate::Expr::Lit(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Loop(self0), crate::Expr::Loop(other0)) => self0 == other0,
            (crate::Expr::Macro(self0), crate::Expr::Macro(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Match(self0), crate::Expr::Match(other0)) => self0 == other0,
            (crate::Expr::MethodCall(self0), crate::Expr::MethodCall(other0)) => {
                self0 == other0
            }
            (crate::Expr::Paren(self0), crate::Expr::Paren(other0)) => self0 == other0,
            (crate::Expr::Path(self0), crate::Expr::Path(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Range(self0), crate::Expr::Range(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::RawAddr(self0), crate::Expr::RawAddr(other0)) => {
                self0 == other0
            }
            (crate::Expr::Reference(self0), crate::Expr::Reference(other0)) => {
                self0 == other0
            }
            #[cfg(feature = "full")]
            (crate::Expr::Repeat(self0), crate::Expr::Repeat(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Return(self0), crate::Expr::Return(other0)) => self0 == other0,
            (crate::Expr::Struct(self0), crate::Expr::Struct(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Try(self0), crate::Expr::Try(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::TryBlock(self0), crate::Expr::TryBlock(other0)) => {
                self0 == other0
            }
            (crate::Expr::Tuple(self0), crate::Expr::Tuple(other0)) => self0 == other0,
            (crate::Expr::Unary(self0), crate::Expr::Unary(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Unsafe(self0), crate::Expr::Unsafe(other0)) => self0 == other0,
            (crate::Expr::Verbatim(self0), crate::Expr::Verbatim(other0)) => {
                TokenStreamHelper(self0) == TokenStreamHelper(other0)
            }
            #[cfg(feature = "full")]
            (crate::Expr::While(self0), crate::Expr::While(other0)) => self0 == other0,
            #[cfg(feature = "full")]
            (crate::Expr::Yield(self0), crate::Expr::Yield(other0)) => self0 == other0,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprArray {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprArray {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.elems == other.elems
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprAssign {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprAssign {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.left == other.left && self.right == other.right
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprAsync {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprAsync {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.capture == other.capture
            && self.block == other.block
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprAwait {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprAwait {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.base == other.base
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprBinary {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprBinary {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.left == other.left && self.op == other.op
            && self.right == other.right
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprBlock {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprBlock {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.label == other.label
            && self.block == other.block
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprBreak {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprBreak {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.label == other.label && self.expr == other.expr
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprCall {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprCall {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.func == other.func && self.args == other.args
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprCast {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprCast {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.expr == other.expr && self.ty == other.ty
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprClosure {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprClosure {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.lifetimes == other.lifetimes
            && self.constness == other.constness && self.movability == other.movability
            && self.asyncness == other.asyncness && self.capture == other.capture
            && self.inputs == other.inputs && self.output == other.output
            && self.body == other.body
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprConst {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprConst {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.block == other.block
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprContinue {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprContinue {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.label == other.label
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprField {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprField {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.base == other.base
            && self.member == other.member
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprForLoop {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprForLoop {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.label == other.label && self.pat == other.pat
            && self.expr == other.expr && self.body == other.body
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprGroup {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprGroup {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.expr == other.expr
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprIf {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprIf {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.cond == other.cond
            && self.then_branch == other.then_branch
            && self.else_branch == other.else_branch
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprIndex {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprIndex {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.expr == other.expr && self.index == other.index
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprInfer {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprInfer {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprLet {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprLet {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.pat == other.pat && self.expr == other.expr
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprLit {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprLit {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.lit == other.lit
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprLoop {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprLoop {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.label == other.label && self.body == other.body
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprMacro {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprMacro {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.mac == other.mac
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprMatch {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprMatch {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.expr == other.expr && self.arms == other.arms
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprMethodCall {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprMethodCall {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.receiver == other.receiver
            && self.method == other.method && self.turbofish == other.turbofish
            && self.args == other.args
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprParen {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprParen {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.expr == other.expr
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprPath {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprPath {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.qself == other.qself && self.path == other.path
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprRange {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprRange {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.start == other.start
            && self.limits == other.limits && self.end == other.end
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprRawAddr {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprRawAddr {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.mutability == other.mutability
            && self.expr == other.expr
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprReference {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprReference {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.mutability == other.mutability
            && self.expr == other.expr
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprRepeat {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprRepeat {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.expr == other.expr && self.len == other.len
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprReturn {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprReturn {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.expr == other.expr
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprStruct {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprStruct {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.qself == other.qself && self.path == other.path
            && self.fields == other.fields && self.dot2_token == other.dot2_token
            && self.rest == other.rest
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprTry {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprTry {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.expr == other.expr
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprTryBlock {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprTryBlock {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.block == other.block
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprTuple {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprTuple {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.elems == other.elems
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprUnary {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprUnary {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.op == other.op && self.expr == other.expr
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprUnsafe {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprUnsafe {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.block == other.block
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprWhile {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprWhile {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.label == other.label && self.cond == other.cond
            && self.body == other.body
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ExprYield {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ExprYield {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.expr == other.expr
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Field {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Field {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis
            && self.mutability == other.mutability && self.ident == other.ident
            && self.colon_token == other.colon_token && self.ty == other.ty
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::FieldMutability {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::FieldMutability {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::FieldMutability::None, crate::FieldMutability::None) => true,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::FieldPat {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::FieldPat {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.member == other.member
            && self.colon_token == other.colon_token && self.pat == other.pat
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::FieldValue {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::FieldValue {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.member == other.member
            && self.colon_token == other.colon_token && self.expr == other.expr
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Fields {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Fields {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::Fields::Named(self0), crate::Fields::Named(other0)) => {
                self0 == other0
            }
            (crate::Fields::Unnamed(self0), crate::Fields::Unnamed(other0)) => {
                self0 == other0
            }
            (crate::Fields::Unit, crate::Fields::Unit) => true,
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::FieldsNamed {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::FieldsNamed {
    fn eq(&self, other: &Self) -> bool {
        self.named == other.named
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::FieldsUnnamed {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::FieldsUnnamed {
    fn eq(&self, other: &Self) -> bool {
        self.unnamed == other.unnamed
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::File {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::File {
    fn eq(&self, other: &Self) -> bool {
        self.shebang == other.shebang && self.attrs == other.attrs
            && self.items == other.items
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::FnArg {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::FnArg {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::FnArg::Receiver(self0), crate::FnArg::Receiver(other0)) => {
                self0 == other0
            }
            (crate::FnArg::Typed(self0), crate::FnArg::Typed(other0)) => self0 == other0,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ForeignItem {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ForeignItem {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::ForeignItem::Fn(self0), crate::ForeignItem::Fn(other0)) => {
                self0 == other0
            }
            (crate::ForeignItem::Static(self0), crate::ForeignItem::Static(other0)) => {
                self0 == other0
            }
            (crate::ForeignItem::Type(self0), crate::ForeignItem::Type(other0)) => {
                self0 == other0
            }
            (crate::ForeignItem::Macro(self0), crate::ForeignItem::Macro(other0)) => {
                self0 == other0
            }
            (
                crate::ForeignItem::Verbatim(self0),
                crate::ForeignItem::Verbatim(other0),
            ) => TokenStreamHelper(self0) == TokenStreamHelper(other0),
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ForeignItemFn {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ForeignItemFn {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.sig == other.sig
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ForeignItemMacro {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ForeignItemMacro {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.mac == other.mac
            && self.semi_token == other.semi_token
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ForeignItemStatic {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ForeignItemStatic {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis
            && self.mutability == other.mutability && self.ident == other.ident
            && self.ty == other.ty
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ForeignItemType {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ForeignItemType {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.ident == other.ident
            && self.generics == other.generics
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::GenericArgument {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::GenericArgument {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (
                crate::GenericArgument::Lifetime(self0),
                crate::GenericArgument::Lifetime(other0),
            ) => self0 == other0,
            (
                crate::GenericArgument::Type(self0),
                crate::GenericArgument::Type(other0),
            ) => self0 == other0,
            (
                crate::GenericArgument::Const(self0),
                crate::GenericArgument::Const(other0),
            ) => self0 == other0,
            (
                crate::GenericArgument::AssocType(self0),
                crate::GenericArgument::AssocType(other0),
            ) => self0 == other0,
            (
                crate::GenericArgument::AssocConst(self0),
                crate::GenericArgument::AssocConst(other0),
            ) => self0 == other0,
            (
                crate::GenericArgument::Constraint(self0),
                crate::GenericArgument::Constraint(other0),
            ) => self0 == other0,
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::GenericParam {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::GenericParam {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (
                crate::GenericParam::Lifetime(self0),
                crate::GenericParam::Lifetime(other0),
            ) => self0 == other0,
            (crate::GenericParam::Type(self0), crate::GenericParam::Type(other0)) => {
                self0 == other0
            }
            (crate::GenericParam::Const(self0), crate::GenericParam::Const(other0)) => {
                self0 == other0
            }
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Generics {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Generics {
    fn eq(&self, other: &Self) -> bool {
        self.lt_token == other.lt_token && self.params == other.params
            && self.gt_token == other.gt_token && self.where_clause == other.where_clause
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ImplItem {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ImplItem {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::ImplItem::Const(self0), crate::ImplItem::Const(other0)) => {
                self0 == other0
            }
            (crate::ImplItem::Fn(self0), crate::ImplItem::Fn(other0)) => self0 == other0,
            (crate::ImplItem::Type(self0), crate::ImplItem::Type(other0)) => {
                self0 == other0
            }
            (crate::ImplItem::Macro(self0), crate::ImplItem::Macro(other0)) => {
                self0 == other0
            }
            (crate::ImplItem::Verbatim(self0), crate::ImplItem::Verbatim(other0)) => {
                TokenStreamHelper(self0) == TokenStreamHelper(other0)
            }
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ImplItemConst {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ImplItemConst {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis
            && self.defaultness == other.defaultness && self.ident == other.ident
            && self.generics == other.generics && self.ty == other.ty
            && self.expr == other.expr
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ImplItemFn {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ImplItemFn {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis
            && self.defaultness == other.defaultness && self.sig == other.sig
            && self.block == other.block
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ImplItemMacro {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ImplItemMacro {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.mac == other.mac
            && self.semi_token == other.semi_token
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ImplItemType {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ImplItemType {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis
            && self.defaultness == other.defaultness && self.ident == other.ident
            && self.generics == other.generics && self.ty == other.ty
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ImplRestriction {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ImplRestriction {
    fn eq(&self, _other: &Self) -> bool {
        match *self {}
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Item {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Item {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::Item::Const(self0), crate::Item::Const(other0)) => self0 == other0,
            (crate::Item::Enum(self0), crate::Item::Enum(other0)) => self0 == other0,
            (crate::Item::ExternCrate(self0), crate::Item::ExternCrate(other0)) => {
                self0 == other0
            }
            (crate::Item::Fn(self0), crate::Item::Fn(other0)) => self0 == other0,
            (crate::Item::ForeignMod(self0), crate::Item::ForeignMod(other0)) => {
                self0 == other0
            }
            (crate::Item::Impl(self0), crate::Item::Impl(other0)) => self0 == other0,
            (crate::Item::Macro(self0), crate::Item::Macro(other0)) => self0 == other0,
            (crate::Item::Mod(self0), crate::Item::Mod(other0)) => self0 == other0,
            (crate::Item::Static(self0), crate::Item::Static(other0)) => self0 == other0,
            (crate::Item::Struct(self0), crate::Item::Struct(other0)) => self0 == other0,
            (crate::Item::Trait(self0), crate::Item::Trait(other0)) => self0 == other0,
            (crate::Item::TraitAlias(self0), crate::Item::TraitAlias(other0)) => {
                self0 == other0
            }
            (crate::Item::Type(self0), crate::Item::Type(other0)) => self0 == other0,
            (crate::Item::Union(self0), crate::Item::Union(other0)) => self0 == other0,
            (crate::Item::Use(self0), crate::Item::Use(other0)) => self0 == other0,
            (crate::Item::Verbatim(self0), crate::Item::Verbatim(other0)) => {
                TokenStreamHelper(self0) == TokenStreamHelper(other0)
            }
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemConst {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemConst {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.ident == other.ident
            && self.generics == other.generics && self.ty == other.ty
            && self.expr == other.expr
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemEnum {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemEnum {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.ident == other.ident
            && self.generics == other.generics && self.variants == other.variants
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemExternCrate {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemExternCrate {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.ident == other.ident
            && self.rename == other.rename
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemFn {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemFn {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.sig == other.sig
            && self.block == other.block
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemForeignMod {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemForeignMod {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.unsafety == other.unsafety
            && self.abi == other.abi && self.items == other.items
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemImpl {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemImpl {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.defaultness == other.defaultness
            && self.unsafety == other.unsafety && self.generics == other.generics
            && self.trait_ == other.trait_ && self.self_ty == other.self_ty
            && self.items == other.items
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemMacro {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemMacro {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.ident == other.ident && self.mac == other.mac
            && self.semi_token == other.semi_token
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemMod {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemMod {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis
            && self.unsafety == other.unsafety && self.ident == other.ident
            && self.content == other.content && self.semi == other.semi
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemStatic {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemStatic {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis
            && self.mutability == other.mutability && self.ident == other.ident
            && self.ty == other.ty && self.expr == other.expr
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemStruct {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemStruct {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.ident == other.ident
            && self.generics == other.generics && self.fields == other.fields
            && self.semi_token == other.semi_token
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemTrait {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemTrait {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis
            && self.unsafety == other.unsafety && self.auto_token == other.auto_token
            && self.restriction == other.restriction && self.ident == other.ident
            && self.generics == other.generics && self.colon_token == other.colon_token
            && self.supertraits == other.supertraits && self.items == other.items
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemTraitAlias {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemTraitAlias {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.ident == other.ident
            && self.generics == other.generics && self.bounds == other.bounds
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemType {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemType {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.ident == other.ident
            && self.generics == other.generics && self.ty == other.ty
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemUnion {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemUnion {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis && self.ident == other.ident
            && self.generics == other.generics && self.fields == other.fields
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ItemUse {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ItemUse {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.vis == other.vis
            && self.leading_colon == other.leading_colon && self.tree == other.tree
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Label {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Label {
    fn eq(&self, other: &Self) -> bool {
        self.name == other.name
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LifetimeParam {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::LifetimeParam {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.lifetime == other.lifetime
            && self.colon_token == other.colon_token && self.bounds == other.bounds
    }
}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Lit {}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Lit {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::Lit::Str(self0), crate::Lit::Str(other0)) => self0 == other0,
            (crate::Lit::ByteStr(self0), crate::Lit::ByteStr(other0)) => self0 == other0,
            (crate::Lit::CStr(self0), crate::Lit::CStr(other0)) => self0 == other0,
            (crate::Lit::Byte(self0), crate::Lit::Byte(other0)) => self0 == other0,
            (crate::Lit::Char(self0), crate::Lit::Char(other0)) => self0 == other0,
            (crate::Lit::Int(self0), crate::Lit::Int(other0)) => self0 == other0,
            (crate::Lit::Float(self0), crate::Lit::Float(other0)) => self0 == other0,
            (crate::Lit::Bool(self0), crate::Lit::Bool(other0)) => self0 == other0,
            (crate::Lit::Verbatim(self0), crate::Lit::Verbatim(other0)) => {
                self0.to_string() == other0.to_string()
            }
            _ => false,
        }
    }
}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LitBool {}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::LitBool {
    fn eq(&self, other: &Self) -> bool {
        self.value == other.value
    }
}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LitByte {}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LitByteStr {}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LitCStr {}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LitChar {}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LitFloat {}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LitInt {}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LitStr {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Local {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Local {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.pat == other.pat && self.init == other.init
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::LocalInit {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::LocalInit {
    fn eq(&self, other: &Self) -> bool {
        self.expr == other.expr && self.diverge == other.diverge
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Macro {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Macro {
    fn eq(&self, other: &Self) -> bool {
        self.path == other.path && self.delimiter == other.delimiter
            && TokenStreamHelper(&self.tokens) == TokenStreamHelper(&other.tokens)
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::MacroDelimiter {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::MacroDelimiter {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::MacroDelimiter::Paren(_), crate::MacroDelimiter::Paren(_)) => true,
            (crate::MacroDelimiter::Brace(_), crate::MacroDelimiter::Brace(_)) => true,
            (crate::MacroDelimiter::Bracket(_), crate::MacroDelimiter::Bracket(_)) => {
                true
            }
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Meta {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Meta {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::Meta::Path(self0), crate::Meta::Path(other0)) => self0 == other0,
            (crate::Meta::List(self0), crate::Meta::List(other0)) => self0 == other0,
            (crate::Meta::NameValue(self0), crate::Meta::NameValue(other0)) => {
                self0 == other0
            }
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::MetaList {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::MetaList {
    fn eq(&self, other: &Self) -> bool {
        self.path == other.path && self.delimiter == other.delimiter
            && TokenStreamHelper(&self.tokens) == TokenStreamHelper(&other.tokens)
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::MetaNameValue {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::MetaNameValue {
    fn eq(&self, other: &Self) -> bool {
        self.path == other.path && self.value == other.value
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ParenthesizedGenericArguments {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ParenthesizedGenericArguments {
    fn eq(&self, other: &Self) -> bool {
        self.inputs == other.inputs && self.output == other.output
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Pat {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Pat {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::Pat::Const(self0), crate::Pat::Const(other0)) => self0 == other0,
            (crate::Pat::Ident(self0), crate::Pat::Ident(other0)) => self0 == other0,
            (crate::Pat::Lit(self0), crate::Pat::Lit(other0)) => self0 == other0,
            (crate::Pat::Macro(self0), crate::Pat::Macro(other0)) => self0 == other0,
            (crate::Pat::Or(self0), crate::Pat::Or(other0)) => self0 == other0,
            (crate::Pat::Paren(self0), crate::Pat::Paren(other0)) => self0 == other0,
            (crate::Pat::Path(self0), crate::Pat::Path(other0)) => self0 == other0,
            (crate::Pat::Range(self0), crate::Pat::Range(other0)) => self0 == other0,
            (crate::Pat::Reference(self0), crate::Pat::Reference(other0)) => {
                self0 == other0
            }
            (crate::Pat::Rest(self0), crate::Pat::Rest(other0)) => self0 == other0,
            (crate::Pat::Slice(self0), crate::Pat::Slice(other0)) => self0 == other0,
            (crate::Pat::Struct(self0), crate::Pat::Struct(other0)) => self0 == other0,
            (crate::Pat::Tuple(self0), crate::Pat::Tuple(other0)) => self0 == other0,
            (crate::Pat::TupleStruct(self0), crate::Pat::TupleStruct(other0)) => {
                self0 == other0
            }
            (crate::Pat::Type(self0), crate::Pat::Type(other0)) => self0 == other0,
            (crate::Pat::Verbatim(self0), crate::Pat::Verbatim(other0)) => {
                TokenStreamHelper(self0) == TokenStreamHelper(other0)
            }
            (crate::Pat::Wild(self0), crate::Pat::Wild(other0)) => self0 == other0,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatIdent {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatIdent {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.by_ref == other.by_ref
            && self.mutability == other.mutability && self.ident == other.ident
            && self.subpat == other.subpat
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatOr {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatOr {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.leading_vert == other.leading_vert
            && self.cases == other.cases
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatParen {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatParen {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.pat == other.pat
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatReference {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatReference {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.mutability == other.mutability
            && self.pat == other.pat
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatRest {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatRest {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatSlice {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatSlice {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.elems == other.elems
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatStruct {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatStruct {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.qself == other.qself && self.path == other.path
            && self.fields == other.fields && self.rest == other.rest
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatTuple {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatTuple {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.elems == other.elems
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatTupleStruct {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatTupleStruct {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.qself == other.qself && self.path == other.path
            && self.elems == other.elems
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatType {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatType {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.pat == other.pat && self.ty == other.ty
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PatWild {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PatWild {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Path {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Path {
    fn eq(&self, other: &Self) -> bool {
        self.leading_colon == other.leading_colon && self.segments == other.segments
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PathArguments {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PathArguments {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::PathArguments::None, crate::PathArguments::None) => true,
            (
                crate::PathArguments::AngleBracketed(self0),
                crate::PathArguments::AngleBracketed(other0),
            ) => self0 == other0,
            (
                crate::PathArguments::Parenthesized(self0),
                crate::PathArguments::Parenthesized(other0),
            ) => self0 == other0,
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PathSegment {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PathSegment {
    fn eq(&self, other: &Self) -> bool {
        self.ident == other.ident && self.arguments == other.arguments
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PointerMutability {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PointerMutability {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::PointerMutability::Const(_), crate::PointerMutability::Const(_)) => {
                true
            }
            (crate::PointerMutability::Mut(_), crate::PointerMutability::Mut(_)) => true,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PreciseCapture {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PreciseCapture {
    fn eq(&self, other: &Self) -> bool {
        self.params == other.params
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PredicateLifetime {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PredicateLifetime {
    fn eq(&self, other: &Self) -> bool {
        self.lifetime == other.lifetime && self.bounds == other.bounds
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::PredicateType {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::PredicateType {
    fn eq(&self, other: &Self) -> bool {
        self.lifetimes == other.lifetimes && self.bounded_ty == other.bounded_ty
            && self.bounds == other.bounds
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::QSelf {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::QSelf {
    fn eq(&self, other: &Self) -> bool {
        self.ty == other.ty && self.position == other.position
            && self.as_token == other.as_token
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::RangeLimits {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::RangeLimits {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::RangeLimits::HalfOpen(_), crate::RangeLimits::HalfOpen(_)) => true,
            (crate::RangeLimits::Closed(_), crate::RangeLimits::Closed(_)) => true,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Receiver {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Receiver {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.reference == other.reference
            && self.mutability == other.mutability
            && self.colon_token == other.colon_token && self.ty == other.ty
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::ReturnType {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::ReturnType {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::ReturnType::Default, crate::ReturnType::Default) => true,
            (crate::ReturnType::Type(_, self1), crate::ReturnType::Type(_, other1)) => {
                self1 == other1
            }
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Signature {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Signature {
    fn eq(&self, other: &Self) -> bool {
        self.constness == other.constness && self.asyncness == other.asyncness
            && self.unsafety == other.unsafety && self.abi == other.abi
            && self.ident == other.ident && self.generics == other.generics
            && self.inputs == other.inputs && self.variadic == other.variadic
            && self.output == other.output
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::StaticMutability {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::StaticMutability {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::StaticMutability::Mut(_), crate::StaticMutability::Mut(_)) => true,
            (crate::StaticMutability::None, crate::StaticMutability::None) => true,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Stmt {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Stmt {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::Stmt::Local(self0), crate::Stmt::Local(other0)) => self0 == other0,
            (crate::Stmt::Item(self0), crate::Stmt::Item(other0)) => self0 == other0,
            (crate::Stmt::Expr(self0, self1), crate::Stmt::Expr(other0, other1)) => {
                self0 == other0 && self1 == other1
            }
            (crate::Stmt::Macro(self0), crate::Stmt::Macro(other0)) => self0 == other0,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::StmtMacro {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::StmtMacro {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.mac == other.mac
            && self.semi_token == other.semi_token
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TraitBound {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TraitBound {
    fn eq(&self, other: &Self) -> bool {
        self.paren_token == other.paren_token && self.modifier == other.modifier
            && self.lifetimes == other.lifetimes && self.path == other.path
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TraitBoundModifier {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TraitBoundModifier {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::TraitBoundModifier::None, crate::TraitBoundModifier::None) => true,
            (
                crate::TraitBoundModifier::Maybe(_),
                crate::TraitBoundModifier::Maybe(_),
            ) => true,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TraitItem {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TraitItem {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::TraitItem::Const(self0), crate::TraitItem::Const(other0)) => {
                self0 == other0
            }
            (crate::TraitItem::Fn(self0), crate::TraitItem::Fn(other0)) => {
                self0 == other0
            }
            (crate::TraitItem::Type(self0), crate::TraitItem::Type(other0)) => {
                self0 == other0
            }
            (crate::TraitItem::Macro(self0), crate::TraitItem::Macro(other0)) => {
                self0 == other0
            }
            (crate::TraitItem::Verbatim(self0), crate::TraitItem::Verbatim(other0)) => {
                TokenStreamHelper(self0) == TokenStreamHelper(other0)
            }
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TraitItemConst {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TraitItemConst {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.ident == other.ident
            && self.generics == other.generics && self.ty == other.ty
            && self.default == other.default
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TraitItemFn {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TraitItemFn {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.sig == other.sig
            && self.default == other.default && self.semi_token == other.semi_token
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TraitItemMacro {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TraitItemMacro {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.mac == other.mac
            && self.semi_token == other.semi_token
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TraitItemType {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TraitItemType {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.ident == other.ident
            && self.generics == other.generics && self.colon_token == other.colon_token
            && self.bounds == other.bounds && self.default == other.default
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Type {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Type {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::Type::Array(self0), crate::Type::Array(other0)) => self0 == other0,
            (crate::Type::BareFn(self0), crate::Type::BareFn(other0)) => self0 == other0,
            (crate::Type::Group(self0), crate::Type::Group(other0)) => self0 == other0,
            (crate::Type::ImplTrait(self0), crate::Type::ImplTrait(other0)) => {
                self0 == other0
            }
            (crate::Type::Infer(self0), crate::Type::Infer(other0)) => self0 == other0,
            (crate::Type::Macro(self0), crate::Type::Macro(other0)) => self0 == other0,
            (crate::Type::Never(self0), crate::Type::Never(other0)) => self0 == other0,
            (crate::Type::Paren(self0), crate::Type::Paren(other0)) => self0 == other0,
            (crate::Type::Path(self0), crate::Type::Path(other0)) => self0 == other0,
            (crate::Type::Ptr(self0), crate::Type::Ptr(other0)) => self0 == other0,
            (crate::Type::Reference(self0), crate::Type::Reference(other0)) => {
                self0 == other0
            }
            (crate::Type::Slice(self0), crate::Type::Slice(other0)) => self0 == other0,
            (crate::Type::TraitObject(self0), crate::Type::TraitObject(other0)) => {
                self0 == other0
            }
            (crate::Type::Tuple(self0), crate::Type::Tuple(other0)) => self0 == other0,
            (crate::Type::Verbatim(self0), crate::Type::Verbatim(other0)) => {
                TokenStreamHelper(self0) == TokenStreamHelper(other0)
            }
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeArray {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeArray {
    fn eq(&self, other: &Self) -> bool {
        self.elem == other.elem && self.len == other.len
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeBareFn {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeBareFn {
    fn eq(&self, other: &Self) -> bool {
        self.lifetimes == other.lifetimes && self.unsafety == other.unsafety
            && self.abi == other.abi && self.inputs == other.inputs
            && self.variadic == other.variadic && self.output == other.output
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeGroup {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeGroup {
    fn eq(&self, other: &Self) -> bool {
        self.elem == other.elem
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeImplTrait {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeImplTrait {
    fn eq(&self, other: &Self) -> bool {
        self.bounds == other.bounds
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeInfer {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeInfer {
    fn eq(&self, _other: &Self) -> bool {
        true
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeMacro {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeMacro {
    fn eq(&self, other: &Self) -> bool {
        self.mac == other.mac
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeNever {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeNever {
    fn eq(&self, _other: &Self) -> bool {
        true
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeParam {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeParam {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.ident == other.ident
            && self.colon_token == other.colon_token && self.bounds == other.bounds
            && self.eq_token == other.eq_token && self.default == other.default
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeParamBound {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeParamBound {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (
                crate::TypeParamBound::Trait(self0),
                crate::TypeParamBound::Trait(other0),
            ) => self0 == other0,
            (
                crate::TypeParamBound::Lifetime(self0),
                crate::TypeParamBound::Lifetime(other0),
            ) => self0 == other0,
            #[cfg(feature = "full")]
            (
                crate::TypeParamBound::PreciseCapture(self0),
                crate::TypeParamBound::PreciseCapture(other0),
            ) => self0 == other0,
            (
                crate::TypeParamBound::Verbatim(self0),
                crate::TypeParamBound::Verbatim(other0),
            ) => TokenStreamHelper(self0) == TokenStreamHelper(other0),
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeParen {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeParen {
    fn eq(&self, other: &Self) -> bool {
        self.elem == other.elem
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypePath {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypePath {
    fn eq(&self, other: &Self) -> bool {
        self.qself == other.qself && self.path == other.path
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypePtr {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypePtr {
    fn eq(&self, other: &Self) -> bool {
        self.const_token == other.const_token && self.mutability == other.mutability
            && self.elem == other.elem
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeReference {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeReference {
    fn eq(&self, other: &Self) -> bool {
        self.lifetime == other.lifetime && self.mutability == other.mutability
            && self.elem == other.elem
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeSlice {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeSlice {
    fn eq(&self, other: &Self) -> bool {
        self.elem == other.elem
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeTraitObject {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeTraitObject {
    fn eq(&self, other: &Self) -> bool {
        self.dyn_token == other.dyn_token && self.bounds == other.bounds
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::TypeTuple {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::TypeTuple {
    fn eq(&self, other: &Self) -> bool {
        self.elems == other.elems
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::UnOp {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::UnOp {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::UnOp::Deref(_), crate::UnOp::Deref(_)) => true,
            (crate::UnOp::Not(_), crate::UnOp::Not(_)) => true,
            (crate::UnOp::Neg(_), crate::UnOp::Neg(_)) => true,
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::UseGlob {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::UseGlob {
    fn eq(&self, _other: &Self) -> bool {
        true
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::UseGroup {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::UseGroup {
    fn eq(&self, other: &Self) -> bool {
        self.items == other.items
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::UseName {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::UseName {
    fn eq(&self, other: &Self) -> bool {
        self.ident == other.ident
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::UsePath {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::UsePath {
    fn eq(&self, other: &Self) -> bool {
        self.ident == other.ident && self.tree == other.tree
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::UseRename {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::UseRename {
    fn eq(&self, other: &Self) -> bool {
        self.ident == other.ident && self.rename == other.rename
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::UseTree {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::UseTree {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::UseTree::Path(self0), crate::UseTree::Path(other0)) => {
                self0 == other0
            }
            (crate::UseTree::Name(self0), crate::UseTree::Name(other0)) => {
                self0 == other0
            }
            (crate::UseTree::Rename(self0), crate::UseTree::Rename(other0)) => {
                self0 == other0
            }
            (crate::UseTree::Glob(self0), crate::UseTree::Glob(other0)) => {
                self0 == other0
            }
            (crate::UseTree::Group(self0), crate::UseTree::Group(other0)) => {
                self0 == other0
            }
            _ => false,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Variadic {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Variadic {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.pat == other.pat && self.comma == other.comma
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Variant {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Variant {
    fn eq(&self, other: &Self) -> bool {
        self.attrs == other.attrs && self.ident == other.ident
            && self.fields == other.fields && self.discriminant == other.discriminant
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::VisRestricted {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::VisRestricted {
    fn eq(&self, other: &Self) -> bool {
        self.in_token == other.in_token && self.path == other.path
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::Visibility {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::Visibility {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (crate::Visibility::Public(_), crate::Visibility::Public(_)) => true,
            (
                crate::Visibility::Restricted(self0),
                crate::Visibility::Restricted(other0),
            ) => self0 == other0,
            (crate::Visibility::Inherited, crate::Visibility::Inherited) => true,
            _ => false,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::WhereClause {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::WhereClause {
    fn eq(&self, other: &Self) -> bool {
        self.predicates == other.predicates
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Eq for crate::WherePredicate {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl PartialEq for crate::WherePredicate {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (
                crate::WherePredicate::Lifetime(self0),
                crate::WherePredicate::Lifetime(other0),
            ) => self0 == other0,
            (crate::WherePredicate::Type(self0), crate::WherePredicate::Type(other0)) => {
                self0 == other0
            }
            _ => false,
        }
    }
}
