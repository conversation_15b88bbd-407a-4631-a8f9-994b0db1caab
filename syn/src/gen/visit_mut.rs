// This file is @generated by syn-internal-codegen.
// It is not intended for manual editing.

#![allow(unused_variables)]
#![allow(clippy::needless_pass_by_ref_mut)]
#[cfg(any(feature = "full", feature = "derive"))]
use crate::punctuated::Punctuated;
#[cfg(feature = "full")]
macro_rules! full {
    ($e:expr) => {
        $e
    };
}
#[cfg(all(feature = "derive", not(feature = "full")))]
macro_rules! full {
    ($e:expr) => {
        unreachable!()
    };
}
macro_rules! skip {
    ($($tt:tt)*) => {};
}
/// Syntax tree traversal to mutate an exclusive borrow of a syntax tree in
/// place.
///
/// See the [module documentation] for details.
///
/// [module documentation]: self
pub trait VisitMut {
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_abi_mut(&mut self, i: &mut crate::Abi) {
        visit_abi_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_angle_bracketed_generic_arguments_mut(
        &mut self,
        i: &mut crate::AngleBracketedGenericArguments,
    ) {
        visit_angle_bracketed_generic_arguments_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_arm_mut(&mut self, i: &mut crate::Arm) {
        visit_arm_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_assoc_const_mut(&mut self, i: &mut crate::AssocConst) {
        visit_assoc_const_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_assoc_type_mut(&mut self, i: &mut crate::AssocType) {
        visit_assoc_type_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_attr_style_mut(&mut self, i: &mut crate::AttrStyle) {
        visit_attr_style_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_attribute_mut(&mut self, i: &mut crate::Attribute) {
        visit_attribute_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_attributes_mut(&mut self, i: &mut Vec<crate::Attribute>) {
        for attr in i {
            self.visit_attribute_mut(attr);
        }
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_bare_fn_arg_mut(&mut self, i: &mut crate::BareFnArg) {
        visit_bare_fn_arg_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_bare_variadic_mut(&mut self, i: &mut crate::BareVariadic) {
        visit_bare_variadic_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_bin_op_mut(&mut self, i: &mut crate::BinOp) {
        visit_bin_op_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_block_mut(&mut self, i: &mut crate::Block) {
        visit_block_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_bound_lifetimes_mut(&mut self, i: &mut crate::BoundLifetimes) {
        visit_bound_lifetimes_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_captured_param_mut(&mut self, i: &mut crate::CapturedParam) {
        visit_captured_param_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_const_param_mut(&mut self, i: &mut crate::ConstParam) {
        visit_const_param_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_constraint_mut(&mut self, i: &mut crate::Constraint) {
        visit_constraint_mut(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_data_mut(&mut self, i: &mut crate::Data) {
        visit_data_mut(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_data_enum_mut(&mut self, i: &mut crate::DataEnum) {
        visit_data_enum_mut(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_data_struct_mut(&mut self, i: &mut crate::DataStruct) {
        visit_data_struct_mut(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_data_union_mut(&mut self, i: &mut crate::DataUnion) {
        visit_data_union_mut(self, i);
    }
    #[cfg(feature = "derive")]
    #[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
    fn visit_derive_input_mut(&mut self, i: &mut crate::DeriveInput) {
        visit_derive_input_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_mut(&mut self, i: &mut crate::Expr) {
        visit_expr_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_array_mut(&mut self, i: &mut crate::ExprArray) {
        visit_expr_array_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_assign_mut(&mut self, i: &mut crate::ExprAssign) {
        visit_expr_assign_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_async_mut(&mut self, i: &mut crate::ExprAsync) {
        visit_expr_async_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_await_mut(&mut self, i: &mut crate::ExprAwait) {
        visit_expr_await_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_binary_mut(&mut self, i: &mut crate::ExprBinary) {
        visit_expr_binary_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_block_mut(&mut self, i: &mut crate::ExprBlock) {
        visit_expr_block_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_break_mut(&mut self, i: &mut crate::ExprBreak) {
        visit_expr_break_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_call_mut(&mut self, i: &mut crate::ExprCall) {
        visit_expr_call_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_cast_mut(&mut self, i: &mut crate::ExprCast) {
        visit_expr_cast_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_closure_mut(&mut self, i: &mut crate::ExprClosure) {
        visit_expr_closure_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_const_mut(&mut self, i: &mut crate::ExprConst) {
        visit_expr_const_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_continue_mut(&mut self, i: &mut crate::ExprContinue) {
        visit_expr_continue_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_field_mut(&mut self, i: &mut crate::ExprField) {
        visit_expr_field_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_for_loop_mut(&mut self, i: &mut crate::ExprForLoop) {
        visit_expr_for_loop_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_group_mut(&mut self, i: &mut crate::ExprGroup) {
        visit_expr_group_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_if_mut(&mut self, i: &mut crate::ExprIf) {
        visit_expr_if_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_index_mut(&mut self, i: &mut crate::ExprIndex) {
        visit_expr_index_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_infer_mut(&mut self, i: &mut crate::ExprInfer) {
        visit_expr_infer_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_let_mut(&mut self, i: &mut crate::ExprLet) {
        visit_expr_let_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_lit_mut(&mut self, i: &mut crate::ExprLit) {
        visit_expr_lit_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_loop_mut(&mut self, i: &mut crate::ExprLoop) {
        visit_expr_loop_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_macro_mut(&mut self, i: &mut crate::ExprMacro) {
        visit_expr_macro_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_match_mut(&mut self, i: &mut crate::ExprMatch) {
        visit_expr_match_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_method_call_mut(&mut self, i: &mut crate::ExprMethodCall) {
        visit_expr_method_call_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_paren_mut(&mut self, i: &mut crate::ExprParen) {
        visit_expr_paren_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_path_mut(&mut self, i: &mut crate::ExprPath) {
        visit_expr_path_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_range_mut(&mut self, i: &mut crate::ExprRange) {
        visit_expr_range_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_raw_addr_mut(&mut self, i: &mut crate::ExprRawAddr) {
        visit_expr_raw_addr_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_reference_mut(&mut self, i: &mut crate::ExprReference) {
        visit_expr_reference_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_repeat_mut(&mut self, i: &mut crate::ExprRepeat) {
        visit_expr_repeat_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_return_mut(&mut self, i: &mut crate::ExprReturn) {
        visit_expr_return_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_struct_mut(&mut self, i: &mut crate::ExprStruct) {
        visit_expr_struct_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_try_mut(&mut self, i: &mut crate::ExprTry) {
        visit_expr_try_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_try_block_mut(&mut self, i: &mut crate::ExprTryBlock) {
        visit_expr_try_block_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_tuple_mut(&mut self, i: &mut crate::ExprTuple) {
        visit_expr_tuple_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_expr_unary_mut(&mut self, i: &mut crate::ExprUnary) {
        visit_expr_unary_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_unsafe_mut(&mut self, i: &mut crate::ExprUnsafe) {
        visit_expr_unsafe_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_while_mut(&mut self, i: &mut crate::ExprWhile) {
        visit_expr_while_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_expr_yield_mut(&mut self, i: &mut crate::ExprYield) {
        visit_expr_yield_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_field_mut(&mut self, i: &mut crate::Field) {
        visit_field_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_field_mutability_mut(&mut self, i: &mut crate::FieldMutability) {
        visit_field_mutability_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_field_pat_mut(&mut self, i: &mut crate::FieldPat) {
        visit_field_pat_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_field_value_mut(&mut self, i: &mut crate::FieldValue) {
        visit_field_value_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_fields_mut(&mut self, i: &mut crate::Fields) {
        visit_fields_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_fields_named_mut(&mut self, i: &mut crate::FieldsNamed) {
        visit_fields_named_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_fields_unnamed_mut(&mut self, i: &mut crate::FieldsUnnamed) {
        visit_fields_unnamed_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_file_mut(&mut self, i: &mut crate::File) {
        visit_file_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_fn_arg_mut(&mut self, i: &mut crate::FnArg) {
        visit_fn_arg_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item_mut(&mut self, i: &mut crate::ForeignItem) {
        visit_foreign_item_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item_fn_mut(&mut self, i: &mut crate::ForeignItemFn) {
        visit_foreign_item_fn_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item_macro_mut(&mut self, i: &mut crate::ForeignItemMacro) {
        visit_foreign_item_macro_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item_static_mut(&mut self, i: &mut crate::ForeignItemStatic) {
        visit_foreign_item_static_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_foreign_item_type_mut(&mut self, i: &mut crate::ForeignItemType) {
        visit_foreign_item_type_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_generic_argument_mut(&mut self, i: &mut crate::GenericArgument) {
        visit_generic_argument_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_generic_param_mut(&mut self, i: &mut crate::GenericParam) {
        visit_generic_param_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_generics_mut(&mut self, i: &mut crate::Generics) {
        visit_generics_mut(self, i);
    }
    fn visit_ident_mut(&mut self, i: &mut proc_macro2::Ident) {
        visit_ident_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item_mut(&mut self, i: &mut crate::ImplItem) {
        visit_impl_item_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item_const_mut(&mut self, i: &mut crate::ImplItemConst) {
        visit_impl_item_const_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item_fn_mut(&mut self, i: &mut crate::ImplItemFn) {
        visit_impl_item_fn_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item_macro_mut(&mut self, i: &mut crate::ImplItemMacro) {
        visit_impl_item_macro_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_item_type_mut(&mut self, i: &mut crate::ImplItemType) {
        visit_impl_item_type_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_impl_restriction_mut(&mut self, i: &mut crate::ImplRestriction) {
        visit_impl_restriction_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_index_mut(&mut self, i: &mut crate::Index) {
        visit_index_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_mut(&mut self, i: &mut crate::Item) {
        visit_item_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_const_mut(&mut self, i: &mut crate::ItemConst) {
        visit_item_const_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_enum_mut(&mut self, i: &mut crate::ItemEnum) {
        visit_item_enum_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_extern_crate_mut(&mut self, i: &mut crate::ItemExternCrate) {
        visit_item_extern_crate_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_fn_mut(&mut self, i: &mut crate::ItemFn) {
        visit_item_fn_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_foreign_mod_mut(&mut self, i: &mut crate::ItemForeignMod) {
        visit_item_foreign_mod_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_impl_mut(&mut self, i: &mut crate::ItemImpl) {
        visit_item_impl_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_macro_mut(&mut self, i: &mut crate::ItemMacro) {
        visit_item_macro_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_mod_mut(&mut self, i: &mut crate::ItemMod) {
        visit_item_mod_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_static_mut(&mut self, i: &mut crate::ItemStatic) {
        visit_item_static_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_struct_mut(&mut self, i: &mut crate::ItemStruct) {
        visit_item_struct_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_trait_mut(&mut self, i: &mut crate::ItemTrait) {
        visit_item_trait_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_trait_alias_mut(&mut self, i: &mut crate::ItemTraitAlias) {
        visit_item_trait_alias_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_type_mut(&mut self, i: &mut crate::ItemType) {
        visit_item_type_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_union_mut(&mut self, i: &mut crate::ItemUnion) {
        visit_item_union_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_item_use_mut(&mut self, i: &mut crate::ItemUse) {
        visit_item_use_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_label_mut(&mut self, i: &mut crate::Label) {
        visit_label_mut(self, i);
    }
    fn visit_lifetime_mut(&mut self, i: &mut crate::Lifetime) {
        visit_lifetime_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_lifetime_param_mut(&mut self, i: &mut crate::LifetimeParam) {
        visit_lifetime_param_mut(self, i);
    }
    fn visit_lit_mut(&mut self, i: &mut crate::Lit) {
        visit_lit_mut(self, i);
    }
    fn visit_lit_bool_mut(&mut self, i: &mut crate::LitBool) {
        visit_lit_bool_mut(self, i);
    }
    fn visit_lit_byte_mut(&mut self, i: &mut crate::LitByte) {
        visit_lit_byte_mut(self, i);
    }
    fn visit_lit_byte_str_mut(&mut self, i: &mut crate::LitByteStr) {
        visit_lit_byte_str_mut(self, i);
    }
    fn visit_lit_cstr_mut(&mut self, i: &mut crate::LitCStr) {
        visit_lit_cstr_mut(self, i);
    }
    fn visit_lit_char_mut(&mut self, i: &mut crate::LitChar) {
        visit_lit_char_mut(self, i);
    }
    fn visit_lit_float_mut(&mut self, i: &mut crate::LitFloat) {
        visit_lit_float_mut(self, i);
    }
    fn visit_lit_int_mut(&mut self, i: &mut crate::LitInt) {
        visit_lit_int_mut(self, i);
    }
    fn visit_lit_str_mut(&mut self, i: &mut crate::LitStr) {
        visit_lit_str_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_local_mut(&mut self, i: &mut crate::Local) {
        visit_local_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_local_init_mut(&mut self, i: &mut crate::LocalInit) {
        visit_local_init_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_macro_mut(&mut self, i: &mut crate::Macro) {
        visit_macro_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_macro_delimiter_mut(&mut self, i: &mut crate::MacroDelimiter) {
        visit_macro_delimiter_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_member_mut(&mut self, i: &mut crate::Member) {
        visit_member_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_meta_mut(&mut self, i: &mut crate::Meta) {
        visit_meta_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_meta_list_mut(&mut self, i: &mut crate::MetaList) {
        visit_meta_list_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_meta_name_value_mut(&mut self, i: &mut crate::MetaNameValue) {
        visit_meta_name_value_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_parenthesized_generic_arguments_mut(
        &mut self,
        i: &mut crate::ParenthesizedGenericArguments,
    ) {
        visit_parenthesized_generic_arguments_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_mut(&mut self, i: &mut crate::Pat) {
        visit_pat_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_ident_mut(&mut self, i: &mut crate::PatIdent) {
        visit_pat_ident_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_or_mut(&mut self, i: &mut crate::PatOr) {
        visit_pat_or_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_paren_mut(&mut self, i: &mut crate::PatParen) {
        visit_pat_paren_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_reference_mut(&mut self, i: &mut crate::PatReference) {
        visit_pat_reference_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_rest_mut(&mut self, i: &mut crate::PatRest) {
        visit_pat_rest_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_slice_mut(&mut self, i: &mut crate::PatSlice) {
        visit_pat_slice_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_struct_mut(&mut self, i: &mut crate::PatStruct) {
        visit_pat_struct_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_tuple_mut(&mut self, i: &mut crate::PatTuple) {
        visit_pat_tuple_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_tuple_struct_mut(&mut self, i: &mut crate::PatTupleStruct) {
        visit_pat_tuple_struct_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_type_mut(&mut self, i: &mut crate::PatType) {
        visit_pat_type_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pat_wild_mut(&mut self, i: &mut crate::PatWild) {
        visit_pat_wild_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_path_mut(&mut self, i: &mut crate::Path) {
        visit_path_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_path_arguments_mut(&mut self, i: &mut crate::PathArguments) {
        visit_path_arguments_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_path_segment_mut(&mut self, i: &mut crate::PathSegment) {
        visit_path_segment_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_pointer_mutability_mut(&mut self, i: &mut crate::PointerMutability) {
        visit_pointer_mutability_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_precise_capture_mut(&mut self, i: &mut crate::PreciseCapture) {
        visit_precise_capture_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_predicate_lifetime_mut(&mut self, i: &mut crate::PredicateLifetime) {
        visit_predicate_lifetime_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_predicate_type_mut(&mut self, i: &mut crate::PredicateType) {
        visit_predicate_type_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_qself_mut(&mut self, i: &mut crate::QSelf) {
        visit_qself_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_range_limits_mut(&mut self, i: &mut crate::RangeLimits) {
        visit_range_limits_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_receiver_mut(&mut self, i: &mut crate::Receiver) {
        visit_receiver_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_return_type_mut(&mut self, i: &mut crate::ReturnType) {
        visit_return_type_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_signature_mut(&mut self, i: &mut crate::Signature) {
        visit_signature_mut(self, i);
    }
    fn visit_span_mut(&mut self, i: &mut proc_macro2::Span) {}
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_static_mutability_mut(&mut self, i: &mut crate::StaticMutability) {
        visit_static_mutability_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_stmt_mut(&mut self, i: &mut crate::Stmt) {
        visit_stmt_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_stmt_macro_mut(&mut self, i: &mut crate::StmtMacro) {
        visit_stmt_macro_mut(self, i);
    }
    fn visit_token_stream_mut(&mut self, i: &mut proc_macro2::TokenStream) {}
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_trait_bound_mut(&mut self, i: &mut crate::TraitBound) {
        visit_trait_bound_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_trait_bound_modifier_mut(&mut self, i: &mut crate::TraitBoundModifier) {
        visit_trait_bound_modifier_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item_mut(&mut self, i: &mut crate::TraitItem) {
        visit_trait_item_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item_const_mut(&mut self, i: &mut crate::TraitItemConst) {
        visit_trait_item_const_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item_fn_mut(&mut self, i: &mut crate::TraitItemFn) {
        visit_trait_item_fn_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item_macro_mut(&mut self, i: &mut crate::TraitItemMacro) {
        visit_trait_item_macro_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_trait_item_type_mut(&mut self, i: &mut crate::TraitItemType) {
        visit_trait_item_type_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_mut(&mut self, i: &mut crate::Type) {
        visit_type_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_array_mut(&mut self, i: &mut crate::TypeArray) {
        visit_type_array_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_bare_fn_mut(&mut self, i: &mut crate::TypeBareFn) {
        visit_type_bare_fn_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_group_mut(&mut self, i: &mut crate::TypeGroup) {
        visit_type_group_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_impl_trait_mut(&mut self, i: &mut crate::TypeImplTrait) {
        visit_type_impl_trait_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_infer_mut(&mut self, i: &mut crate::TypeInfer) {
        visit_type_infer_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_macro_mut(&mut self, i: &mut crate::TypeMacro) {
        visit_type_macro_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_never_mut(&mut self, i: &mut crate::TypeNever) {
        visit_type_never_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_param_mut(&mut self, i: &mut crate::TypeParam) {
        visit_type_param_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_param_bound_mut(&mut self, i: &mut crate::TypeParamBound) {
        visit_type_param_bound_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_paren_mut(&mut self, i: &mut crate::TypeParen) {
        visit_type_paren_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_path_mut(&mut self, i: &mut crate::TypePath) {
        visit_type_path_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_ptr_mut(&mut self, i: &mut crate::TypePtr) {
        visit_type_ptr_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_reference_mut(&mut self, i: &mut crate::TypeReference) {
        visit_type_reference_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_slice_mut(&mut self, i: &mut crate::TypeSlice) {
        visit_type_slice_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_trait_object_mut(&mut self, i: &mut crate::TypeTraitObject) {
        visit_type_trait_object_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_type_tuple_mut(&mut self, i: &mut crate::TypeTuple) {
        visit_type_tuple_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_un_op_mut(&mut self, i: &mut crate::UnOp) {
        visit_un_op_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_glob_mut(&mut self, i: &mut crate::UseGlob) {
        visit_use_glob_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_group_mut(&mut self, i: &mut crate::UseGroup) {
        visit_use_group_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_name_mut(&mut self, i: &mut crate::UseName) {
        visit_use_name_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_path_mut(&mut self, i: &mut crate::UsePath) {
        visit_use_path_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_rename_mut(&mut self, i: &mut crate::UseRename) {
        visit_use_rename_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_use_tree_mut(&mut self, i: &mut crate::UseTree) {
        visit_use_tree_mut(self, i);
    }
    #[cfg(feature = "full")]
    #[cfg_attr(docsrs, doc(cfg(feature = "full")))]
    fn visit_variadic_mut(&mut self, i: &mut crate::Variadic) {
        visit_variadic_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_variant_mut(&mut self, i: &mut crate::Variant) {
        visit_variant_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_vis_restricted_mut(&mut self, i: &mut crate::VisRestricted) {
        visit_vis_restricted_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_visibility_mut(&mut self, i: &mut crate::Visibility) {
        visit_visibility_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_where_clause_mut(&mut self, i: &mut crate::WhereClause) {
        visit_where_clause_mut(self, i);
    }
    #[cfg(any(feature = "derive", feature = "full"))]
    #[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
    fn visit_where_predicate_mut(&mut self, i: &mut crate::WherePredicate) {
        visit_where_predicate_mut(self, i);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_abi_mut<V>(v: &mut V, node: &mut crate::Abi)
where
    V: VisitMut + ?Sized,
{
    skip!(node.extern_token);
    if let Some(it) = &mut node.name {
        v.visit_lit_str_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_angle_bracketed_generic_arguments_mut<V>(
    v: &mut V,
    node: &mut crate::AngleBracketedGenericArguments,
)
where
    V: VisitMut + ?Sized,
{
    skip!(node.colon2_token);
    skip!(node.lt_token);
    for mut el in Punctuated::pairs_mut(&mut node.args) {
        let it = el.value_mut();
        v.visit_generic_argument_mut(it);
    }
    skip!(node.gt_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_arm_mut<V>(v: &mut V, node: &mut crate::Arm)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_pat_mut(&mut node.pat);
    if let Some(it) = &mut node.guard {
        skip!((it).0);
        v.visit_expr_mut(&mut *(it).1);
    }
    skip!(node.fat_arrow_token);
    v.visit_expr_mut(&mut *node.body);
    skip!(node.comma);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_assoc_const_mut<V>(v: &mut V, node: &mut crate::AssocConst)
where
    V: VisitMut + ?Sized,
{
    v.visit_ident_mut(&mut node.ident);
    if let Some(it) = &mut node.generics {
        v.visit_angle_bracketed_generic_arguments_mut(it);
    }
    skip!(node.eq_token);
    v.visit_expr_mut(&mut node.value);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_assoc_type_mut<V>(v: &mut V, node: &mut crate::AssocType)
where
    V: VisitMut + ?Sized,
{
    v.visit_ident_mut(&mut node.ident);
    if let Some(it) = &mut node.generics {
        v.visit_angle_bracketed_generic_arguments_mut(it);
    }
    skip!(node.eq_token);
    v.visit_type_mut(&mut node.ty);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_attr_style_mut<V>(v: &mut V, node: &mut crate::AttrStyle)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::AttrStyle::Outer => {}
        crate::AttrStyle::Inner(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_attribute_mut<V>(v: &mut V, node: &mut crate::Attribute)
where
    V: VisitMut + ?Sized,
{
    skip!(node.pound_token);
    v.visit_attr_style_mut(&mut node.style);
    skip!(node.bracket_token);
    v.visit_meta_mut(&mut node.meta);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_bare_fn_arg_mut<V>(v: &mut V, node: &mut crate::BareFnArg)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.name {
        v.visit_ident_mut(&mut (it).0);
        skip!((it).1);
    }
    v.visit_type_mut(&mut node.ty);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_bare_variadic_mut<V>(v: &mut V, node: &mut crate::BareVariadic)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.name {
        v.visit_ident_mut(&mut (it).0);
        skip!((it).1);
    }
    skip!(node.dots);
    skip!(node.comma);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_bin_op_mut<V>(v: &mut V, node: &mut crate::BinOp)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::BinOp::Add(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Sub(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Mul(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Div(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Rem(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::And(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Or(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitXor(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitAnd(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitOr(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Shl(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Shr(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Eq(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Lt(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Le(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Ne(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Ge(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::Gt(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::AddAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::SubAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::MulAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::DivAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::RemAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitXorAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitAndAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::BitOrAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::ShlAssign(_binding_0) => {
            skip!(_binding_0);
        }
        crate::BinOp::ShrAssign(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_block_mut<V>(v: &mut V, node: &mut crate::Block)
where
    V: VisitMut + ?Sized,
{
    skip!(node.brace_token);
    for it in &mut node.stmts {
        v.visit_stmt_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_bound_lifetimes_mut<V>(v: &mut V, node: &mut crate::BoundLifetimes)
where
    V: VisitMut + ?Sized,
{
    skip!(node.for_token);
    skip!(node.lt_token);
    for mut el in Punctuated::pairs_mut(&mut node.lifetimes) {
        let it = el.value_mut();
        v.visit_generic_param_mut(it);
    }
    skip!(node.gt_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_captured_param_mut<V>(v: &mut V, node: &mut crate::CapturedParam)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::CapturedParam::Lifetime(_binding_0) => {
            v.visit_lifetime_mut(_binding_0);
        }
        crate::CapturedParam::Ident(_binding_0) => {
            v.visit_ident_mut(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_const_param_mut<V>(v: &mut V, node: &mut crate::ConstParam)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.const_token);
    v.visit_ident_mut(&mut node.ident);
    skip!(node.colon_token);
    v.visit_type_mut(&mut node.ty);
    skip!(node.eq_token);
    if let Some(it) = &mut node.default {
        v.visit_expr_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_constraint_mut<V>(v: &mut V, node: &mut crate::Constraint)
where
    V: VisitMut + ?Sized,
{
    v.visit_ident_mut(&mut node.ident);
    if let Some(it) = &mut node.generics {
        v.visit_angle_bracketed_generic_arguments_mut(it);
    }
    skip!(node.colon_token);
    for mut el in Punctuated::pairs_mut(&mut node.bounds) {
        let it = el.value_mut();
        v.visit_type_param_bound_mut(it);
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_data_mut<V>(v: &mut V, node: &mut crate::Data)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Data::Struct(_binding_0) => {
            v.visit_data_struct_mut(_binding_0);
        }
        crate::Data::Enum(_binding_0) => {
            v.visit_data_enum_mut(_binding_0);
        }
        crate::Data::Union(_binding_0) => {
            v.visit_data_union_mut(_binding_0);
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_data_enum_mut<V>(v: &mut V, node: &mut crate::DataEnum)
where
    V: VisitMut + ?Sized,
{
    skip!(node.enum_token);
    skip!(node.brace_token);
    for mut el in Punctuated::pairs_mut(&mut node.variants) {
        let it = el.value_mut();
        v.visit_variant_mut(it);
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_data_struct_mut<V>(v: &mut V, node: &mut crate::DataStruct)
where
    V: VisitMut + ?Sized,
{
    skip!(node.struct_token);
    v.visit_fields_mut(&mut node.fields);
    skip!(node.semi_token);
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_data_union_mut<V>(v: &mut V, node: &mut crate::DataUnion)
where
    V: VisitMut + ?Sized,
{
    skip!(node.union_token);
    v.visit_fields_named_mut(&mut node.fields);
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "derive")))]
pub fn visit_derive_input_mut<V>(v: &mut V, node: &mut crate::DeriveInput)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    v.visit_data_mut(&mut node.data);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_mut<V>(v: &mut V, node: &mut crate::Expr)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Expr::Array(_binding_0) => {
            full!(v.visit_expr_array_mut(_binding_0));
        }
        crate::Expr::Assign(_binding_0) => {
            full!(v.visit_expr_assign_mut(_binding_0));
        }
        crate::Expr::Async(_binding_0) => {
            full!(v.visit_expr_async_mut(_binding_0));
        }
        crate::Expr::Await(_binding_0) => {
            full!(v.visit_expr_await_mut(_binding_0));
        }
        crate::Expr::Binary(_binding_0) => {
            v.visit_expr_binary_mut(_binding_0);
        }
        crate::Expr::Block(_binding_0) => {
            full!(v.visit_expr_block_mut(_binding_0));
        }
        crate::Expr::Break(_binding_0) => {
            full!(v.visit_expr_break_mut(_binding_0));
        }
        crate::Expr::Call(_binding_0) => {
            v.visit_expr_call_mut(_binding_0);
        }
        crate::Expr::Cast(_binding_0) => {
            v.visit_expr_cast_mut(_binding_0);
        }
        crate::Expr::Closure(_binding_0) => {
            full!(v.visit_expr_closure_mut(_binding_0));
        }
        crate::Expr::Const(_binding_0) => {
            full!(v.visit_expr_const_mut(_binding_0));
        }
        crate::Expr::Continue(_binding_0) => {
            full!(v.visit_expr_continue_mut(_binding_0));
        }
        crate::Expr::Field(_binding_0) => {
            v.visit_expr_field_mut(_binding_0);
        }
        crate::Expr::ForLoop(_binding_0) => {
            full!(v.visit_expr_for_loop_mut(_binding_0));
        }
        crate::Expr::Group(_binding_0) => {
            v.visit_expr_group_mut(_binding_0);
        }
        crate::Expr::If(_binding_0) => {
            full!(v.visit_expr_if_mut(_binding_0));
        }
        crate::Expr::Index(_binding_0) => {
            v.visit_expr_index_mut(_binding_0);
        }
        crate::Expr::Infer(_binding_0) => {
            full!(v.visit_expr_infer_mut(_binding_0));
        }
        crate::Expr::Let(_binding_0) => {
            full!(v.visit_expr_let_mut(_binding_0));
        }
        crate::Expr::Lit(_binding_0) => {
            v.visit_expr_lit_mut(_binding_0);
        }
        crate::Expr::Loop(_binding_0) => {
            full!(v.visit_expr_loop_mut(_binding_0));
        }
        crate::Expr::Macro(_binding_0) => {
            v.visit_expr_macro_mut(_binding_0);
        }
        crate::Expr::Match(_binding_0) => {
            full!(v.visit_expr_match_mut(_binding_0));
        }
        crate::Expr::MethodCall(_binding_0) => {
            v.visit_expr_method_call_mut(_binding_0);
        }
        crate::Expr::Paren(_binding_0) => {
            v.visit_expr_paren_mut(_binding_0);
        }
        crate::Expr::Path(_binding_0) => {
            v.visit_expr_path_mut(_binding_0);
        }
        crate::Expr::Range(_binding_0) => {
            full!(v.visit_expr_range_mut(_binding_0));
        }
        crate::Expr::RawAddr(_binding_0) => {
            full!(v.visit_expr_raw_addr_mut(_binding_0));
        }
        crate::Expr::Reference(_binding_0) => {
            v.visit_expr_reference_mut(_binding_0);
        }
        crate::Expr::Repeat(_binding_0) => {
            full!(v.visit_expr_repeat_mut(_binding_0));
        }
        crate::Expr::Return(_binding_0) => {
            full!(v.visit_expr_return_mut(_binding_0));
        }
        crate::Expr::Struct(_binding_0) => {
            v.visit_expr_struct_mut(_binding_0);
        }
        crate::Expr::Try(_binding_0) => {
            full!(v.visit_expr_try_mut(_binding_0));
        }
        crate::Expr::TryBlock(_binding_0) => {
            full!(v.visit_expr_try_block_mut(_binding_0));
        }
        crate::Expr::Tuple(_binding_0) => {
            v.visit_expr_tuple_mut(_binding_0);
        }
        crate::Expr::Unary(_binding_0) => {
            v.visit_expr_unary_mut(_binding_0);
        }
        crate::Expr::Unsafe(_binding_0) => {
            full!(v.visit_expr_unsafe_mut(_binding_0));
        }
        crate::Expr::Verbatim(_binding_0) => {
            v.visit_token_stream_mut(_binding_0);
        }
        crate::Expr::While(_binding_0) => {
            full!(v.visit_expr_while_mut(_binding_0));
        }
        crate::Expr::Yield(_binding_0) => {
            full!(v.visit_expr_yield_mut(_binding_0));
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_array_mut<V>(v: &mut V, node: &mut crate::ExprArray)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.bracket_token);
    for mut el in Punctuated::pairs_mut(&mut node.elems) {
        let it = el.value_mut();
        v.visit_expr_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_assign_mut<V>(v: &mut V, node: &mut crate::ExprAssign)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_expr_mut(&mut *node.left);
    skip!(node.eq_token);
    v.visit_expr_mut(&mut *node.right);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_async_mut<V>(v: &mut V, node: &mut crate::ExprAsync)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.async_token);
    skip!(node.capture);
    v.visit_block_mut(&mut node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_await_mut<V>(v: &mut V, node: &mut crate::ExprAwait)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_expr_mut(&mut *node.base);
    skip!(node.dot_token);
    skip!(node.await_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_binary_mut<V>(v: &mut V, node: &mut crate::ExprBinary)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_expr_mut(&mut *node.left);
    v.visit_bin_op_mut(&mut node.op);
    v.visit_expr_mut(&mut *node.right);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_block_mut<V>(v: &mut V, node: &mut crate::ExprBlock)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.label {
        v.visit_label_mut(it);
    }
    v.visit_block_mut(&mut node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_break_mut<V>(v: &mut V, node: &mut crate::ExprBreak)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.break_token);
    if let Some(it) = &mut node.label {
        v.visit_lifetime_mut(it);
    }
    if let Some(it) = &mut node.expr {
        v.visit_expr_mut(&mut **it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_call_mut<V>(v: &mut V, node: &mut crate::ExprCall)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_expr_mut(&mut *node.func);
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.args) {
        let it = el.value_mut();
        v.visit_expr_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_cast_mut<V>(v: &mut V, node: &mut crate::ExprCast)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_expr_mut(&mut *node.expr);
    skip!(node.as_token);
    v.visit_type_mut(&mut *node.ty);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_closure_mut<V>(v: &mut V, node: &mut crate::ExprClosure)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.lifetimes {
        v.visit_bound_lifetimes_mut(it);
    }
    skip!(node.constness);
    skip!(node.movability);
    skip!(node.asyncness);
    skip!(node.capture);
    skip!(node.or1_token);
    for mut el in Punctuated::pairs_mut(&mut node.inputs) {
        let it = el.value_mut();
        v.visit_pat_mut(it);
    }
    skip!(node.or2_token);
    v.visit_return_type_mut(&mut node.output);
    v.visit_expr_mut(&mut *node.body);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_const_mut<V>(v: &mut V, node: &mut crate::ExprConst)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.const_token);
    v.visit_block_mut(&mut node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_continue_mut<V>(v: &mut V, node: &mut crate::ExprContinue)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.continue_token);
    if let Some(it) = &mut node.label {
        v.visit_lifetime_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_field_mut<V>(v: &mut V, node: &mut crate::ExprField)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_expr_mut(&mut *node.base);
    skip!(node.dot_token);
    v.visit_member_mut(&mut node.member);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_for_loop_mut<V>(v: &mut V, node: &mut crate::ExprForLoop)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.label {
        v.visit_label_mut(it);
    }
    skip!(node.for_token);
    v.visit_pat_mut(&mut *node.pat);
    skip!(node.in_token);
    v.visit_expr_mut(&mut *node.expr);
    v.visit_block_mut(&mut node.body);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_group_mut<V>(v: &mut V, node: &mut crate::ExprGroup)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.group_token);
    v.visit_expr_mut(&mut *node.expr);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_if_mut<V>(v: &mut V, node: &mut crate::ExprIf)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.if_token);
    v.visit_expr_mut(&mut *node.cond);
    v.visit_block_mut(&mut node.then_branch);
    if let Some(it) = &mut node.else_branch {
        skip!((it).0);
        v.visit_expr_mut(&mut *(it).1);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_index_mut<V>(v: &mut V, node: &mut crate::ExprIndex)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_expr_mut(&mut *node.expr);
    skip!(node.bracket_token);
    v.visit_expr_mut(&mut *node.index);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_infer_mut<V>(v: &mut V, node: &mut crate::ExprInfer)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.underscore_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_let_mut<V>(v: &mut V, node: &mut crate::ExprLet)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.let_token);
    v.visit_pat_mut(&mut *node.pat);
    skip!(node.eq_token);
    v.visit_expr_mut(&mut *node.expr);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_lit_mut<V>(v: &mut V, node: &mut crate::ExprLit)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_lit_mut(&mut node.lit);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_loop_mut<V>(v: &mut V, node: &mut crate::ExprLoop)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.label {
        v.visit_label_mut(it);
    }
    skip!(node.loop_token);
    v.visit_block_mut(&mut node.body);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_macro_mut<V>(v: &mut V, node: &mut crate::ExprMacro)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_macro_mut(&mut node.mac);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_match_mut<V>(v: &mut V, node: &mut crate::ExprMatch)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.match_token);
    v.visit_expr_mut(&mut *node.expr);
    skip!(node.brace_token);
    for it in &mut node.arms {
        v.visit_arm_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_method_call_mut<V>(v: &mut V, node: &mut crate::ExprMethodCall)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_expr_mut(&mut *node.receiver);
    skip!(node.dot_token);
    v.visit_ident_mut(&mut node.method);
    if let Some(it) = &mut node.turbofish {
        v.visit_angle_bracketed_generic_arguments_mut(it);
    }
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.args) {
        let it = el.value_mut();
        v.visit_expr_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_paren_mut<V>(v: &mut V, node: &mut crate::ExprParen)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.paren_token);
    v.visit_expr_mut(&mut *node.expr);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_path_mut<V>(v: &mut V, node: &mut crate::ExprPath)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.qself {
        v.visit_qself_mut(it);
    }
    v.visit_path_mut(&mut node.path);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_range_mut<V>(v: &mut V, node: &mut crate::ExprRange)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.start {
        v.visit_expr_mut(&mut **it);
    }
    v.visit_range_limits_mut(&mut node.limits);
    if let Some(it) = &mut node.end {
        v.visit_expr_mut(&mut **it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_raw_addr_mut<V>(v: &mut V, node: &mut crate::ExprRawAddr)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.and_token);
    skip!(node.raw);
    v.visit_pointer_mutability_mut(&mut node.mutability);
    v.visit_expr_mut(&mut *node.expr);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_reference_mut<V>(v: &mut V, node: &mut crate::ExprReference)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.and_token);
    skip!(node.mutability);
    v.visit_expr_mut(&mut *node.expr);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_repeat_mut<V>(v: &mut V, node: &mut crate::ExprRepeat)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.bracket_token);
    v.visit_expr_mut(&mut *node.expr);
    skip!(node.semi_token);
    v.visit_expr_mut(&mut *node.len);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_return_mut<V>(v: &mut V, node: &mut crate::ExprReturn)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.return_token);
    if let Some(it) = &mut node.expr {
        v.visit_expr_mut(&mut **it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_struct_mut<V>(v: &mut V, node: &mut crate::ExprStruct)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.qself {
        v.visit_qself_mut(it);
    }
    v.visit_path_mut(&mut node.path);
    skip!(node.brace_token);
    for mut el in Punctuated::pairs_mut(&mut node.fields) {
        let it = el.value_mut();
        v.visit_field_value_mut(it);
    }
    skip!(node.dot2_token);
    if let Some(it) = &mut node.rest {
        v.visit_expr_mut(&mut **it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_try_mut<V>(v: &mut V, node: &mut crate::ExprTry)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_expr_mut(&mut *node.expr);
    skip!(node.question_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_try_block_mut<V>(v: &mut V, node: &mut crate::ExprTryBlock)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.try_token);
    v.visit_block_mut(&mut node.block);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_tuple_mut<V>(v: &mut V, node: &mut crate::ExprTuple)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.elems) {
        let it = el.value_mut();
        v.visit_expr_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_expr_unary_mut<V>(v: &mut V, node: &mut crate::ExprUnary)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_un_op_mut(&mut node.op);
    v.visit_expr_mut(&mut *node.expr);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_unsafe_mut<V>(v: &mut V, node: &mut crate::ExprUnsafe)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.unsafe_token);
    v.visit_block_mut(&mut node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_while_mut<V>(v: &mut V, node: &mut crate::ExprWhile)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.label {
        v.visit_label_mut(it);
    }
    skip!(node.while_token);
    v.visit_expr_mut(&mut *node.cond);
    v.visit_block_mut(&mut node.body);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_expr_yield_mut<V>(v: &mut V, node: &mut crate::ExprYield)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.yield_token);
    if let Some(it) = &mut node.expr {
        v.visit_expr_mut(&mut **it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_field_mut<V>(v: &mut V, node: &mut crate::Field)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    v.visit_field_mutability_mut(&mut node.mutability);
    if let Some(it) = &mut node.ident {
        v.visit_ident_mut(it);
    }
    skip!(node.colon_token);
    v.visit_type_mut(&mut node.ty);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_field_mutability_mut<V>(v: &mut V, node: &mut crate::FieldMutability)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::FieldMutability::None => {}
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_field_pat_mut<V>(v: &mut V, node: &mut crate::FieldPat)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_member_mut(&mut node.member);
    skip!(node.colon_token);
    v.visit_pat_mut(&mut *node.pat);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_field_value_mut<V>(v: &mut V, node: &mut crate::FieldValue)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_member_mut(&mut node.member);
    skip!(node.colon_token);
    v.visit_expr_mut(&mut node.expr);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_fields_mut<V>(v: &mut V, node: &mut crate::Fields)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Fields::Named(_binding_0) => {
            v.visit_fields_named_mut(_binding_0);
        }
        crate::Fields::Unnamed(_binding_0) => {
            v.visit_fields_unnamed_mut(_binding_0);
        }
        crate::Fields::Unit => {}
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_fields_named_mut<V>(v: &mut V, node: &mut crate::FieldsNamed)
where
    V: VisitMut + ?Sized,
{
    skip!(node.brace_token);
    for mut el in Punctuated::pairs_mut(&mut node.named) {
        let it = el.value_mut();
        v.visit_field_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_fields_unnamed_mut<V>(v: &mut V, node: &mut crate::FieldsUnnamed)
where
    V: VisitMut + ?Sized,
{
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.unnamed) {
        let it = el.value_mut();
        v.visit_field_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_file_mut<V>(v: &mut V, node: &mut crate::File)
where
    V: VisitMut + ?Sized,
{
    skip!(node.shebang);
    v.visit_attributes_mut(&mut node.attrs);
    for it in &mut node.items {
        v.visit_item_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_fn_arg_mut<V>(v: &mut V, node: &mut crate::FnArg)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::FnArg::Receiver(_binding_0) => {
            v.visit_receiver_mut(_binding_0);
        }
        crate::FnArg::Typed(_binding_0) => {
            v.visit_pat_type_mut(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item_mut<V>(v: &mut V, node: &mut crate::ForeignItem)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::ForeignItem::Fn(_binding_0) => {
            v.visit_foreign_item_fn_mut(_binding_0);
        }
        crate::ForeignItem::Static(_binding_0) => {
            v.visit_foreign_item_static_mut(_binding_0);
        }
        crate::ForeignItem::Type(_binding_0) => {
            v.visit_foreign_item_type_mut(_binding_0);
        }
        crate::ForeignItem::Macro(_binding_0) => {
            v.visit_foreign_item_macro_mut(_binding_0);
        }
        crate::ForeignItem::Verbatim(_binding_0) => {
            v.visit_token_stream_mut(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item_fn_mut<V>(v: &mut V, node: &mut crate::ForeignItemFn)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    v.visit_signature_mut(&mut node.sig);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item_macro_mut<V>(v: &mut V, node: &mut crate::ForeignItemMacro)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_macro_mut(&mut node.mac);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item_static_mut<V>(v: &mut V, node: &mut crate::ForeignItemStatic)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.static_token);
    v.visit_static_mutability_mut(&mut node.mutability);
    v.visit_ident_mut(&mut node.ident);
    skip!(node.colon_token);
    v.visit_type_mut(&mut *node.ty);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_foreign_item_type_mut<V>(v: &mut V, node: &mut crate::ForeignItemType)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.type_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.semi_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_generic_argument_mut<V>(v: &mut V, node: &mut crate::GenericArgument)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::GenericArgument::Lifetime(_binding_0) => {
            v.visit_lifetime_mut(_binding_0);
        }
        crate::GenericArgument::Type(_binding_0) => {
            v.visit_type_mut(_binding_0);
        }
        crate::GenericArgument::Const(_binding_0) => {
            v.visit_expr_mut(_binding_0);
        }
        crate::GenericArgument::AssocType(_binding_0) => {
            v.visit_assoc_type_mut(_binding_0);
        }
        crate::GenericArgument::AssocConst(_binding_0) => {
            v.visit_assoc_const_mut(_binding_0);
        }
        crate::GenericArgument::Constraint(_binding_0) => {
            v.visit_constraint_mut(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_generic_param_mut<V>(v: &mut V, node: &mut crate::GenericParam)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::GenericParam::Lifetime(_binding_0) => {
            v.visit_lifetime_param_mut(_binding_0);
        }
        crate::GenericParam::Type(_binding_0) => {
            v.visit_type_param_mut(_binding_0);
        }
        crate::GenericParam::Const(_binding_0) => {
            v.visit_const_param_mut(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_generics_mut<V>(v: &mut V, node: &mut crate::Generics)
where
    V: VisitMut + ?Sized,
{
    skip!(node.lt_token);
    for mut el in Punctuated::pairs_mut(&mut node.params) {
        let it = el.value_mut();
        v.visit_generic_param_mut(it);
    }
    skip!(node.gt_token);
    if let Some(it) = &mut node.where_clause {
        v.visit_where_clause_mut(it);
    }
}
pub fn visit_ident_mut<V>(v: &mut V, node: &mut proc_macro2::Ident)
where
    V: VisitMut + ?Sized,
{
    let mut span = node.span();
    v.visit_span_mut(&mut span);
    node.set_span(span);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item_mut<V>(v: &mut V, node: &mut crate::ImplItem)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::ImplItem::Const(_binding_0) => {
            v.visit_impl_item_const_mut(_binding_0);
        }
        crate::ImplItem::Fn(_binding_0) => {
            v.visit_impl_item_fn_mut(_binding_0);
        }
        crate::ImplItem::Type(_binding_0) => {
            v.visit_impl_item_type_mut(_binding_0);
        }
        crate::ImplItem::Macro(_binding_0) => {
            v.visit_impl_item_macro_mut(_binding_0);
        }
        crate::ImplItem::Verbatim(_binding_0) => {
            v.visit_token_stream_mut(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item_const_mut<V>(v: &mut V, node: &mut crate::ImplItemConst)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.defaultness);
    skip!(node.const_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.colon_token);
    v.visit_type_mut(&mut node.ty);
    skip!(node.eq_token);
    v.visit_expr_mut(&mut node.expr);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item_fn_mut<V>(v: &mut V, node: &mut crate::ImplItemFn)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.defaultness);
    v.visit_signature_mut(&mut node.sig);
    v.visit_block_mut(&mut node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item_macro_mut<V>(v: &mut V, node: &mut crate::ImplItemMacro)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_macro_mut(&mut node.mac);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_item_type_mut<V>(v: &mut V, node: &mut crate::ImplItemType)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.defaultness);
    skip!(node.type_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.eq_token);
    v.visit_type_mut(&mut node.ty);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_impl_restriction_mut<V>(v: &mut V, node: &mut crate::ImplRestriction)
where
    V: VisitMut + ?Sized,
{
    match *node {}
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_index_mut<V>(v: &mut V, node: &mut crate::Index)
where
    V: VisitMut + ?Sized,
{
    skip!(node.index);
    v.visit_span_mut(&mut node.span);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_mut<V>(v: &mut V, node: &mut crate::Item)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Item::Const(_binding_0) => {
            v.visit_item_const_mut(_binding_0);
        }
        crate::Item::Enum(_binding_0) => {
            v.visit_item_enum_mut(_binding_0);
        }
        crate::Item::ExternCrate(_binding_0) => {
            v.visit_item_extern_crate_mut(_binding_0);
        }
        crate::Item::Fn(_binding_0) => {
            v.visit_item_fn_mut(_binding_0);
        }
        crate::Item::ForeignMod(_binding_0) => {
            v.visit_item_foreign_mod_mut(_binding_0);
        }
        crate::Item::Impl(_binding_0) => {
            v.visit_item_impl_mut(_binding_0);
        }
        crate::Item::Macro(_binding_0) => {
            v.visit_item_macro_mut(_binding_0);
        }
        crate::Item::Mod(_binding_0) => {
            v.visit_item_mod_mut(_binding_0);
        }
        crate::Item::Static(_binding_0) => {
            v.visit_item_static_mut(_binding_0);
        }
        crate::Item::Struct(_binding_0) => {
            v.visit_item_struct_mut(_binding_0);
        }
        crate::Item::Trait(_binding_0) => {
            v.visit_item_trait_mut(_binding_0);
        }
        crate::Item::TraitAlias(_binding_0) => {
            v.visit_item_trait_alias_mut(_binding_0);
        }
        crate::Item::Type(_binding_0) => {
            v.visit_item_type_mut(_binding_0);
        }
        crate::Item::Union(_binding_0) => {
            v.visit_item_union_mut(_binding_0);
        }
        crate::Item::Use(_binding_0) => {
            v.visit_item_use_mut(_binding_0);
        }
        crate::Item::Verbatim(_binding_0) => {
            v.visit_token_stream_mut(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_const_mut<V>(v: &mut V, node: &mut crate::ItemConst)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.const_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.colon_token);
    v.visit_type_mut(&mut *node.ty);
    skip!(node.eq_token);
    v.visit_expr_mut(&mut *node.expr);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_enum_mut<V>(v: &mut V, node: &mut crate::ItemEnum)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.enum_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.brace_token);
    for mut el in Punctuated::pairs_mut(&mut node.variants) {
        let it = el.value_mut();
        v.visit_variant_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_extern_crate_mut<V>(v: &mut V, node: &mut crate::ItemExternCrate)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.extern_token);
    skip!(node.crate_token);
    v.visit_ident_mut(&mut node.ident);
    if let Some(it) = &mut node.rename {
        skip!((it).0);
        v.visit_ident_mut(&mut (it).1);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_fn_mut<V>(v: &mut V, node: &mut crate::ItemFn)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    v.visit_signature_mut(&mut node.sig);
    v.visit_block_mut(&mut *node.block);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_foreign_mod_mut<V>(v: &mut V, node: &mut crate::ItemForeignMod)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.unsafety);
    v.visit_abi_mut(&mut node.abi);
    skip!(node.brace_token);
    for it in &mut node.items {
        v.visit_foreign_item_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_impl_mut<V>(v: &mut V, node: &mut crate::ItemImpl)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.defaultness);
    skip!(node.unsafety);
    skip!(node.impl_token);
    v.visit_generics_mut(&mut node.generics);
    if let Some(it) = &mut node.trait_ {
        skip!((it).0);
        v.visit_path_mut(&mut (it).1);
        skip!((it).2);
    }
    v.visit_type_mut(&mut *node.self_ty);
    skip!(node.brace_token);
    for it in &mut node.items {
        v.visit_impl_item_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_macro_mut<V>(v: &mut V, node: &mut crate::ItemMacro)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.ident {
        v.visit_ident_mut(it);
    }
    v.visit_macro_mut(&mut node.mac);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_mod_mut<V>(v: &mut V, node: &mut crate::ItemMod)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.unsafety);
    skip!(node.mod_token);
    v.visit_ident_mut(&mut node.ident);
    if let Some(it) = &mut node.content {
        skip!((it).0);
        for it in &mut (it).1 {
            v.visit_item_mut(it);
        }
    }
    skip!(node.semi);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_static_mut<V>(v: &mut V, node: &mut crate::ItemStatic)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.static_token);
    v.visit_static_mutability_mut(&mut node.mutability);
    v.visit_ident_mut(&mut node.ident);
    skip!(node.colon_token);
    v.visit_type_mut(&mut *node.ty);
    skip!(node.eq_token);
    v.visit_expr_mut(&mut *node.expr);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_struct_mut<V>(v: &mut V, node: &mut crate::ItemStruct)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.struct_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    v.visit_fields_mut(&mut node.fields);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_trait_mut<V>(v: &mut V, node: &mut crate::ItemTrait)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.unsafety);
    skip!(node.auto_token);
    if let Some(it) = &mut node.restriction {
        v.visit_impl_restriction_mut(it);
    }
    skip!(node.trait_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.colon_token);
    for mut el in Punctuated::pairs_mut(&mut node.supertraits) {
        let it = el.value_mut();
        v.visit_type_param_bound_mut(it);
    }
    skip!(node.brace_token);
    for it in &mut node.items {
        v.visit_trait_item_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_trait_alias_mut<V>(v: &mut V, node: &mut crate::ItemTraitAlias)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.trait_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.eq_token);
    for mut el in Punctuated::pairs_mut(&mut node.bounds) {
        let it = el.value_mut();
        v.visit_type_param_bound_mut(it);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_type_mut<V>(v: &mut V, node: &mut crate::ItemType)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.type_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.eq_token);
    v.visit_type_mut(&mut *node.ty);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_union_mut<V>(v: &mut V, node: &mut crate::ItemUnion)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.union_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    v.visit_fields_named_mut(&mut node.fields);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_item_use_mut<V>(v: &mut V, node: &mut crate::ItemUse)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_visibility_mut(&mut node.vis);
    skip!(node.use_token);
    skip!(node.leading_colon);
    v.visit_use_tree_mut(&mut node.tree);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_label_mut<V>(v: &mut V, node: &mut crate::Label)
where
    V: VisitMut + ?Sized,
{
    v.visit_lifetime_mut(&mut node.name);
    skip!(node.colon_token);
}
pub fn visit_lifetime_mut<V>(v: &mut V, node: &mut crate::Lifetime)
where
    V: VisitMut + ?Sized,
{
    v.visit_span_mut(&mut node.apostrophe);
    v.visit_ident_mut(&mut node.ident);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_lifetime_param_mut<V>(v: &mut V, node: &mut crate::LifetimeParam)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_lifetime_mut(&mut node.lifetime);
    skip!(node.colon_token);
    for mut el in Punctuated::pairs_mut(&mut node.bounds) {
        let it = el.value_mut();
        v.visit_lifetime_mut(it);
    }
}
pub fn visit_lit_mut<V>(v: &mut V, node: &mut crate::Lit)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Lit::Str(_binding_0) => {
            v.visit_lit_str_mut(_binding_0);
        }
        crate::Lit::ByteStr(_binding_0) => {
            v.visit_lit_byte_str_mut(_binding_0);
        }
        crate::Lit::CStr(_binding_0) => {
            v.visit_lit_cstr_mut(_binding_0);
        }
        crate::Lit::Byte(_binding_0) => {
            v.visit_lit_byte_mut(_binding_0);
        }
        crate::Lit::Char(_binding_0) => {
            v.visit_lit_char_mut(_binding_0);
        }
        crate::Lit::Int(_binding_0) => {
            v.visit_lit_int_mut(_binding_0);
        }
        crate::Lit::Float(_binding_0) => {
            v.visit_lit_float_mut(_binding_0);
        }
        crate::Lit::Bool(_binding_0) => {
            v.visit_lit_bool_mut(_binding_0);
        }
        crate::Lit::Verbatim(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
pub fn visit_lit_bool_mut<V>(v: &mut V, node: &mut crate::LitBool)
where
    V: VisitMut + ?Sized,
{
    skip!(node.value);
    v.visit_span_mut(&mut node.span);
}
pub fn visit_lit_byte_mut<V>(v: &mut V, node: &mut crate::LitByte)
where
    V: VisitMut + ?Sized,
{}
pub fn visit_lit_byte_str_mut<V>(v: &mut V, node: &mut crate::LitByteStr)
where
    V: VisitMut + ?Sized,
{}
pub fn visit_lit_cstr_mut<V>(v: &mut V, node: &mut crate::LitCStr)
where
    V: VisitMut + ?Sized,
{}
pub fn visit_lit_char_mut<V>(v: &mut V, node: &mut crate::LitChar)
where
    V: VisitMut + ?Sized,
{}
pub fn visit_lit_float_mut<V>(v: &mut V, node: &mut crate::LitFloat)
where
    V: VisitMut + ?Sized,
{}
pub fn visit_lit_int_mut<V>(v: &mut V, node: &mut crate::LitInt)
where
    V: VisitMut + ?Sized,
{}
pub fn visit_lit_str_mut<V>(v: &mut V, node: &mut crate::LitStr)
where
    V: VisitMut + ?Sized,
{}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_local_mut<V>(v: &mut V, node: &mut crate::Local)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.let_token);
    v.visit_pat_mut(&mut node.pat);
    if let Some(it) = &mut node.init {
        v.visit_local_init_mut(it);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_local_init_mut<V>(v: &mut V, node: &mut crate::LocalInit)
where
    V: VisitMut + ?Sized,
{
    skip!(node.eq_token);
    v.visit_expr_mut(&mut *node.expr);
    if let Some(it) = &mut node.diverge {
        skip!((it).0);
        v.visit_expr_mut(&mut *(it).1);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_macro_mut<V>(v: &mut V, node: &mut crate::Macro)
where
    V: VisitMut + ?Sized,
{
    v.visit_path_mut(&mut node.path);
    skip!(node.bang_token);
    v.visit_macro_delimiter_mut(&mut node.delimiter);
    v.visit_token_stream_mut(&mut node.tokens);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_macro_delimiter_mut<V>(v: &mut V, node: &mut crate::MacroDelimiter)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::MacroDelimiter::Paren(_binding_0) => {
            skip!(_binding_0);
        }
        crate::MacroDelimiter::Brace(_binding_0) => {
            skip!(_binding_0);
        }
        crate::MacroDelimiter::Bracket(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_member_mut<V>(v: &mut V, node: &mut crate::Member)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Member::Named(_binding_0) => {
            v.visit_ident_mut(_binding_0);
        }
        crate::Member::Unnamed(_binding_0) => {
            v.visit_index_mut(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_meta_mut<V>(v: &mut V, node: &mut crate::Meta)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Meta::Path(_binding_0) => {
            v.visit_path_mut(_binding_0);
        }
        crate::Meta::List(_binding_0) => {
            v.visit_meta_list_mut(_binding_0);
        }
        crate::Meta::NameValue(_binding_0) => {
            v.visit_meta_name_value_mut(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_meta_list_mut<V>(v: &mut V, node: &mut crate::MetaList)
where
    V: VisitMut + ?Sized,
{
    v.visit_path_mut(&mut node.path);
    v.visit_macro_delimiter_mut(&mut node.delimiter);
    v.visit_token_stream_mut(&mut node.tokens);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_meta_name_value_mut<V>(v: &mut V, node: &mut crate::MetaNameValue)
where
    V: VisitMut + ?Sized,
{
    v.visit_path_mut(&mut node.path);
    skip!(node.eq_token);
    v.visit_expr_mut(&mut node.value);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_parenthesized_generic_arguments_mut<V>(
    v: &mut V,
    node: &mut crate::ParenthesizedGenericArguments,
)
where
    V: VisitMut + ?Sized,
{
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.inputs) {
        let it = el.value_mut();
        v.visit_type_mut(it);
    }
    v.visit_return_type_mut(&mut node.output);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_mut<V>(v: &mut V, node: &mut crate::Pat)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Pat::Const(_binding_0) => {
            v.visit_expr_const_mut(_binding_0);
        }
        crate::Pat::Ident(_binding_0) => {
            v.visit_pat_ident_mut(_binding_0);
        }
        crate::Pat::Lit(_binding_0) => {
            v.visit_expr_lit_mut(_binding_0);
        }
        crate::Pat::Macro(_binding_0) => {
            v.visit_expr_macro_mut(_binding_0);
        }
        crate::Pat::Or(_binding_0) => {
            v.visit_pat_or_mut(_binding_0);
        }
        crate::Pat::Paren(_binding_0) => {
            v.visit_pat_paren_mut(_binding_0);
        }
        crate::Pat::Path(_binding_0) => {
            v.visit_expr_path_mut(_binding_0);
        }
        crate::Pat::Range(_binding_0) => {
            v.visit_expr_range_mut(_binding_0);
        }
        crate::Pat::Reference(_binding_0) => {
            v.visit_pat_reference_mut(_binding_0);
        }
        crate::Pat::Rest(_binding_0) => {
            v.visit_pat_rest_mut(_binding_0);
        }
        crate::Pat::Slice(_binding_0) => {
            v.visit_pat_slice_mut(_binding_0);
        }
        crate::Pat::Struct(_binding_0) => {
            v.visit_pat_struct_mut(_binding_0);
        }
        crate::Pat::Tuple(_binding_0) => {
            v.visit_pat_tuple_mut(_binding_0);
        }
        crate::Pat::TupleStruct(_binding_0) => {
            v.visit_pat_tuple_struct_mut(_binding_0);
        }
        crate::Pat::Type(_binding_0) => {
            v.visit_pat_type_mut(_binding_0);
        }
        crate::Pat::Verbatim(_binding_0) => {
            v.visit_token_stream_mut(_binding_0);
        }
        crate::Pat::Wild(_binding_0) => {
            v.visit_pat_wild_mut(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_ident_mut<V>(v: &mut V, node: &mut crate::PatIdent)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.by_ref);
    skip!(node.mutability);
    v.visit_ident_mut(&mut node.ident);
    if let Some(it) = &mut node.subpat {
        skip!((it).0);
        v.visit_pat_mut(&mut *(it).1);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_or_mut<V>(v: &mut V, node: &mut crate::PatOr)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.leading_vert);
    for mut el in Punctuated::pairs_mut(&mut node.cases) {
        let it = el.value_mut();
        v.visit_pat_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_paren_mut<V>(v: &mut V, node: &mut crate::PatParen)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.paren_token);
    v.visit_pat_mut(&mut *node.pat);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_reference_mut<V>(v: &mut V, node: &mut crate::PatReference)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.and_token);
    skip!(node.mutability);
    v.visit_pat_mut(&mut *node.pat);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_rest_mut<V>(v: &mut V, node: &mut crate::PatRest)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.dot2_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_slice_mut<V>(v: &mut V, node: &mut crate::PatSlice)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.bracket_token);
    for mut el in Punctuated::pairs_mut(&mut node.elems) {
        let it = el.value_mut();
        v.visit_pat_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_struct_mut<V>(v: &mut V, node: &mut crate::PatStruct)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.qself {
        v.visit_qself_mut(it);
    }
    v.visit_path_mut(&mut node.path);
    skip!(node.brace_token);
    for mut el in Punctuated::pairs_mut(&mut node.fields) {
        let it = el.value_mut();
        v.visit_field_pat_mut(it);
    }
    if let Some(it) = &mut node.rest {
        v.visit_pat_rest_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_tuple_mut<V>(v: &mut V, node: &mut crate::PatTuple)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.elems) {
        let it = el.value_mut();
        v.visit_pat_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_tuple_struct_mut<V>(v: &mut V, node: &mut crate::PatTupleStruct)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.qself {
        v.visit_qself_mut(it);
    }
    v.visit_path_mut(&mut node.path);
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.elems) {
        let it = el.value_mut();
        v.visit_pat_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_type_mut<V>(v: &mut V, node: &mut crate::PatType)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_pat_mut(&mut *node.pat);
    skip!(node.colon_token);
    v.visit_type_mut(&mut *node.ty);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pat_wild_mut<V>(v: &mut V, node: &mut crate::PatWild)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.underscore_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_path_mut<V>(v: &mut V, node: &mut crate::Path)
where
    V: VisitMut + ?Sized,
{
    skip!(node.leading_colon);
    for mut el in Punctuated::pairs_mut(&mut node.segments) {
        let it = el.value_mut();
        v.visit_path_segment_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_path_arguments_mut<V>(v: &mut V, node: &mut crate::PathArguments)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::PathArguments::None => {}
        crate::PathArguments::AngleBracketed(_binding_0) => {
            v.visit_angle_bracketed_generic_arguments_mut(_binding_0);
        }
        crate::PathArguments::Parenthesized(_binding_0) => {
            v.visit_parenthesized_generic_arguments_mut(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_path_segment_mut<V>(v: &mut V, node: &mut crate::PathSegment)
where
    V: VisitMut + ?Sized,
{
    v.visit_ident_mut(&mut node.ident);
    v.visit_path_arguments_mut(&mut node.arguments);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_pointer_mutability_mut<V>(v: &mut V, node: &mut crate::PointerMutability)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::PointerMutability::Const(_binding_0) => {
            skip!(_binding_0);
        }
        crate::PointerMutability::Mut(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_precise_capture_mut<V>(v: &mut V, node: &mut crate::PreciseCapture)
where
    V: VisitMut + ?Sized,
{
    skip!(node.use_token);
    skip!(node.lt_token);
    for mut el in Punctuated::pairs_mut(&mut node.params) {
        let it = el.value_mut();
        v.visit_captured_param_mut(it);
    }
    skip!(node.gt_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_predicate_lifetime_mut<V>(v: &mut V, node: &mut crate::PredicateLifetime)
where
    V: VisitMut + ?Sized,
{
    v.visit_lifetime_mut(&mut node.lifetime);
    skip!(node.colon_token);
    for mut el in Punctuated::pairs_mut(&mut node.bounds) {
        let it = el.value_mut();
        v.visit_lifetime_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_predicate_type_mut<V>(v: &mut V, node: &mut crate::PredicateType)
where
    V: VisitMut + ?Sized,
{
    if let Some(it) = &mut node.lifetimes {
        v.visit_bound_lifetimes_mut(it);
    }
    v.visit_type_mut(&mut node.bounded_ty);
    skip!(node.colon_token);
    for mut el in Punctuated::pairs_mut(&mut node.bounds) {
        let it = el.value_mut();
        v.visit_type_param_bound_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_qself_mut<V>(v: &mut V, node: &mut crate::QSelf)
where
    V: VisitMut + ?Sized,
{
    skip!(node.lt_token);
    v.visit_type_mut(&mut *node.ty);
    skip!(node.position);
    skip!(node.as_token);
    skip!(node.gt_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_range_limits_mut<V>(v: &mut V, node: &mut crate::RangeLimits)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::RangeLimits::HalfOpen(_binding_0) => {
            skip!(_binding_0);
        }
        crate::RangeLimits::Closed(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_receiver_mut<V>(v: &mut V, node: &mut crate::Receiver)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.reference {
        skip!((it).0);
        if let Some(it) = &mut (it).1 {
            v.visit_lifetime_mut(it);
        }
    }
    skip!(node.mutability);
    skip!(node.self_token);
    skip!(node.colon_token);
    v.visit_type_mut(&mut *node.ty);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_return_type_mut<V>(v: &mut V, node: &mut crate::ReturnType)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::ReturnType::Default => {}
        crate::ReturnType::Type(_binding_0, _binding_1) => {
            skip!(_binding_0);
            v.visit_type_mut(&mut **_binding_1);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_signature_mut<V>(v: &mut V, node: &mut crate::Signature)
where
    V: VisitMut + ?Sized,
{
    skip!(node.constness);
    skip!(node.asyncness);
    skip!(node.unsafety);
    if let Some(it) = &mut node.abi {
        v.visit_abi_mut(it);
    }
    skip!(node.fn_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.inputs) {
        let it = el.value_mut();
        v.visit_fn_arg_mut(it);
    }
    if let Some(it) = &mut node.variadic {
        v.visit_variadic_mut(it);
    }
    v.visit_return_type_mut(&mut node.output);
}
pub fn visit_span_mut<V>(v: &mut V, node: &mut proc_macro2::Span)
where
    V: VisitMut + ?Sized,
{}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_static_mutability_mut<V>(v: &mut V, node: &mut crate::StaticMutability)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::StaticMutability::Mut(_binding_0) => {
            skip!(_binding_0);
        }
        crate::StaticMutability::None => {}
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_stmt_mut<V>(v: &mut V, node: &mut crate::Stmt)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Stmt::Local(_binding_0) => {
            v.visit_local_mut(_binding_0);
        }
        crate::Stmt::Item(_binding_0) => {
            v.visit_item_mut(_binding_0);
        }
        crate::Stmt::Expr(_binding_0, _binding_1) => {
            v.visit_expr_mut(_binding_0);
            skip!(_binding_1);
        }
        crate::Stmt::Macro(_binding_0) => {
            v.visit_stmt_macro_mut(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_stmt_macro_mut<V>(v: &mut V, node: &mut crate::StmtMacro)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_macro_mut(&mut node.mac);
    skip!(node.semi_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_trait_bound_mut<V>(v: &mut V, node: &mut crate::TraitBound)
where
    V: VisitMut + ?Sized,
{
    skip!(node.paren_token);
    v.visit_trait_bound_modifier_mut(&mut node.modifier);
    if let Some(it) = &mut node.lifetimes {
        v.visit_bound_lifetimes_mut(it);
    }
    v.visit_path_mut(&mut node.path);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_trait_bound_modifier_mut<V>(v: &mut V, node: &mut crate::TraitBoundModifier)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::TraitBoundModifier::None => {}
        crate::TraitBoundModifier::Maybe(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item_mut<V>(v: &mut V, node: &mut crate::TraitItem)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::TraitItem::Const(_binding_0) => {
            v.visit_trait_item_const_mut(_binding_0);
        }
        crate::TraitItem::Fn(_binding_0) => {
            v.visit_trait_item_fn_mut(_binding_0);
        }
        crate::TraitItem::Type(_binding_0) => {
            v.visit_trait_item_type_mut(_binding_0);
        }
        crate::TraitItem::Macro(_binding_0) => {
            v.visit_trait_item_macro_mut(_binding_0);
        }
        crate::TraitItem::Verbatim(_binding_0) => {
            v.visit_token_stream_mut(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item_const_mut<V>(v: &mut V, node: &mut crate::TraitItemConst)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.const_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.colon_token);
    v.visit_type_mut(&mut node.ty);
    if let Some(it) = &mut node.default {
        skip!((it).0);
        v.visit_expr_mut(&mut (it).1);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item_fn_mut<V>(v: &mut V, node: &mut crate::TraitItemFn)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_signature_mut(&mut node.sig);
    if let Some(it) = &mut node.default {
        v.visit_block_mut(it);
    }
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item_macro_mut<V>(v: &mut V, node: &mut crate::TraitItemMacro)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_macro_mut(&mut node.mac);
    skip!(node.semi_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_trait_item_type_mut<V>(v: &mut V, node: &mut crate::TraitItemType)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    skip!(node.type_token);
    v.visit_ident_mut(&mut node.ident);
    v.visit_generics_mut(&mut node.generics);
    skip!(node.colon_token);
    for mut el in Punctuated::pairs_mut(&mut node.bounds) {
        let it = el.value_mut();
        v.visit_type_param_bound_mut(it);
    }
    if let Some(it) = &mut node.default {
        skip!((it).0);
        v.visit_type_mut(&mut (it).1);
    }
    skip!(node.semi_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_mut<V>(v: &mut V, node: &mut crate::Type)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Type::Array(_binding_0) => {
            v.visit_type_array_mut(_binding_0);
        }
        crate::Type::BareFn(_binding_0) => {
            v.visit_type_bare_fn_mut(_binding_0);
        }
        crate::Type::Group(_binding_0) => {
            v.visit_type_group_mut(_binding_0);
        }
        crate::Type::ImplTrait(_binding_0) => {
            v.visit_type_impl_trait_mut(_binding_0);
        }
        crate::Type::Infer(_binding_0) => {
            v.visit_type_infer_mut(_binding_0);
        }
        crate::Type::Macro(_binding_0) => {
            v.visit_type_macro_mut(_binding_0);
        }
        crate::Type::Never(_binding_0) => {
            v.visit_type_never_mut(_binding_0);
        }
        crate::Type::Paren(_binding_0) => {
            v.visit_type_paren_mut(_binding_0);
        }
        crate::Type::Path(_binding_0) => {
            v.visit_type_path_mut(_binding_0);
        }
        crate::Type::Ptr(_binding_0) => {
            v.visit_type_ptr_mut(_binding_0);
        }
        crate::Type::Reference(_binding_0) => {
            v.visit_type_reference_mut(_binding_0);
        }
        crate::Type::Slice(_binding_0) => {
            v.visit_type_slice_mut(_binding_0);
        }
        crate::Type::TraitObject(_binding_0) => {
            v.visit_type_trait_object_mut(_binding_0);
        }
        crate::Type::Tuple(_binding_0) => {
            v.visit_type_tuple_mut(_binding_0);
        }
        crate::Type::Verbatim(_binding_0) => {
            v.visit_token_stream_mut(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_array_mut<V>(v: &mut V, node: &mut crate::TypeArray)
where
    V: VisitMut + ?Sized,
{
    skip!(node.bracket_token);
    v.visit_type_mut(&mut *node.elem);
    skip!(node.semi_token);
    v.visit_expr_mut(&mut node.len);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_bare_fn_mut<V>(v: &mut V, node: &mut crate::TypeBareFn)
where
    V: VisitMut + ?Sized,
{
    if let Some(it) = &mut node.lifetimes {
        v.visit_bound_lifetimes_mut(it);
    }
    skip!(node.unsafety);
    if let Some(it) = &mut node.abi {
        v.visit_abi_mut(it);
    }
    skip!(node.fn_token);
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.inputs) {
        let it = el.value_mut();
        v.visit_bare_fn_arg_mut(it);
    }
    if let Some(it) = &mut node.variadic {
        v.visit_bare_variadic_mut(it);
    }
    v.visit_return_type_mut(&mut node.output);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_group_mut<V>(v: &mut V, node: &mut crate::TypeGroup)
where
    V: VisitMut + ?Sized,
{
    skip!(node.group_token);
    v.visit_type_mut(&mut *node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_impl_trait_mut<V>(v: &mut V, node: &mut crate::TypeImplTrait)
where
    V: VisitMut + ?Sized,
{
    skip!(node.impl_token);
    for mut el in Punctuated::pairs_mut(&mut node.bounds) {
        let it = el.value_mut();
        v.visit_type_param_bound_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_infer_mut<V>(v: &mut V, node: &mut crate::TypeInfer)
where
    V: VisitMut + ?Sized,
{
    skip!(node.underscore_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_macro_mut<V>(v: &mut V, node: &mut crate::TypeMacro)
where
    V: VisitMut + ?Sized,
{
    v.visit_macro_mut(&mut node.mac);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_never_mut<V>(v: &mut V, node: &mut crate::TypeNever)
where
    V: VisitMut + ?Sized,
{
    skip!(node.bang_token);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_param_mut<V>(v: &mut V, node: &mut crate::TypeParam)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_ident_mut(&mut node.ident);
    skip!(node.colon_token);
    for mut el in Punctuated::pairs_mut(&mut node.bounds) {
        let it = el.value_mut();
        v.visit_type_param_bound_mut(it);
    }
    skip!(node.eq_token);
    if let Some(it) = &mut node.default {
        v.visit_type_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_param_bound_mut<V>(v: &mut V, node: &mut crate::TypeParamBound)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::TypeParamBound::Trait(_binding_0) => {
            v.visit_trait_bound_mut(_binding_0);
        }
        crate::TypeParamBound::Lifetime(_binding_0) => {
            v.visit_lifetime_mut(_binding_0);
        }
        crate::TypeParamBound::PreciseCapture(_binding_0) => {
            full!(v.visit_precise_capture_mut(_binding_0));
        }
        crate::TypeParamBound::Verbatim(_binding_0) => {
            v.visit_token_stream_mut(_binding_0);
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_paren_mut<V>(v: &mut V, node: &mut crate::TypeParen)
where
    V: VisitMut + ?Sized,
{
    skip!(node.paren_token);
    v.visit_type_mut(&mut *node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_path_mut<V>(v: &mut V, node: &mut crate::TypePath)
where
    V: VisitMut + ?Sized,
{
    if let Some(it) = &mut node.qself {
        v.visit_qself_mut(it);
    }
    v.visit_path_mut(&mut node.path);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_ptr_mut<V>(v: &mut V, node: &mut crate::TypePtr)
where
    V: VisitMut + ?Sized,
{
    skip!(node.star_token);
    skip!(node.const_token);
    skip!(node.mutability);
    v.visit_type_mut(&mut *node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_reference_mut<V>(v: &mut V, node: &mut crate::TypeReference)
where
    V: VisitMut + ?Sized,
{
    skip!(node.and_token);
    if let Some(it) = &mut node.lifetime {
        v.visit_lifetime_mut(it);
    }
    skip!(node.mutability);
    v.visit_type_mut(&mut *node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_slice_mut<V>(v: &mut V, node: &mut crate::TypeSlice)
where
    V: VisitMut + ?Sized,
{
    skip!(node.bracket_token);
    v.visit_type_mut(&mut *node.elem);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_trait_object_mut<V>(v: &mut V, node: &mut crate::TypeTraitObject)
where
    V: VisitMut + ?Sized,
{
    skip!(node.dyn_token);
    for mut el in Punctuated::pairs_mut(&mut node.bounds) {
        let it = el.value_mut();
        v.visit_type_param_bound_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_type_tuple_mut<V>(v: &mut V, node: &mut crate::TypeTuple)
where
    V: VisitMut + ?Sized,
{
    skip!(node.paren_token);
    for mut el in Punctuated::pairs_mut(&mut node.elems) {
        let it = el.value_mut();
        v.visit_type_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_un_op_mut<V>(v: &mut V, node: &mut crate::UnOp)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::UnOp::Deref(_binding_0) => {
            skip!(_binding_0);
        }
        crate::UnOp::Not(_binding_0) => {
            skip!(_binding_0);
        }
        crate::UnOp::Neg(_binding_0) => {
            skip!(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_glob_mut<V>(v: &mut V, node: &mut crate::UseGlob)
where
    V: VisitMut + ?Sized,
{
    skip!(node.star_token);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_group_mut<V>(v: &mut V, node: &mut crate::UseGroup)
where
    V: VisitMut + ?Sized,
{
    skip!(node.brace_token);
    for mut el in Punctuated::pairs_mut(&mut node.items) {
        let it = el.value_mut();
        v.visit_use_tree_mut(it);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_name_mut<V>(v: &mut V, node: &mut crate::UseName)
where
    V: VisitMut + ?Sized,
{
    v.visit_ident_mut(&mut node.ident);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_path_mut<V>(v: &mut V, node: &mut crate::UsePath)
where
    V: VisitMut + ?Sized,
{
    v.visit_ident_mut(&mut node.ident);
    skip!(node.colon2_token);
    v.visit_use_tree_mut(&mut *node.tree);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_rename_mut<V>(v: &mut V, node: &mut crate::UseRename)
where
    V: VisitMut + ?Sized,
{
    v.visit_ident_mut(&mut node.ident);
    skip!(node.as_token);
    v.visit_ident_mut(&mut node.rename);
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_use_tree_mut<V>(v: &mut V, node: &mut crate::UseTree)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::UseTree::Path(_binding_0) => {
            v.visit_use_path_mut(_binding_0);
        }
        crate::UseTree::Name(_binding_0) => {
            v.visit_use_name_mut(_binding_0);
        }
        crate::UseTree::Rename(_binding_0) => {
            v.visit_use_rename_mut(_binding_0);
        }
        crate::UseTree::Glob(_binding_0) => {
            v.visit_use_glob_mut(_binding_0);
        }
        crate::UseTree::Group(_binding_0) => {
            v.visit_use_group_mut(_binding_0);
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "full")))]
pub fn visit_variadic_mut<V>(v: &mut V, node: &mut crate::Variadic)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    if let Some(it) = &mut node.pat {
        v.visit_pat_mut(&mut *(it).0);
        skip!((it).1);
    }
    skip!(node.dots);
    skip!(node.comma);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_variant_mut<V>(v: &mut V, node: &mut crate::Variant)
where
    V: VisitMut + ?Sized,
{
    v.visit_attributes_mut(&mut node.attrs);
    v.visit_ident_mut(&mut node.ident);
    v.visit_fields_mut(&mut node.fields);
    if let Some(it) = &mut node.discriminant {
        skip!((it).0);
        v.visit_expr_mut(&mut (it).1);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_vis_restricted_mut<V>(v: &mut V, node: &mut crate::VisRestricted)
where
    V: VisitMut + ?Sized,
{
    skip!(node.pub_token);
    skip!(node.paren_token);
    skip!(node.in_token);
    v.visit_path_mut(&mut *node.path);
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_visibility_mut<V>(v: &mut V, node: &mut crate::Visibility)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::Visibility::Public(_binding_0) => {
            skip!(_binding_0);
        }
        crate::Visibility::Restricted(_binding_0) => {
            v.visit_vis_restricted_mut(_binding_0);
        }
        crate::Visibility::Inherited => {}
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_where_clause_mut<V>(v: &mut V, node: &mut crate::WhereClause)
where
    V: VisitMut + ?Sized,
{
    skip!(node.where_token);
    for mut el in Punctuated::pairs_mut(&mut node.predicates) {
        let it = el.value_mut();
        v.visit_where_predicate_mut(it);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(any(feature = "derive", feature = "full"))))]
pub fn visit_where_predicate_mut<V>(v: &mut V, node: &mut crate::WherePredicate)
where
    V: VisitMut + ?Sized,
{
    match node {
        crate::WherePredicate::Lifetime(_binding_0) => {
            v.visit_predicate_lifetime_mut(_binding_0);
        }
        crate::WherePredicate::Type(_binding_0) => {
            v.visit_predicate_type_mut(_binding_0);
        }
    }
}
