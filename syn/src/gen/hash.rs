// This file is @generated by syn-internal-codegen.
// It is not intended for manual editing.

#[cfg(any(feature = "derive", feature = "full"))]
use crate::tt::TokenStreamHelper;
use std::hash::{Hash, Hasher};
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Abi {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.name.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::AngleBracketedGenericArguments {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.colon2_token.hash(state);
        self.args.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Arm {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.pat.hash(state);
        self.guard.hash(state);
        self.body.hash(state);
        self.comma.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::AssocConst {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.ident.hash(state);
        self.generics.hash(state);
        self.value.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::AssocType {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.ident.hash(state);
        self.generics.hash(state);
        self.ty.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::AttrStyle {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::AttrStyle::Outer => {
                state.write_u8(0u8);
            }
            crate::AttrStyle::Inner(_) => {
                state.write_u8(1u8);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Attribute {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.style.hash(state);
        self.meta.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::BareFnArg {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.name.hash(state);
        self.ty.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::BareVariadic {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.name.hash(state);
        self.comma.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::BinOp {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::BinOp::Add(_) => {
                state.write_u8(0u8);
            }
            crate::BinOp::Sub(_) => {
                state.write_u8(1u8);
            }
            crate::BinOp::Mul(_) => {
                state.write_u8(2u8);
            }
            crate::BinOp::Div(_) => {
                state.write_u8(3u8);
            }
            crate::BinOp::Rem(_) => {
                state.write_u8(4u8);
            }
            crate::BinOp::And(_) => {
                state.write_u8(5u8);
            }
            crate::BinOp::Or(_) => {
                state.write_u8(6u8);
            }
            crate::BinOp::BitXor(_) => {
                state.write_u8(7u8);
            }
            crate::BinOp::BitAnd(_) => {
                state.write_u8(8u8);
            }
            crate::BinOp::BitOr(_) => {
                state.write_u8(9u8);
            }
            crate::BinOp::Shl(_) => {
                state.write_u8(10u8);
            }
            crate::BinOp::Shr(_) => {
                state.write_u8(11u8);
            }
            crate::BinOp::Eq(_) => {
                state.write_u8(12u8);
            }
            crate::BinOp::Lt(_) => {
                state.write_u8(13u8);
            }
            crate::BinOp::Le(_) => {
                state.write_u8(14u8);
            }
            crate::BinOp::Ne(_) => {
                state.write_u8(15u8);
            }
            crate::BinOp::Ge(_) => {
                state.write_u8(16u8);
            }
            crate::BinOp::Gt(_) => {
                state.write_u8(17u8);
            }
            crate::BinOp::AddAssign(_) => {
                state.write_u8(18u8);
            }
            crate::BinOp::SubAssign(_) => {
                state.write_u8(19u8);
            }
            crate::BinOp::MulAssign(_) => {
                state.write_u8(20u8);
            }
            crate::BinOp::DivAssign(_) => {
                state.write_u8(21u8);
            }
            crate::BinOp::RemAssign(_) => {
                state.write_u8(22u8);
            }
            crate::BinOp::BitXorAssign(_) => {
                state.write_u8(23u8);
            }
            crate::BinOp::BitAndAssign(_) => {
                state.write_u8(24u8);
            }
            crate::BinOp::BitOrAssign(_) => {
                state.write_u8(25u8);
            }
            crate::BinOp::ShlAssign(_) => {
                state.write_u8(26u8);
            }
            crate::BinOp::ShrAssign(_) => {
                state.write_u8(27u8);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Block {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.stmts.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::BoundLifetimes {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.lifetimes.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::CapturedParam {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::CapturedParam::Lifetime(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::CapturedParam::Ident(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ConstParam {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.ident.hash(state);
        self.ty.hash(state);
        self.eq_token.hash(state);
        self.default.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Constraint {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.ident.hash(state);
        self.generics.hash(state);
        self.bounds.hash(state);
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Data {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::Data::Struct(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::Data::Enum(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::Data::Union(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::DataEnum {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.variants.hash(state);
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::DataStruct {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.fields.hash(state);
        self.semi_token.hash(state);
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::DataUnion {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.fields.hash(state);
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::DeriveInput {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.data.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Expr {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            #[cfg(feature = "full")]
            crate::Expr::Array(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Assign(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Async(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Await(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::Expr::Binary(v0) => {
                state.write_u8(4u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Block(v0) => {
                state.write_u8(5u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Break(v0) => {
                state.write_u8(6u8);
                v0.hash(state);
            }
            crate::Expr::Call(v0) => {
                state.write_u8(7u8);
                v0.hash(state);
            }
            crate::Expr::Cast(v0) => {
                state.write_u8(8u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Closure(v0) => {
                state.write_u8(9u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Const(v0) => {
                state.write_u8(10u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Continue(v0) => {
                state.write_u8(11u8);
                v0.hash(state);
            }
            crate::Expr::Field(v0) => {
                state.write_u8(12u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::ForLoop(v0) => {
                state.write_u8(13u8);
                v0.hash(state);
            }
            crate::Expr::Group(v0) => {
                state.write_u8(14u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::If(v0) => {
                state.write_u8(15u8);
                v0.hash(state);
            }
            crate::Expr::Index(v0) => {
                state.write_u8(16u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Infer(v0) => {
                state.write_u8(17u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Let(v0) => {
                state.write_u8(18u8);
                v0.hash(state);
            }
            crate::Expr::Lit(v0) => {
                state.write_u8(19u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Loop(v0) => {
                state.write_u8(20u8);
                v0.hash(state);
            }
            crate::Expr::Macro(v0) => {
                state.write_u8(21u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Match(v0) => {
                state.write_u8(22u8);
                v0.hash(state);
            }
            crate::Expr::MethodCall(v0) => {
                state.write_u8(23u8);
                v0.hash(state);
            }
            crate::Expr::Paren(v0) => {
                state.write_u8(24u8);
                v0.hash(state);
            }
            crate::Expr::Path(v0) => {
                state.write_u8(25u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Range(v0) => {
                state.write_u8(26u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::RawAddr(v0) => {
                state.write_u8(27u8);
                v0.hash(state);
            }
            crate::Expr::Reference(v0) => {
                state.write_u8(28u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Repeat(v0) => {
                state.write_u8(29u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Return(v0) => {
                state.write_u8(30u8);
                v0.hash(state);
            }
            crate::Expr::Struct(v0) => {
                state.write_u8(31u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Try(v0) => {
                state.write_u8(32u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::TryBlock(v0) => {
                state.write_u8(33u8);
                v0.hash(state);
            }
            crate::Expr::Tuple(v0) => {
                state.write_u8(34u8);
                v0.hash(state);
            }
            crate::Expr::Unary(v0) => {
                state.write_u8(35u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Unsafe(v0) => {
                state.write_u8(36u8);
                v0.hash(state);
            }
            crate::Expr::Verbatim(v0) => {
                state.write_u8(37u8);
                TokenStreamHelper(v0).hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::While(v0) => {
                state.write_u8(38u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::Expr::Yield(v0) => {
                state.write_u8(39u8);
                v0.hash(state);
            }
            #[cfg(not(feature = "full"))]
            _ => unreachable!(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprArray {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.elems.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprAssign {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.left.hash(state);
        self.right.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprAsync {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.capture.hash(state);
        self.block.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprAwait {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.base.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprBinary {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.left.hash(state);
        self.op.hash(state);
        self.right.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprBlock {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.label.hash(state);
        self.block.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprBreak {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.label.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprCall {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.func.hash(state);
        self.args.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprCast {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.expr.hash(state);
        self.ty.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprClosure {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.lifetimes.hash(state);
        self.constness.hash(state);
        self.movability.hash(state);
        self.asyncness.hash(state);
        self.capture.hash(state);
        self.inputs.hash(state);
        self.output.hash(state);
        self.body.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprConst {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.block.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprContinue {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.label.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprField {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.base.hash(state);
        self.member.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprForLoop {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.label.hash(state);
        self.pat.hash(state);
        self.expr.hash(state);
        self.body.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprGroup {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprIf {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.cond.hash(state);
        self.then_branch.hash(state);
        self.else_branch.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprIndex {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.expr.hash(state);
        self.index.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprInfer {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprLet {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.pat.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprLit {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.lit.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprLoop {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.label.hash(state);
        self.body.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprMacro {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.mac.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprMatch {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.expr.hash(state);
        self.arms.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprMethodCall {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.receiver.hash(state);
        self.method.hash(state);
        self.turbofish.hash(state);
        self.args.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprParen {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprPath {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.qself.hash(state);
        self.path.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprRange {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.start.hash(state);
        self.limits.hash(state);
        self.end.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprRawAddr {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.mutability.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprReference {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.mutability.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprRepeat {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.expr.hash(state);
        self.len.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprReturn {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprStruct {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.qself.hash(state);
        self.path.hash(state);
        self.fields.hash(state);
        self.dot2_token.hash(state);
        self.rest.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprTry {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprTryBlock {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.block.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprTuple {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.elems.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprUnary {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.op.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprUnsafe {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.block.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprWhile {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.label.hash(state);
        self.cond.hash(state);
        self.body.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ExprYield {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Field {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.mutability.hash(state);
        self.ident.hash(state);
        self.colon_token.hash(state);
        self.ty.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::FieldMutability {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::FieldMutability::None => {
                state.write_u8(0u8);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::FieldPat {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.member.hash(state);
        self.colon_token.hash(state);
        self.pat.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::FieldValue {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.member.hash(state);
        self.colon_token.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Fields {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::Fields::Named(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::Fields::Unnamed(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::Fields::Unit => {
                state.write_u8(2u8);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::FieldsNamed {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.named.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::FieldsUnnamed {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.unnamed.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::File {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.shebang.hash(state);
        self.attrs.hash(state);
        self.items.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::FnArg {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::FnArg::Receiver(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::FnArg::Typed(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ForeignItem {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::ForeignItem::Fn(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::ForeignItem::Static(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::ForeignItem::Type(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::ForeignItem::Macro(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::ForeignItem::Verbatim(v0) => {
                state.write_u8(4u8);
                TokenStreamHelper(v0).hash(state);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ForeignItemFn {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.sig.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ForeignItemMacro {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.mac.hash(state);
        self.semi_token.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ForeignItemStatic {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.mutability.hash(state);
        self.ident.hash(state);
        self.ty.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ForeignItemType {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::GenericArgument {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::GenericArgument::Lifetime(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::GenericArgument::Type(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::GenericArgument::Const(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::GenericArgument::AssocType(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::GenericArgument::AssocConst(v0) => {
                state.write_u8(4u8);
                v0.hash(state);
            }
            crate::GenericArgument::Constraint(v0) => {
                state.write_u8(5u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::GenericParam {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::GenericParam::Lifetime(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::GenericParam::Type(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::GenericParam::Const(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Generics {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.lt_token.hash(state);
        self.params.hash(state);
        self.gt_token.hash(state);
        self.where_clause.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ImplItem {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::ImplItem::Const(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::ImplItem::Fn(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::ImplItem::Type(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::ImplItem::Macro(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::ImplItem::Verbatim(v0) => {
                state.write_u8(4u8);
                TokenStreamHelper(v0).hash(state);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ImplItemConst {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.defaultness.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.ty.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ImplItemFn {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.defaultness.hash(state);
        self.sig.hash(state);
        self.block.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ImplItemMacro {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.mac.hash(state);
        self.semi_token.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ImplItemType {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.defaultness.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.ty.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ImplRestriction {
    fn hash<H>(&self, _state: &mut H)
    where
        H: Hasher,
    {
        match *self {}
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Item {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::Item::Const(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::Item::Enum(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::Item::ExternCrate(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::Item::Fn(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::Item::ForeignMod(v0) => {
                state.write_u8(4u8);
                v0.hash(state);
            }
            crate::Item::Impl(v0) => {
                state.write_u8(5u8);
                v0.hash(state);
            }
            crate::Item::Macro(v0) => {
                state.write_u8(6u8);
                v0.hash(state);
            }
            crate::Item::Mod(v0) => {
                state.write_u8(7u8);
                v0.hash(state);
            }
            crate::Item::Static(v0) => {
                state.write_u8(8u8);
                v0.hash(state);
            }
            crate::Item::Struct(v0) => {
                state.write_u8(9u8);
                v0.hash(state);
            }
            crate::Item::Trait(v0) => {
                state.write_u8(10u8);
                v0.hash(state);
            }
            crate::Item::TraitAlias(v0) => {
                state.write_u8(11u8);
                v0.hash(state);
            }
            crate::Item::Type(v0) => {
                state.write_u8(12u8);
                v0.hash(state);
            }
            crate::Item::Union(v0) => {
                state.write_u8(13u8);
                v0.hash(state);
            }
            crate::Item::Use(v0) => {
                state.write_u8(14u8);
                v0.hash(state);
            }
            crate::Item::Verbatim(v0) => {
                state.write_u8(15u8);
                TokenStreamHelper(v0).hash(state);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemConst {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.ty.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemEnum {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.variants.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemExternCrate {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.ident.hash(state);
        self.rename.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemFn {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.sig.hash(state);
        self.block.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemForeignMod {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.unsafety.hash(state);
        self.abi.hash(state);
        self.items.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemImpl {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.defaultness.hash(state);
        self.unsafety.hash(state);
        self.generics.hash(state);
        self.trait_.hash(state);
        self.self_ty.hash(state);
        self.items.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemMacro {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.ident.hash(state);
        self.mac.hash(state);
        self.semi_token.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemMod {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.unsafety.hash(state);
        self.ident.hash(state);
        self.content.hash(state);
        self.semi.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemStatic {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.mutability.hash(state);
        self.ident.hash(state);
        self.ty.hash(state);
        self.expr.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemStruct {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.fields.hash(state);
        self.semi_token.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemTrait {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.unsafety.hash(state);
        self.auto_token.hash(state);
        self.restriction.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.colon_token.hash(state);
        self.supertraits.hash(state);
        self.items.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemTraitAlias {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.bounds.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemType {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.ty.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemUnion {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.fields.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ItemUse {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.vis.hash(state);
        self.leading_colon.hash(state);
        self.tree.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Label {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.name.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::LifetimeParam {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.lifetime.hash(state);
        self.colon_token.hash(state);
        self.bounds.hash(state);
    }
}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Lit {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::Lit::Str(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::Lit::ByteStr(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::Lit::CStr(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::Lit::Byte(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::Lit::Char(v0) => {
                state.write_u8(4u8);
                v0.hash(state);
            }
            crate::Lit::Int(v0) => {
                state.write_u8(5u8);
                v0.hash(state);
            }
            crate::Lit::Float(v0) => {
                state.write_u8(6u8);
                v0.hash(state);
            }
            crate::Lit::Bool(v0) => {
                state.write_u8(7u8);
                v0.hash(state);
            }
            crate::Lit::Verbatim(v0) => {
                state.write_u8(8u8);
                v0.to_string().hash(state);
            }
        }
    }
}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::LitBool {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.value.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Local {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.pat.hash(state);
        self.init.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::LocalInit {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.expr.hash(state);
        self.diverge.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Macro {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.path.hash(state);
        self.delimiter.hash(state);
        TokenStreamHelper(&self.tokens).hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::MacroDelimiter {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::MacroDelimiter::Paren(_) => {
                state.write_u8(0u8);
            }
            crate::MacroDelimiter::Brace(_) => {
                state.write_u8(1u8);
            }
            crate::MacroDelimiter::Bracket(_) => {
                state.write_u8(2u8);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Meta {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::Meta::Path(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::Meta::List(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::Meta::NameValue(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::MetaList {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.path.hash(state);
        self.delimiter.hash(state);
        TokenStreamHelper(&self.tokens).hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::MetaNameValue {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.path.hash(state);
        self.value.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ParenthesizedGenericArguments {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.inputs.hash(state);
        self.output.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Pat {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::Pat::Const(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::Pat::Ident(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::Pat::Lit(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::Pat::Macro(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::Pat::Or(v0) => {
                state.write_u8(4u8);
                v0.hash(state);
            }
            crate::Pat::Paren(v0) => {
                state.write_u8(5u8);
                v0.hash(state);
            }
            crate::Pat::Path(v0) => {
                state.write_u8(6u8);
                v0.hash(state);
            }
            crate::Pat::Range(v0) => {
                state.write_u8(7u8);
                v0.hash(state);
            }
            crate::Pat::Reference(v0) => {
                state.write_u8(8u8);
                v0.hash(state);
            }
            crate::Pat::Rest(v0) => {
                state.write_u8(9u8);
                v0.hash(state);
            }
            crate::Pat::Slice(v0) => {
                state.write_u8(10u8);
                v0.hash(state);
            }
            crate::Pat::Struct(v0) => {
                state.write_u8(11u8);
                v0.hash(state);
            }
            crate::Pat::Tuple(v0) => {
                state.write_u8(12u8);
                v0.hash(state);
            }
            crate::Pat::TupleStruct(v0) => {
                state.write_u8(13u8);
                v0.hash(state);
            }
            crate::Pat::Type(v0) => {
                state.write_u8(14u8);
                v0.hash(state);
            }
            crate::Pat::Verbatim(v0) => {
                state.write_u8(15u8);
                TokenStreamHelper(v0).hash(state);
            }
            crate::Pat::Wild(v0) => {
                state.write_u8(16u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatIdent {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.by_ref.hash(state);
        self.mutability.hash(state);
        self.ident.hash(state);
        self.subpat.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatOr {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.leading_vert.hash(state);
        self.cases.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatParen {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.pat.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatReference {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.mutability.hash(state);
        self.pat.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatRest {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatSlice {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.elems.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatStruct {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.qself.hash(state);
        self.path.hash(state);
        self.fields.hash(state);
        self.rest.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatTuple {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.elems.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatTupleStruct {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.qself.hash(state);
        self.path.hash(state);
        self.elems.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatType {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.pat.hash(state);
        self.ty.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PatWild {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Path {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.leading_colon.hash(state);
        self.segments.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PathArguments {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::PathArguments::None => {
                state.write_u8(0u8);
            }
            crate::PathArguments::AngleBracketed(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::PathArguments::Parenthesized(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PathSegment {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.ident.hash(state);
        self.arguments.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PointerMutability {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::PointerMutability::Const(_) => {
                state.write_u8(0u8);
            }
            crate::PointerMutability::Mut(_) => {
                state.write_u8(1u8);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PreciseCapture {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.params.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PredicateLifetime {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.lifetime.hash(state);
        self.bounds.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::PredicateType {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.lifetimes.hash(state);
        self.bounded_ty.hash(state);
        self.bounds.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::QSelf {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.ty.hash(state);
        self.position.hash(state);
        self.as_token.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::RangeLimits {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::RangeLimits::HalfOpen(_) => {
                state.write_u8(0u8);
            }
            crate::RangeLimits::Closed(_) => {
                state.write_u8(1u8);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Receiver {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.reference.hash(state);
        self.mutability.hash(state);
        self.colon_token.hash(state);
        self.ty.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::ReturnType {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::ReturnType::Default => {
                state.write_u8(0u8);
            }
            crate::ReturnType::Type(_, v1) => {
                state.write_u8(1u8);
                v1.hash(state);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Signature {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.constness.hash(state);
        self.asyncness.hash(state);
        self.unsafety.hash(state);
        self.abi.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.inputs.hash(state);
        self.variadic.hash(state);
        self.output.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::StaticMutability {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::StaticMutability::Mut(_) => {
                state.write_u8(0u8);
            }
            crate::StaticMutability::None => {
                state.write_u8(1u8);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Stmt {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::Stmt::Local(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::Stmt::Item(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::Stmt::Expr(v0, v1) => {
                state.write_u8(2u8);
                v0.hash(state);
                v1.hash(state);
            }
            crate::Stmt::Macro(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::StmtMacro {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.mac.hash(state);
        self.semi_token.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TraitBound {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.paren_token.hash(state);
        self.modifier.hash(state);
        self.lifetimes.hash(state);
        self.path.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TraitBoundModifier {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::TraitBoundModifier::None => {
                state.write_u8(0u8);
            }
            crate::TraitBoundModifier::Maybe(_) => {
                state.write_u8(1u8);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TraitItem {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::TraitItem::Const(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::TraitItem::Fn(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::TraitItem::Type(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::TraitItem::Macro(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::TraitItem::Verbatim(v0) => {
                state.write_u8(4u8);
                TokenStreamHelper(v0).hash(state);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TraitItemConst {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.ty.hash(state);
        self.default.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TraitItemFn {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.sig.hash(state);
        self.default.hash(state);
        self.semi_token.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TraitItemMacro {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.mac.hash(state);
        self.semi_token.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TraitItemType {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.ident.hash(state);
        self.generics.hash(state);
        self.colon_token.hash(state);
        self.bounds.hash(state);
        self.default.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Type {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::Type::Array(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::Type::BareFn(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::Type::Group(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::Type::ImplTrait(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::Type::Infer(v0) => {
                state.write_u8(4u8);
                v0.hash(state);
            }
            crate::Type::Macro(v0) => {
                state.write_u8(5u8);
                v0.hash(state);
            }
            crate::Type::Never(v0) => {
                state.write_u8(6u8);
                v0.hash(state);
            }
            crate::Type::Paren(v0) => {
                state.write_u8(7u8);
                v0.hash(state);
            }
            crate::Type::Path(v0) => {
                state.write_u8(8u8);
                v0.hash(state);
            }
            crate::Type::Ptr(v0) => {
                state.write_u8(9u8);
                v0.hash(state);
            }
            crate::Type::Reference(v0) => {
                state.write_u8(10u8);
                v0.hash(state);
            }
            crate::Type::Slice(v0) => {
                state.write_u8(11u8);
                v0.hash(state);
            }
            crate::Type::TraitObject(v0) => {
                state.write_u8(12u8);
                v0.hash(state);
            }
            crate::Type::Tuple(v0) => {
                state.write_u8(13u8);
                v0.hash(state);
            }
            crate::Type::Verbatim(v0) => {
                state.write_u8(14u8);
                TokenStreamHelper(v0).hash(state);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeArray {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.elem.hash(state);
        self.len.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeBareFn {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.lifetimes.hash(state);
        self.unsafety.hash(state);
        self.abi.hash(state);
        self.inputs.hash(state);
        self.variadic.hash(state);
        self.output.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeGroup {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.elem.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeImplTrait {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.bounds.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeInfer {
    fn hash<H>(&self, _state: &mut H)
    where
        H: Hasher,
    {}
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeMacro {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.mac.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeNever {
    fn hash<H>(&self, _state: &mut H)
    where
        H: Hasher,
    {}
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeParam {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.ident.hash(state);
        self.colon_token.hash(state);
        self.bounds.hash(state);
        self.eq_token.hash(state);
        self.default.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeParamBound {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::TypeParamBound::Trait(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::TypeParamBound::Lifetime(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            #[cfg(feature = "full")]
            crate::TypeParamBound::PreciseCapture(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::TypeParamBound::Verbatim(v0) => {
                state.write_u8(3u8);
                TokenStreamHelper(v0).hash(state);
            }
            #[cfg(not(feature = "full"))]
            _ => unreachable!(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeParen {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.elem.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypePath {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.qself.hash(state);
        self.path.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypePtr {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.const_token.hash(state);
        self.mutability.hash(state);
        self.elem.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeReference {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.lifetime.hash(state);
        self.mutability.hash(state);
        self.elem.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeSlice {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.elem.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeTraitObject {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.dyn_token.hash(state);
        self.bounds.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::TypeTuple {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.elems.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::UnOp {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::UnOp::Deref(_) => {
                state.write_u8(0u8);
            }
            crate::UnOp::Not(_) => {
                state.write_u8(1u8);
            }
            crate::UnOp::Neg(_) => {
                state.write_u8(2u8);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::UseGlob {
    fn hash<H>(&self, _state: &mut H)
    where
        H: Hasher,
    {}
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::UseGroup {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.items.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::UseName {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.ident.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::UsePath {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.ident.hash(state);
        self.tree.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::UseRename {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.ident.hash(state);
        self.rename.hash(state);
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::UseTree {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::UseTree::Path(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::UseTree::Name(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::UseTree::Rename(v0) => {
                state.write_u8(2u8);
                v0.hash(state);
            }
            crate::UseTree::Glob(v0) => {
                state.write_u8(3u8);
                v0.hash(state);
            }
            crate::UseTree::Group(v0) => {
                state.write_u8(4u8);
                v0.hash(state);
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Variadic {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.pat.hash(state);
        self.comma.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Variant {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.attrs.hash(state);
        self.ident.hash(state);
        self.fields.hash(state);
        self.discriminant.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::VisRestricted {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.in_token.hash(state);
        self.path.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::Visibility {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::Visibility::Public(_) => {
                state.write_u8(0u8);
            }
            crate::Visibility::Restricted(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
            crate::Visibility::Inherited => {
                state.write_u8(2u8);
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::WhereClause {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        self.predicates.hash(state);
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Hash for crate::WherePredicate {
    fn hash<H>(&self, state: &mut H)
    where
        H: Hasher,
    {
        match self {
            crate::WherePredicate::Lifetime(v0) => {
                state.write_u8(0u8);
                v0.hash(state);
            }
            crate::WherePredicate::Type(v0) => {
                state.write_u8(1u8);
                v0.hash(state);
            }
        }
    }
}
