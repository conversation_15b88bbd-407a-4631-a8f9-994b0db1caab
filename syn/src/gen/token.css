a.struct[title="struct syn::token::Abstract"],
a.struct[title="struct syn::token::And"],
a.struct[title="struct syn::token::AndAnd"],
a.struct[title="struct syn::token::AndEq"],
a.struct[title="struct syn::token::As"],
a.struct[title="struct syn::token::Async"],
a.struct[title="struct syn::token::At"],
a.struct[title="struct syn::token::Auto"],
a.struct[title="struct syn::token::Await"],
a.struct[title="struct syn::token::Become"],
a.struct[title="struct syn::token::Box"],
a.struct[title="struct syn::token::Break"],
a.struct[title="struct syn::token::Caret"],
a.struct[title="struct syn::token::CaretEq"],
a.struct[title="struct syn::token::Colon"],
a.struct[title="struct syn::token::Comma"],
a.struct[title="struct syn::token::Const"],
a.struct[title="struct syn::token::Continue"],
a.struct[title="struct syn::token::Crate"],
a.struct[title="struct syn::token::Default"],
a.struct[title="struct syn::token::Do"],
a.struct[title="struct syn::token::Dollar"],
a.struct[title="struct syn::token::Dot"],
a.struct[title="struct syn::token::DotDot"],
a.struct[title="struct syn::token::DotDotDot"],
a.struct[title="struct syn::token::DotDotEq"],
a.struct[title="struct syn::token::Dyn"],
a.struct[title="struct syn::token::Else"],
a.struct[title="struct syn::token::Enum"],
a.struct[title="struct syn::token::Eq"],
a.struct[title="struct syn::token::EqEq"],
a.struct[title="struct syn::token::Extern"],
a.struct[title="struct syn::token::FatArrow"],
a.struct[title="struct syn::token::Final"],
a.struct[title="struct syn::token::Fn"],
a.struct[title="struct syn::token::For"],
a.struct[title="struct syn::token::Ge"],
a.struct[title="struct syn::token::Gt"],
a.struct[title="struct syn::token::If"],
a.struct[title="struct syn::token::Impl"],
a.struct[title="struct syn::token::In"],
a.struct[title="struct syn::token::LArrow"],
a.struct[title="struct syn::token::Le"],
a.struct[title="struct syn::token::Let"],
a.struct[title="struct syn::token::Loop"],
a.struct[title="struct syn::token::Lt"],
a.struct[title="struct syn::token::Macro"],
a.struct[title="struct syn::token::Match"],
a.struct[title="struct syn::token::Minus"],
a.struct[title="struct syn::token::MinusEq"],
a.struct[title="struct syn::token::Mod"],
a.struct[title="struct syn::token::Move"],
a.struct[title="struct syn::token::Mut"],
a.struct[title="struct syn::token::Ne"],
a.struct[title="struct syn::token::Not"],
a.struct[title="struct syn::token::Or"],
a.struct[title="struct syn::token::OrEq"],
a.struct[title="struct syn::token::OrOr"],
a.struct[title="struct syn::token::Override"],
a.struct[title="struct syn::token::PathSep"],
a.struct[title="struct syn::token::Percent"],
a.struct[title="struct syn::token::PercentEq"],
a.struct[title="struct syn::token::Plus"],
a.struct[title="struct syn::token::PlusEq"],
a.struct[title="struct syn::token::Pound"],
a.struct[title="struct syn::token::Priv"],
a.struct[title="struct syn::token::Pub"],
a.struct[title="struct syn::token::Question"],
a.struct[title="struct syn::token::RArrow"],
a.struct[title="struct syn::token::Raw"],
a.struct[title="struct syn::token::Ref"],
a.struct[title="struct syn::token::Return"],
a.struct[title="struct syn::token::SelfType"],
a.struct[title="struct syn::token::SelfValue"],
a.struct[title="struct syn::token::Semi"],
a.struct[title="struct syn::token::Shl"],
a.struct[title="struct syn::token::ShlEq"],
a.struct[title="struct syn::token::Shr"],
a.struct[title="struct syn::token::ShrEq"],
a.struct[title="struct syn::token::Slash"],
a.struct[title="struct syn::token::SlashEq"],
a.struct[title="struct syn::token::Star"],
a.struct[title="struct syn::token::StarEq"],
a.struct[title="struct syn::token::Static"],
a.struct[title="struct syn::token::Struct"],
a.struct[title="struct syn::token::Super"],
a.struct[title="struct syn::token::Tilde"],
a.struct[title="struct syn::token::Trait"],
a.struct[title="struct syn::token::Try"],
a.struct[title="struct syn::token::Type"],
a.struct[title="struct syn::token::Typeof"],
a.struct[title="struct syn::token::Underscore"],
a.struct[title="struct syn::token::Union"],
a.struct[title="struct syn::token::Unsafe"],
a.struct[title="struct syn::token::Unsized"],
a.struct[title="struct syn::token::Use"],
a.struct[title="struct syn::token::Virtual"],
a.struct[title="struct syn::token::Where"],
a.struct[title="struct syn::token::While"],
a.struct[title="struct syn::token::Yield"] {
	display: inline-block;
	color: transparent;
	white-space: nowrap;
}

a.struct[title="struct syn::token::Abstract"]::before,
a.struct[title="struct syn::token::And"]::before,
a.struct[title="struct syn::token::AndAnd"]::before,
a.struct[title="struct syn::token::AndEq"]::before,
a.struct[title="struct syn::token::As"]::before,
a.struct[title="struct syn::token::Async"]::before,
a.struct[title="struct syn::token::At"]::before,
a.struct[title="struct syn::token::Auto"]::before,
a.struct[title="struct syn::token::Await"]::before,
a.struct[title="struct syn::token::Become"]::before,
a.struct[title="struct syn::token::Box"]::before,
a.struct[title="struct syn::token::Break"]::before,
a.struct[title="struct syn::token::Caret"]::before,
a.struct[title="struct syn::token::CaretEq"]::before,
a.struct[title="struct syn::token::Colon"]::before,
a.struct[title="struct syn::token::Comma"]::before,
a.struct[title="struct syn::token::Const"]::before,
a.struct[title="struct syn::token::Continue"]::before,
a.struct[title="struct syn::token::Crate"]::before,
a.struct[title="struct syn::token::Default"]::before,
a.struct[title="struct syn::token::Do"]::before,
a.struct[title="struct syn::token::Dollar"]::before,
a.struct[title="struct syn::token::Dot"]::before,
a.struct[title="struct syn::token::DotDot"]::before,
a.struct[title="struct syn::token::DotDotDot"]::before,
a.struct[title="struct syn::token::DotDotEq"]::before,
a.struct[title="struct syn::token::Dyn"]::before,
a.struct[title="struct syn::token::Else"]::before,
a.struct[title="struct syn::token::Enum"]::before,
a.struct[title="struct syn::token::Eq"]::before,
a.struct[title="struct syn::token::EqEq"]::before,
a.struct[title="struct syn::token::Extern"]::before,
a.struct[title="struct syn::token::FatArrow"]::before,
a.struct[title="struct syn::token::Final"]::before,
a.struct[title="struct syn::token::Fn"]::before,
a.struct[title="struct syn::token::For"]::before,
a.struct[title="struct syn::token::Ge"]::before,
a.struct[title="struct syn::token::Gt"]::before,
a.struct[title="struct syn::token::If"]::before,
a.struct[title="struct syn::token::Impl"]::before,
a.struct[title="struct syn::token::In"]::before,
a.struct[title="struct syn::token::LArrow"]::before,
a.struct[title="struct syn::token::Le"]::before,
a.struct[title="struct syn::token::Let"]::before,
a.struct[title="struct syn::token::Loop"]::before,
a.struct[title="struct syn::token::Lt"]::before,
a.struct[title="struct syn::token::Macro"]::before,
a.struct[title="struct syn::token::Match"]::before,
a.struct[title="struct syn::token::Minus"]::before,
a.struct[title="struct syn::token::MinusEq"]::before,
a.struct[title="struct syn::token::Mod"]::before,
a.struct[title="struct syn::token::Move"]::before,
a.struct[title="struct syn::token::Mut"]::before,
a.struct[title="struct syn::token::Ne"]::before,
a.struct[title="struct syn::token::Not"]::before,
a.struct[title="struct syn::token::Or"]::before,
a.struct[title="struct syn::token::OrEq"]::before,
a.struct[title="struct syn::token::OrOr"]::before,
a.struct[title="struct syn::token::Override"]::before,
a.struct[title="struct syn::token::PathSep"]::before,
a.struct[title="struct syn::token::Percent"]::before,
a.struct[title="struct syn::token::PercentEq"]::before,
a.struct[title="struct syn::token::Plus"]::before,
a.struct[title="struct syn::token::PlusEq"]::before,
a.struct[title="struct syn::token::Pound"]::before,
a.struct[title="struct syn::token::Priv"]::before,
a.struct[title="struct syn::token::Pub"]::before,
a.struct[title="struct syn::token::Question"]::before,
a.struct[title="struct syn::token::RArrow"]::before,
a.struct[title="struct syn::token::Raw"]::before,
a.struct[title="struct syn::token::Ref"]::before,
a.struct[title="struct syn::token::Return"]::before,
a.struct[title="struct syn::token::SelfType"]::before,
a.struct[title="struct syn::token::SelfValue"]::before,
a.struct[title="struct syn::token::Semi"]::before,
a.struct[title="struct syn::token::Shl"]::before,
a.struct[title="struct syn::token::ShlEq"]::before,
a.struct[title="struct syn::token::Shr"]::before,
a.struct[title="struct syn::token::ShrEq"]::before,
a.struct[title="struct syn::token::Slash"]::before,
a.struct[title="struct syn::token::SlashEq"]::before,
a.struct[title="struct syn::token::Star"]::before,
a.struct[title="struct syn::token::StarEq"]::before,
a.struct[title="struct syn::token::Static"]::before,
a.struct[title="struct syn::token::Struct"]::before,
a.struct[title="struct syn::token::Super"]::before,
a.struct[title="struct syn::token::Tilde"]::before,
a.struct[title="struct syn::token::Trait"]::before,
a.struct[title="struct syn::token::Try"]::before,
a.struct[title="struct syn::token::Type"]::before,
a.struct[title="struct syn::token::Typeof"]::before,
a.struct[title="struct syn::token::Underscore"]::before,
a.struct[title="struct syn::token::Union"]::before,
a.struct[title="struct syn::token::Unsafe"]::before,
a.struct[title="struct syn::token::Unsized"]::before,
a.struct[title="struct syn::token::Use"]::before,
a.struct[title="struct syn::token::Virtual"]::before,
a.struct[title="struct syn::token::Where"]::before,
a.struct[title="struct syn::token::While"]::before,
a.struct[title="struct syn::token::Yield"]::before {
	display: inline-block;
	color: var(--type-link-color);
	width: 0;
}

a.struct[title="struct syn::token::Abstract"]::before {
	content: "Token![abstract]";
}

a.struct[title="struct syn::token::And"]::before {
	content: "Token![&]";
}

a.struct[title="struct syn::token::AndAnd"]::before {
	content: "Token![&&]";
}

a.struct[title="struct syn::token::AndEq"]::before {
	content: "Token![&=]";
}

a.struct[title="struct syn::token::As"]::before {
	content: "Token![as]";
}

a.struct[title="struct syn::token::Async"]::before {
	content: "Token![async]";
}

a.struct[title="struct syn::token::At"]::before {
	content: "Token![@]";
}

a.struct[title="struct syn::token::Auto"]::before {
	content: "Token![auto]";
}

a.struct[title="struct syn::token::Await"]::before {
	content: "Token![await]";
}

a.struct[title="struct syn::token::Become"]::before {
	content: "Token![become]";
}

a.struct[title="struct syn::token::Box"]::before {
	content: "Token![box]";
}

a.struct[title="struct syn::token::Break"]::before {
	content: "Token![break]";
}

a.struct[title="struct syn::token::Caret"]::before {
	content: "Token![^]";
}

a.struct[title="struct syn::token::CaretEq"]::before {
	content: "Token![^=]";
}

a.struct[title="struct syn::token::Colon"]::before {
	content: "Token![:]";
}

a.struct[title="struct syn::token::Comma"]::before {
	content: "Token![,]";
}

a.struct[title="struct syn::token::Const"]::before {
	content: "Token![const]";
}

a.struct[title="struct syn::token::Continue"]::before {
	content: "Token![continue]";
}

a.struct[title="struct syn::token::Crate"]::before {
	content: "Token![crate]";
}

a.struct[title="struct syn::token::Default"]::before {
	content: "Token![default]";
}

a.struct[title="struct syn::token::Do"]::before {
	content: "Token![do]";
}

a.struct[title="struct syn::token::Dollar"]::before {
	content: "Token![$]";
}

a.struct[title="struct syn::token::Dot"]::before {
	content: "Token![.]";
}

a.struct[title="struct syn::token::DotDot"]::before {
	content: "Token![..]";
}

a.struct[title="struct syn::token::DotDotDot"]::before {
	content: "Token![...]";
}

a.struct[title="struct syn::token::DotDotEq"]::before {
	content: "Token![..=]";
}

a.struct[title="struct syn::token::Dyn"]::before {
	content: "Token![dyn]";
}

a.struct[title="struct syn::token::Else"]::before {
	content: "Token![else]";
}

a.struct[title="struct syn::token::Enum"]::before {
	content: "Token![enum]";
}

a.struct[title="struct syn::token::Eq"]::before {
	content: "Token![=]";
}

a.struct[title="struct syn::token::EqEq"]::before {
	content: "Token![==]";
}

a.struct[title="struct syn::token::Extern"]::before {
	content: "Token![extern]";
}

a.struct[title="struct syn::token::FatArrow"]::before {
	content: "Token![=>]";
}

a.struct[title="struct syn::token::Final"]::before {
	content: "Token![final]";
}

a.struct[title="struct syn::token::Fn"]::before {
	content: "Token![fn]";
}

a.struct[title="struct syn::token::For"]::before {
	content: "Token![for]";
}

a.struct[title="struct syn::token::Ge"]::before {
	content: "Token![>=]";
}

a.struct[title="struct syn::token::Gt"]::before {
	content: "Token![>]";
}

a.struct[title="struct syn::token::If"]::before {
	content: "Token![if]";
}

a.struct[title="struct syn::token::Impl"]::before {
	content: "Token![impl]";
}

a.struct[title="struct syn::token::In"]::before {
	content: "Token![in]";
}

a.struct[title="struct syn::token::LArrow"]::before {
	content: "Token![<-]";
}

a.struct[title="struct syn::token::Le"]::before {
	content: "Token![<=]";
}

a.struct[title="struct syn::token::Let"]::before {
	content: "Token![let]";
}

a.struct[title="struct syn::token::Loop"]::before {
	content: "Token![loop]";
}

a.struct[title="struct syn::token::Lt"]::before {
	content: "Token![<]";
}

a.struct[title="struct syn::token::Macro"]::before {
	content: "Token![macro]";
}

a.struct[title="struct syn::token::Match"]::before {
	content: "Token![match]";
}

a.struct[title="struct syn::token::Minus"]::before {
	content: "Token![-]";
}

a.struct[title="struct syn::token::MinusEq"]::before {
	content: "Token![-=]";
}

a.struct[title="struct syn::token::Mod"]::before {
	content: "Token![mod]";
}

a.struct[title="struct syn::token::Move"]::before {
	content: "Token![move]";
}

a.struct[title="struct syn::token::Mut"]::before {
	content: "Token![mut]";
}

a.struct[title="struct syn::token::Ne"]::before {
	content: "Token![!=]";
}

a.struct[title="struct syn::token::Not"]::before {
	content: "Token![!]";
}

a.struct[title="struct syn::token::Or"]::before {
	content: "Token![|]";
}

a.struct[title="struct syn::token::OrEq"]::before {
	content: "Token![|=]";
}

a.struct[title="struct syn::token::OrOr"]::before {
	content: "Token![||]";
}

a.struct[title="struct syn::token::Override"]::before {
	content: "Token![override]";
}

a.struct[title="struct syn::token::PathSep"]::before {
	content: "Token![::]";
}

a.struct[title="struct syn::token::Percent"]::before {
	content: "Token![%]";
}

a.struct[title="struct syn::token::PercentEq"]::before {
	content: "Token![%=]";
}

a.struct[title="struct syn::token::Plus"]::before {
	content: "Token![+]";
}

a.struct[title="struct syn::token::PlusEq"]::before {
	content: "Token![+=]";
}

a.struct[title="struct syn::token::Pound"]::before {
	content: "Token![#]";
}

a.struct[title="struct syn::token::Priv"]::before {
	content: "Token![priv]";
}

a.struct[title="struct syn::token::Pub"]::before {
	content: "Token![pub]";
}

a.struct[title="struct syn::token::Question"]::before {
	content: "Token![?]";
}

a.struct[title="struct syn::token::RArrow"]::before {
	content: "Token![->]";
}

a.struct[title="struct syn::token::Raw"]::before {
	content: "Token![raw]";
}

a.struct[title="struct syn::token::Ref"]::before {
	content: "Token![ref]";
}

a.struct[title="struct syn::token::Return"]::before {
	content: "Token![return]";
}

a.struct[title="struct syn::token::SelfType"]::before {
	content: "Token![Self]";
}

a.struct[title="struct syn::token::SelfValue"]::before {
	content: "Token![self]";
}

a.struct[title="struct syn::token::Semi"]::before {
	content: "Token![;]";
}

a.struct[title="struct syn::token::Shl"]::before {
	content: "Token![<<]";
}

a.struct[title="struct syn::token::ShlEq"]::before {
	content: "Token![<<=]";
}

a.struct[title="struct syn::token::Shr"]::before {
	content: "Token![>>]";
}

a.struct[title="struct syn::token::ShrEq"]::before {
	content: "Token![>>=]";
}

a.struct[title="struct syn::token::Slash"]::before {
	content: "Token![/]";
}

a.struct[title="struct syn::token::SlashEq"]::before {
	content: "Token![/=]";
}

a.struct[title="struct syn::token::Star"]::before {
	content: "Token![*]";
}

a.struct[title="struct syn::token::StarEq"]::before {
	content: "Token![*=]";
}

a.struct[title="struct syn::token::Static"]::before {
	content: "Token![static]";
}

a.struct[title="struct syn::token::Struct"]::before {
	content: "Token![struct]";
}

a.struct[title="struct syn::token::Super"]::before {
	content: "Token![super]";
}

a.struct[title="struct syn::token::Tilde"]::before {
	content: "Token![~]";
}

a.struct[title="struct syn::token::Trait"]::before {
	content: "Token![trait]";
}

a.struct[title="struct syn::token::Try"]::before {
	content: "Token![try]";
}

a.struct[title="struct syn::token::Type"]::before {
	content: "Token![type]";
}

a.struct[title="struct syn::token::Typeof"]::before {
	content: "Token![typeof]";
}

a.struct[title="struct syn::token::Underscore"]::before {
	content: "Token![_]";
	font-size: calc(100% * 10 / 9);
}

a.struct[title="struct syn::token::Union"]::before {
	content: "Token![union]";
}

a.struct[title="struct syn::token::Unsafe"]::before {
	content: "Token![unsafe]";
}

a.struct[title="struct syn::token::Unsized"]::before {
	content: "Token![unsized]";
}

a.struct[title="struct syn::token::Use"]::before {
	content: "Token![use]";
}

a.struct[title="struct syn::token::Virtual"]::before {
	content: "Token![virtual]";
}

a.struct[title="struct syn::token::Where"]::before {
	content: "Token![where]";
}

a.struct[title="struct syn::token::While"]::before {
	content: "Token![while]";
}

a.struct[title="struct syn::token::Yield"]::before {
	content: "Token![yield]";
}

a.struct[title="struct syn::token::Underscore"] {
	font-size: calc(100% * 9 / 10);
}

a.struct[title="struct syn::token::PercentEq"]::after,
a.struct[title="struct syn::token::Question"]::after {
	content: ".";
}

a.struct[title="struct syn::token::DotDotDot"]::after,
a.struct[title="struct syn::token::FatArrow"]::after,
a.struct[title="struct syn::token::Percent"]::after {
	content: "..";
}

a.struct[title="struct syn::token::CaretEq"]::after,
a.struct[title="struct syn::token::Dollar"]::after,
a.struct[title="struct syn::token::DotDotEq"]::after,
a.struct[title="struct syn::token::MinusEq"]::after,
a.struct[title="struct syn::token::PathSep"]::after,
a.struct[title="struct syn::token::SelfValue"]::after,
a.struct[title="struct syn::token::SlashEq"]::after {
	content: "...";
}

a.struct[title="struct syn::token::AndAnd"]::after,
a.struct[title="struct syn::token::Caret"]::after,
a.struct[title="struct syn::token::Colon"]::after,
a.struct[title="struct syn::token::Comma"]::after,
a.struct[title="struct syn::token::DotDot"]::after,
a.struct[title="struct syn::token::LArrow"]::after,
a.struct[title="struct syn::token::Minus"]::after,
a.struct[title="struct syn::token::PlusEq"]::after,
a.struct[title="struct syn::token::Pound"]::after,
a.struct[title="struct syn::token::RArrow"]::after,
a.struct[title="struct syn::token::SelfType"]::after,
a.struct[title="struct syn::token::Slash"]::after,
a.struct[title="struct syn::token::StarEq"]::after,
a.struct[title="struct syn::token::Tilde"]::after {
	content: "....";
}

a.struct[title="struct syn::token::AndEq"]::after,
a.struct[title="struct syn::token::Plus"]::after,
a.struct[title="struct syn::token::Semi"]::after,
a.struct[title="struct syn::token::Star"]::after {
	content: ".....";
}

a.struct[title="struct syn::token::And"]::after,
a.struct[title="struct syn::token::Dot"]::after,
a.struct[title="struct syn::token::EqEq"]::after,
a.struct[title="struct syn::token::Not"]::after,
a.struct[title="struct syn::token::OrEq"]::after,
a.struct[title="struct syn::token::OrOr"]::after,
a.struct[title="struct syn::token::ShlEq"]::after,
a.struct[title="struct syn::token::ShrEq"]::after {
	content: "......";
}

a.struct[title="struct syn::token::At"]::after,
a.struct[title="struct syn::token::Eq"]::after,
a.struct[title="struct syn::token::Gt"]::after,
a.struct[title="struct syn::token::Lt"]::after,
a.struct[title="struct syn::token::Or"]::after,
a.struct[title="struct syn::token::Shl"]::after,
a.struct[title="struct syn::token::Shr"]::after {
	content: ".......";
}

a.struct[title="struct syn::token::Abstract"]::after,
a.struct[title="struct syn::token::As"]::after,
a.struct[title="struct syn::token::Async"]::after,
a.struct[title="struct syn::token::Auto"]::after,
a.struct[title="struct syn::token::Await"]::after,
a.struct[title="struct syn::token::Become"]::after,
a.struct[title="struct syn::token::Box"]::after,
a.struct[title="struct syn::token::Break"]::after,
a.struct[title="struct syn::token::Const"]::after,
a.struct[title="struct syn::token::Continue"]::after,
a.struct[title="struct syn::token::Crate"]::after,
a.struct[title="struct syn::token::Default"]::after,
a.struct[title="struct syn::token::Do"]::after,
a.struct[title="struct syn::token::Dyn"]::after,
a.struct[title="struct syn::token::Else"]::after,
a.struct[title="struct syn::token::Enum"]::after,
a.struct[title="struct syn::token::Extern"]::after,
a.struct[title="struct syn::token::Final"]::after,
a.struct[title="struct syn::token::Fn"]::after,
a.struct[title="struct syn::token::For"]::after,
a.struct[title="struct syn::token::Ge"]::after,
a.struct[title="struct syn::token::If"]::after,
a.struct[title="struct syn::token::Impl"]::after,
a.struct[title="struct syn::token::In"]::after,
a.struct[title="struct syn::token::Le"]::after,
a.struct[title="struct syn::token::Let"]::after,
a.struct[title="struct syn::token::Loop"]::after,
a.struct[title="struct syn::token::Macro"]::after,
a.struct[title="struct syn::token::Match"]::after,
a.struct[title="struct syn::token::Mod"]::after,
a.struct[title="struct syn::token::Move"]::after,
a.struct[title="struct syn::token::Mut"]::after,
a.struct[title="struct syn::token::Ne"]::after,
a.struct[title="struct syn::token::Override"]::after,
a.struct[title="struct syn::token::Priv"]::after,
a.struct[title="struct syn::token::Pub"]::after,
a.struct[title="struct syn::token::Raw"]::after,
a.struct[title="struct syn::token::Ref"]::after,
a.struct[title="struct syn::token::Return"]::after,
a.struct[title="struct syn::token::Static"]::after,
a.struct[title="struct syn::token::Struct"]::after,
a.struct[title="struct syn::token::Super"]::after,
a.struct[title="struct syn::token::Trait"]::after,
a.struct[title="struct syn::token::Try"]::after,
a.struct[title="struct syn::token::Type"]::after,
a.struct[title="struct syn::token::Typeof"]::after,
a.struct[title="struct syn::token::Union"]::after,
a.struct[title="struct syn::token::Unsafe"]::after,
a.struct[title="struct syn::token::Unsized"]::after,
a.struct[title="struct syn::token::Use"]::after,
a.struct[title="struct syn::token::Virtual"]::after,
a.struct[title="struct syn::token::Where"]::after,
a.struct[title="struct syn::token::While"]::after,
a.struct[title="struct syn::token::Yield"]::after {
	content: "........";
}
