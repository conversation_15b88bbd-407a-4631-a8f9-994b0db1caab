// This file is @generated by syn-internal-codegen.
// It is not intended for manual editing.

#![allow(clippy::clone_on_copy, clippy::expl_impl_clone_on_copy)]
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Abi {
    fn clone(&self) -> Self {
        crate::Abi {
            extern_token: self.extern_token.clone(),
            name: self.name.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::AngleBracketedGenericArguments {
    fn clone(&self) -> Self {
        crate::AngleBracketedGenericArguments {
            colon2_token: self.colon2_token.clone(),
            lt_token: self.lt_token.clone(),
            args: self.args.clone(),
            gt_token: self.gt_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Arm {
    fn clone(&self) -> Self {
        crate::Arm {
            attrs: self.attrs.clone(),
            pat: self.pat.clone(),
            guard: self.guard.clone(),
            fat_arrow_token: self.fat_arrow_token.clone(),
            body: self.body.clone(),
            comma: self.comma.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::AssocConst {
    fn clone(&self) -> Self {
        crate::AssocConst {
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            eq_token: self.eq_token.clone(),
            value: self.value.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::AssocType {
    fn clone(&self) -> Self {
        crate::AssocType {
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            eq_token: self.eq_token.clone(),
            ty: self.ty.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Copy for crate::AttrStyle {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::AttrStyle {
    fn clone(&self) -> Self {
        *self
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Attribute {
    fn clone(&self) -> Self {
        crate::Attribute {
            pound_token: self.pound_token.clone(),
            style: self.style.clone(),
            bracket_token: self.bracket_token.clone(),
            meta: self.meta.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::BareFnArg {
    fn clone(&self) -> Self {
        crate::BareFnArg {
            attrs: self.attrs.clone(),
            name: self.name.clone(),
            ty: self.ty.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::BareVariadic {
    fn clone(&self) -> Self {
        crate::BareVariadic {
            attrs: self.attrs.clone(),
            name: self.name.clone(),
            dots: self.dots.clone(),
            comma: self.comma.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Copy for crate::BinOp {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::BinOp {
    fn clone(&self) -> Self {
        *self
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Block {
    fn clone(&self) -> Self {
        crate::Block {
            brace_token: self.brace_token.clone(),
            stmts: self.stmts.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::BoundLifetimes {
    fn clone(&self) -> Self {
        crate::BoundLifetimes {
            for_token: self.for_token.clone(),
            lt_token: self.lt_token.clone(),
            lifetimes: self.lifetimes.clone(),
            gt_token: self.gt_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::CapturedParam {
    fn clone(&self) -> Self {
        match self {
            crate::CapturedParam::Lifetime(v0) => {
                crate::CapturedParam::Lifetime(v0.clone())
            }
            crate::CapturedParam::Ident(v0) => crate::CapturedParam::Ident(v0.clone()),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ConstParam {
    fn clone(&self) -> Self {
        crate::ConstParam {
            attrs: self.attrs.clone(),
            const_token: self.const_token.clone(),
            ident: self.ident.clone(),
            colon_token: self.colon_token.clone(),
            ty: self.ty.clone(),
            eq_token: self.eq_token.clone(),
            default: self.default.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Constraint {
    fn clone(&self) -> Self {
        crate::Constraint {
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            colon_token: self.colon_token.clone(),
            bounds: self.bounds.clone(),
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Data {
    fn clone(&self) -> Self {
        match self {
            crate::Data::Struct(v0) => crate::Data::Struct(v0.clone()),
            crate::Data::Enum(v0) => crate::Data::Enum(v0.clone()),
            crate::Data::Union(v0) => crate::Data::Union(v0.clone()),
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::DataEnum {
    fn clone(&self) -> Self {
        crate::DataEnum {
            enum_token: self.enum_token.clone(),
            brace_token: self.brace_token.clone(),
            variants: self.variants.clone(),
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::DataStruct {
    fn clone(&self) -> Self {
        crate::DataStruct {
            struct_token: self.struct_token.clone(),
            fields: self.fields.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::DataUnion {
    fn clone(&self) -> Self {
        crate::DataUnion {
            union_token: self.union_token.clone(),
            fields: self.fields.clone(),
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::DeriveInput {
    fn clone(&self) -> Self {
        crate::DeriveInput {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            data: self.data.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Expr {
    fn clone(&self) -> Self {
        match self {
            #[cfg(feature = "full")]
            crate::Expr::Array(v0) => crate::Expr::Array(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Assign(v0) => crate::Expr::Assign(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Async(v0) => crate::Expr::Async(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Await(v0) => crate::Expr::Await(v0.clone()),
            crate::Expr::Binary(v0) => crate::Expr::Binary(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Block(v0) => crate::Expr::Block(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Break(v0) => crate::Expr::Break(v0.clone()),
            crate::Expr::Call(v0) => crate::Expr::Call(v0.clone()),
            crate::Expr::Cast(v0) => crate::Expr::Cast(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Closure(v0) => crate::Expr::Closure(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Const(v0) => crate::Expr::Const(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Continue(v0) => crate::Expr::Continue(v0.clone()),
            crate::Expr::Field(v0) => crate::Expr::Field(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::ForLoop(v0) => crate::Expr::ForLoop(v0.clone()),
            crate::Expr::Group(v0) => crate::Expr::Group(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::If(v0) => crate::Expr::If(v0.clone()),
            crate::Expr::Index(v0) => crate::Expr::Index(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Infer(v0) => crate::Expr::Infer(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Let(v0) => crate::Expr::Let(v0.clone()),
            crate::Expr::Lit(v0) => crate::Expr::Lit(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Loop(v0) => crate::Expr::Loop(v0.clone()),
            crate::Expr::Macro(v0) => crate::Expr::Macro(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Match(v0) => crate::Expr::Match(v0.clone()),
            crate::Expr::MethodCall(v0) => crate::Expr::MethodCall(v0.clone()),
            crate::Expr::Paren(v0) => crate::Expr::Paren(v0.clone()),
            crate::Expr::Path(v0) => crate::Expr::Path(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Range(v0) => crate::Expr::Range(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::RawAddr(v0) => crate::Expr::RawAddr(v0.clone()),
            crate::Expr::Reference(v0) => crate::Expr::Reference(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Repeat(v0) => crate::Expr::Repeat(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Return(v0) => crate::Expr::Return(v0.clone()),
            crate::Expr::Struct(v0) => crate::Expr::Struct(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Try(v0) => crate::Expr::Try(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::TryBlock(v0) => crate::Expr::TryBlock(v0.clone()),
            crate::Expr::Tuple(v0) => crate::Expr::Tuple(v0.clone()),
            crate::Expr::Unary(v0) => crate::Expr::Unary(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Unsafe(v0) => crate::Expr::Unsafe(v0.clone()),
            crate::Expr::Verbatim(v0) => crate::Expr::Verbatim(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::While(v0) => crate::Expr::While(v0.clone()),
            #[cfg(feature = "full")]
            crate::Expr::Yield(v0) => crate::Expr::Yield(v0.clone()),
            #[cfg(not(feature = "full"))]
            _ => unreachable!(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprArray {
    fn clone(&self) -> Self {
        crate::ExprArray {
            attrs: self.attrs.clone(),
            bracket_token: self.bracket_token.clone(),
            elems: self.elems.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprAssign {
    fn clone(&self) -> Self {
        crate::ExprAssign {
            attrs: self.attrs.clone(),
            left: self.left.clone(),
            eq_token: self.eq_token.clone(),
            right: self.right.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprAsync {
    fn clone(&self) -> Self {
        crate::ExprAsync {
            attrs: self.attrs.clone(),
            async_token: self.async_token.clone(),
            capture: self.capture.clone(),
            block: self.block.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprAwait {
    fn clone(&self) -> Self {
        crate::ExprAwait {
            attrs: self.attrs.clone(),
            base: self.base.clone(),
            dot_token: self.dot_token.clone(),
            await_token: self.await_token.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprBinary {
    fn clone(&self) -> Self {
        crate::ExprBinary {
            attrs: self.attrs.clone(),
            left: self.left.clone(),
            op: self.op.clone(),
            right: self.right.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprBlock {
    fn clone(&self) -> Self {
        crate::ExprBlock {
            attrs: self.attrs.clone(),
            label: self.label.clone(),
            block: self.block.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprBreak {
    fn clone(&self) -> Self {
        crate::ExprBreak {
            attrs: self.attrs.clone(),
            break_token: self.break_token.clone(),
            label: self.label.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprCall {
    fn clone(&self) -> Self {
        crate::ExprCall {
            attrs: self.attrs.clone(),
            func: self.func.clone(),
            paren_token: self.paren_token.clone(),
            args: self.args.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprCast {
    fn clone(&self) -> Self {
        crate::ExprCast {
            attrs: self.attrs.clone(),
            expr: self.expr.clone(),
            as_token: self.as_token.clone(),
            ty: self.ty.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprClosure {
    fn clone(&self) -> Self {
        crate::ExprClosure {
            attrs: self.attrs.clone(),
            lifetimes: self.lifetimes.clone(),
            constness: self.constness.clone(),
            movability: self.movability.clone(),
            asyncness: self.asyncness.clone(),
            capture: self.capture.clone(),
            or1_token: self.or1_token.clone(),
            inputs: self.inputs.clone(),
            or2_token: self.or2_token.clone(),
            output: self.output.clone(),
            body: self.body.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprConst {
    fn clone(&self) -> Self {
        crate::ExprConst {
            attrs: self.attrs.clone(),
            const_token: self.const_token.clone(),
            block: self.block.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprContinue {
    fn clone(&self) -> Self {
        crate::ExprContinue {
            attrs: self.attrs.clone(),
            continue_token: self.continue_token.clone(),
            label: self.label.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprField {
    fn clone(&self) -> Self {
        crate::ExprField {
            attrs: self.attrs.clone(),
            base: self.base.clone(),
            dot_token: self.dot_token.clone(),
            member: self.member.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprForLoop {
    fn clone(&self) -> Self {
        crate::ExprForLoop {
            attrs: self.attrs.clone(),
            label: self.label.clone(),
            for_token: self.for_token.clone(),
            pat: self.pat.clone(),
            in_token: self.in_token.clone(),
            expr: self.expr.clone(),
            body: self.body.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprGroup {
    fn clone(&self) -> Self {
        crate::ExprGroup {
            attrs: self.attrs.clone(),
            group_token: self.group_token.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprIf {
    fn clone(&self) -> Self {
        crate::ExprIf {
            attrs: self.attrs.clone(),
            if_token: self.if_token.clone(),
            cond: self.cond.clone(),
            then_branch: self.then_branch.clone(),
            else_branch: self.else_branch.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprIndex {
    fn clone(&self) -> Self {
        crate::ExprIndex {
            attrs: self.attrs.clone(),
            expr: self.expr.clone(),
            bracket_token: self.bracket_token.clone(),
            index: self.index.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprInfer {
    fn clone(&self) -> Self {
        crate::ExprInfer {
            attrs: self.attrs.clone(),
            underscore_token: self.underscore_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprLet {
    fn clone(&self) -> Self {
        crate::ExprLet {
            attrs: self.attrs.clone(),
            let_token: self.let_token.clone(),
            pat: self.pat.clone(),
            eq_token: self.eq_token.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprLit {
    fn clone(&self) -> Self {
        crate::ExprLit {
            attrs: self.attrs.clone(),
            lit: self.lit.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprLoop {
    fn clone(&self) -> Self {
        crate::ExprLoop {
            attrs: self.attrs.clone(),
            label: self.label.clone(),
            loop_token: self.loop_token.clone(),
            body: self.body.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprMacro {
    fn clone(&self) -> Self {
        crate::ExprMacro {
            attrs: self.attrs.clone(),
            mac: self.mac.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprMatch {
    fn clone(&self) -> Self {
        crate::ExprMatch {
            attrs: self.attrs.clone(),
            match_token: self.match_token.clone(),
            expr: self.expr.clone(),
            brace_token: self.brace_token.clone(),
            arms: self.arms.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprMethodCall {
    fn clone(&self) -> Self {
        crate::ExprMethodCall {
            attrs: self.attrs.clone(),
            receiver: self.receiver.clone(),
            dot_token: self.dot_token.clone(),
            method: self.method.clone(),
            turbofish: self.turbofish.clone(),
            paren_token: self.paren_token.clone(),
            args: self.args.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprParen {
    fn clone(&self) -> Self {
        crate::ExprParen {
            attrs: self.attrs.clone(),
            paren_token: self.paren_token.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprPath {
    fn clone(&self) -> Self {
        crate::ExprPath {
            attrs: self.attrs.clone(),
            qself: self.qself.clone(),
            path: self.path.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprRange {
    fn clone(&self) -> Self {
        crate::ExprRange {
            attrs: self.attrs.clone(),
            start: self.start.clone(),
            limits: self.limits.clone(),
            end: self.end.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprRawAddr {
    fn clone(&self) -> Self {
        crate::ExprRawAddr {
            attrs: self.attrs.clone(),
            and_token: self.and_token.clone(),
            raw: self.raw.clone(),
            mutability: self.mutability.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprReference {
    fn clone(&self) -> Self {
        crate::ExprReference {
            attrs: self.attrs.clone(),
            and_token: self.and_token.clone(),
            mutability: self.mutability.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprRepeat {
    fn clone(&self) -> Self {
        crate::ExprRepeat {
            attrs: self.attrs.clone(),
            bracket_token: self.bracket_token.clone(),
            expr: self.expr.clone(),
            semi_token: self.semi_token.clone(),
            len: self.len.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprReturn {
    fn clone(&self) -> Self {
        crate::ExprReturn {
            attrs: self.attrs.clone(),
            return_token: self.return_token.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprStruct {
    fn clone(&self) -> Self {
        crate::ExprStruct {
            attrs: self.attrs.clone(),
            qself: self.qself.clone(),
            path: self.path.clone(),
            brace_token: self.brace_token.clone(),
            fields: self.fields.clone(),
            dot2_token: self.dot2_token.clone(),
            rest: self.rest.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprTry {
    fn clone(&self) -> Self {
        crate::ExprTry {
            attrs: self.attrs.clone(),
            expr: self.expr.clone(),
            question_token: self.question_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprTryBlock {
    fn clone(&self) -> Self {
        crate::ExprTryBlock {
            attrs: self.attrs.clone(),
            try_token: self.try_token.clone(),
            block: self.block.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprTuple {
    fn clone(&self) -> Self {
        crate::ExprTuple {
            attrs: self.attrs.clone(),
            paren_token: self.paren_token.clone(),
            elems: self.elems.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprUnary {
    fn clone(&self) -> Self {
        crate::ExprUnary {
            attrs: self.attrs.clone(),
            op: self.op.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprUnsafe {
    fn clone(&self) -> Self {
        crate::ExprUnsafe {
            attrs: self.attrs.clone(),
            unsafe_token: self.unsafe_token.clone(),
            block: self.block.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprWhile {
    fn clone(&self) -> Self {
        crate::ExprWhile {
            attrs: self.attrs.clone(),
            label: self.label.clone(),
            while_token: self.while_token.clone(),
            cond: self.cond.clone(),
            body: self.body.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ExprYield {
    fn clone(&self) -> Self {
        crate::ExprYield {
            attrs: self.attrs.clone(),
            yield_token: self.yield_token.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Field {
    fn clone(&self) -> Self {
        crate::Field {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            mutability: self.mutability.clone(),
            ident: self.ident.clone(),
            colon_token: self.colon_token.clone(),
            ty: self.ty.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::FieldMutability {
    fn clone(&self) -> Self {
        match self {
            crate::FieldMutability::None => crate::FieldMutability::None,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::FieldPat {
    fn clone(&self) -> Self {
        crate::FieldPat {
            attrs: self.attrs.clone(),
            member: self.member.clone(),
            colon_token: self.colon_token.clone(),
            pat: self.pat.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::FieldValue {
    fn clone(&self) -> Self {
        crate::FieldValue {
            attrs: self.attrs.clone(),
            member: self.member.clone(),
            colon_token: self.colon_token.clone(),
            expr: self.expr.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Fields {
    fn clone(&self) -> Self {
        match self {
            crate::Fields::Named(v0) => crate::Fields::Named(v0.clone()),
            crate::Fields::Unnamed(v0) => crate::Fields::Unnamed(v0.clone()),
            crate::Fields::Unit => crate::Fields::Unit,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::FieldsNamed {
    fn clone(&self) -> Self {
        crate::FieldsNamed {
            brace_token: self.brace_token.clone(),
            named: self.named.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::FieldsUnnamed {
    fn clone(&self) -> Self {
        crate::FieldsUnnamed {
            paren_token: self.paren_token.clone(),
            unnamed: self.unnamed.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::File {
    fn clone(&self) -> Self {
        crate::File {
            shebang: self.shebang.clone(),
            attrs: self.attrs.clone(),
            items: self.items.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::FnArg {
    fn clone(&self) -> Self {
        match self {
            crate::FnArg::Receiver(v0) => crate::FnArg::Receiver(v0.clone()),
            crate::FnArg::Typed(v0) => crate::FnArg::Typed(v0.clone()),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ForeignItem {
    fn clone(&self) -> Self {
        match self {
            crate::ForeignItem::Fn(v0) => crate::ForeignItem::Fn(v0.clone()),
            crate::ForeignItem::Static(v0) => crate::ForeignItem::Static(v0.clone()),
            crate::ForeignItem::Type(v0) => crate::ForeignItem::Type(v0.clone()),
            crate::ForeignItem::Macro(v0) => crate::ForeignItem::Macro(v0.clone()),
            crate::ForeignItem::Verbatim(v0) => crate::ForeignItem::Verbatim(v0.clone()),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ForeignItemFn {
    fn clone(&self) -> Self {
        crate::ForeignItemFn {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            sig: self.sig.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ForeignItemMacro {
    fn clone(&self) -> Self {
        crate::ForeignItemMacro {
            attrs: self.attrs.clone(),
            mac: self.mac.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ForeignItemStatic {
    fn clone(&self) -> Self {
        crate::ForeignItemStatic {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            static_token: self.static_token.clone(),
            mutability: self.mutability.clone(),
            ident: self.ident.clone(),
            colon_token: self.colon_token.clone(),
            ty: self.ty.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ForeignItemType {
    fn clone(&self) -> Self {
        crate::ForeignItemType {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            type_token: self.type_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::GenericArgument {
    fn clone(&self) -> Self {
        match self {
            crate::GenericArgument::Lifetime(v0) => {
                crate::GenericArgument::Lifetime(v0.clone())
            }
            crate::GenericArgument::Type(v0) => crate::GenericArgument::Type(v0.clone()),
            crate::GenericArgument::Const(v0) => {
                crate::GenericArgument::Const(v0.clone())
            }
            crate::GenericArgument::AssocType(v0) => {
                crate::GenericArgument::AssocType(v0.clone())
            }
            crate::GenericArgument::AssocConst(v0) => {
                crate::GenericArgument::AssocConst(v0.clone())
            }
            crate::GenericArgument::Constraint(v0) => {
                crate::GenericArgument::Constraint(v0.clone())
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::GenericParam {
    fn clone(&self) -> Self {
        match self {
            crate::GenericParam::Lifetime(v0) => {
                crate::GenericParam::Lifetime(v0.clone())
            }
            crate::GenericParam::Type(v0) => crate::GenericParam::Type(v0.clone()),
            crate::GenericParam::Const(v0) => crate::GenericParam::Const(v0.clone()),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Generics {
    fn clone(&self) -> Self {
        crate::Generics {
            lt_token: self.lt_token.clone(),
            params: self.params.clone(),
            gt_token: self.gt_token.clone(),
            where_clause: self.where_clause.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ImplItem {
    fn clone(&self) -> Self {
        match self {
            crate::ImplItem::Const(v0) => crate::ImplItem::Const(v0.clone()),
            crate::ImplItem::Fn(v0) => crate::ImplItem::Fn(v0.clone()),
            crate::ImplItem::Type(v0) => crate::ImplItem::Type(v0.clone()),
            crate::ImplItem::Macro(v0) => crate::ImplItem::Macro(v0.clone()),
            crate::ImplItem::Verbatim(v0) => crate::ImplItem::Verbatim(v0.clone()),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ImplItemConst {
    fn clone(&self) -> Self {
        crate::ImplItemConst {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            defaultness: self.defaultness.clone(),
            const_token: self.const_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            colon_token: self.colon_token.clone(),
            ty: self.ty.clone(),
            eq_token: self.eq_token.clone(),
            expr: self.expr.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ImplItemFn {
    fn clone(&self) -> Self {
        crate::ImplItemFn {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            defaultness: self.defaultness.clone(),
            sig: self.sig.clone(),
            block: self.block.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ImplItemMacro {
    fn clone(&self) -> Self {
        crate::ImplItemMacro {
            attrs: self.attrs.clone(),
            mac: self.mac.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ImplItemType {
    fn clone(&self) -> Self {
        crate::ImplItemType {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            defaultness: self.defaultness.clone(),
            type_token: self.type_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            eq_token: self.eq_token.clone(),
            ty: self.ty.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ImplRestriction {
    fn clone(&self) -> Self {
        match *self {}
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Index {
    fn clone(&self) -> Self {
        crate::Index {
            index: self.index.clone(),
            span: self.span.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Item {
    fn clone(&self) -> Self {
        match self {
            crate::Item::Const(v0) => crate::Item::Const(v0.clone()),
            crate::Item::Enum(v0) => crate::Item::Enum(v0.clone()),
            crate::Item::ExternCrate(v0) => crate::Item::ExternCrate(v0.clone()),
            crate::Item::Fn(v0) => crate::Item::Fn(v0.clone()),
            crate::Item::ForeignMod(v0) => crate::Item::ForeignMod(v0.clone()),
            crate::Item::Impl(v0) => crate::Item::Impl(v0.clone()),
            crate::Item::Macro(v0) => crate::Item::Macro(v0.clone()),
            crate::Item::Mod(v0) => crate::Item::Mod(v0.clone()),
            crate::Item::Static(v0) => crate::Item::Static(v0.clone()),
            crate::Item::Struct(v0) => crate::Item::Struct(v0.clone()),
            crate::Item::Trait(v0) => crate::Item::Trait(v0.clone()),
            crate::Item::TraitAlias(v0) => crate::Item::TraitAlias(v0.clone()),
            crate::Item::Type(v0) => crate::Item::Type(v0.clone()),
            crate::Item::Union(v0) => crate::Item::Union(v0.clone()),
            crate::Item::Use(v0) => crate::Item::Use(v0.clone()),
            crate::Item::Verbatim(v0) => crate::Item::Verbatim(v0.clone()),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemConst {
    fn clone(&self) -> Self {
        crate::ItemConst {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            const_token: self.const_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            colon_token: self.colon_token.clone(),
            ty: self.ty.clone(),
            eq_token: self.eq_token.clone(),
            expr: self.expr.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemEnum {
    fn clone(&self) -> Self {
        crate::ItemEnum {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            enum_token: self.enum_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            brace_token: self.brace_token.clone(),
            variants: self.variants.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemExternCrate {
    fn clone(&self) -> Self {
        crate::ItemExternCrate {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            extern_token: self.extern_token.clone(),
            crate_token: self.crate_token.clone(),
            ident: self.ident.clone(),
            rename: self.rename.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemFn {
    fn clone(&self) -> Self {
        crate::ItemFn {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            sig: self.sig.clone(),
            block: self.block.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemForeignMod {
    fn clone(&self) -> Self {
        crate::ItemForeignMod {
            attrs: self.attrs.clone(),
            unsafety: self.unsafety.clone(),
            abi: self.abi.clone(),
            brace_token: self.brace_token.clone(),
            items: self.items.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemImpl {
    fn clone(&self) -> Self {
        crate::ItemImpl {
            attrs: self.attrs.clone(),
            defaultness: self.defaultness.clone(),
            unsafety: self.unsafety.clone(),
            impl_token: self.impl_token.clone(),
            generics: self.generics.clone(),
            trait_: self.trait_.clone(),
            self_ty: self.self_ty.clone(),
            brace_token: self.brace_token.clone(),
            items: self.items.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemMacro {
    fn clone(&self) -> Self {
        crate::ItemMacro {
            attrs: self.attrs.clone(),
            ident: self.ident.clone(),
            mac: self.mac.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemMod {
    fn clone(&self) -> Self {
        crate::ItemMod {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            unsafety: self.unsafety.clone(),
            mod_token: self.mod_token.clone(),
            ident: self.ident.clone(),
            content: self.content.clone(),
            semi: self.semi.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemStatic {
    fn clone(&self) -> Self {
        crate::ItemStatic {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            static_token: self.static_token.clone(),
            mutability: self.mutability.clone(),
            ident: self.ident.clone(),
            colon_token: self.colon_token.clone(),
            ty: self.ty.clone(),
            eq_token: self.eq_token.clone(),
            expr: self.expr.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemStruct {
    fn clone(&self) -> Self {
        crate::ItemStruct {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            struct_token: self.struct_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            fields: self.fields.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemTrait {
    fn clone(&self) -> Self {
        crate::ItemTrait {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            unsafety: self.unsafety.clone(),
            auto_token: self.auto_token.clone(),
            restriction: self.restriction.clone(),
            trait_token: self.trait_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            colon_token: self.colon_token.clone(),
            supertraits: self.supertraits.clone(),
            brace_token: self.brace_token.clone(),
            items: self.items.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemTraitAlias {
    fn clone(&self) -> Self {
        crate::ItemTraitAlias {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            trait_token: self.trait_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            eq_token: self.eq_token.clone(),
            bounds: self.bounds.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemType {
    fn clone(&self) -> Self {
        crate::ItemType {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            type_token: self.type_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            eq_token: self.eq_token.clone(),
            ty: self.ty.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemUnion {
    fn clone(&self) -> Self {
        crate::ItemUnion {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            union_token: self.union_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            fields: self.fields.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ItemUse {
    fn clone(&self) -> Self {
        crate::ItemUse {
            attrs: self.attrs.clone(),
            vis: self.vis.clone(),
            use_token: self.use_token.clone(),
            leading_colon: self.leading_colon.clone(),
            tree: self.tree.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Label {
    fn clone(&self) -> Self {
        crate::Label {
            name: self.name.clone(),
            colon_token: self.colon_token.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::LifetimeParam {
    fn clone(&self) -> Self {
        crate::LifetimeParam {
            attrs: self.attrs.clone(),
            lifetime: self.lifetime.clone(),
            colon_token: self.colon_token.clone(),
            bounds: self.bounds.clone(),
        }
    }
}
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Lit {
    fn clone(&self) -> Self {
        match self {
            crate::Lit::Str(v0) => crate::Lit::Str(v0.clone()),
            crate::Lit::ByteStr(v0) => crate::Lit::ByteStr(v0.clone()),
            crate::Lit::CStr(v0) => crate::Lit::CStr(v0.clone()),
            crate::Lit::Byte(v0) => crate::Lit::Byte(v0.clone()),
            crate::Lit::Char(v0) => crate::Lit::Char(v0.clone()),
            crate::Lit::Int(v0) => crate::Lit::Int(v0.clone()),
            crate::Lit::Float(v0) => crate::Lit::Float(v0.clone()),
            crate::Lit::Bool(v0) => crate::Lit::Bool(v0.clone()),
            crate::Lit::Verbatim(v0) => crate::Lit::Verbatim(v0.clone()),
        }
    }
}
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::LitBool {
    fn clone(&self) -> Self {
        crate::LitBool {
            value: self.value.clone(),
            span: self.span.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Local {
    fn clone(&self) -> Self {
        crate::Local {
            attrs: self.attrs.clone(),
            let_token: self.let_token.clone(),
            pat: self.pat.clone(),
            init: self.init.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::LocalInit {
    fn clone(&self) -> Self {
        crate::LocalInit {
            eq_token: self.eq_token.clone(),
            expr: self.expr.clone(),
            diverge: self.diverge.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Macro {
    fn clone(&self) -> Self {
        crate::Macro {
            path: self.path.clone(),
            bang_token: self.bang_token.clone(),
            delimiter: self.delimiter.clone(),
            tokens: self.tokens.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::MacroDelimiter {
    fn clone(&self) -> Self {
        match self {
            crate::MacroDelimiter::Paren(v0) => crate::MacroDelimiter::Paren(v0.clone()),
            crate::MacroDelimiter::Brace(v0) => crate::MacroDelimiter::Brace(v0.clone()),
            crate::MacroDelimiter::Bracket(v0) => {
                crate::MacroDelimiter::Bracket(v0.clone())
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Member {
    fn clone(&self) -> Self {
        match self {
            crate::Member::Named(v0) => crate::Member::Named(v0.clone()),
            crate::Member::Unnamed(v0) => crate::Member::Unnamed(v0.clone()),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Meta {
    fn clone(&self) -> Self {
        match self {
            crate::Meta::Path(v0) => crate::Meta::Path(v0.clone()),
            crate::Meta::List(v0) => crate::Meta::List(v0.clone()),
            crate::Meta::NameValue(v0) => crate::Meta::NameValue(v0.clone()),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::MetaList {
    fn clone(&self) -> Self {
        crate::MetaList {
            path: self.path.clone(),
            delimiter: self.delimiter.clone(),
            tokens: self.tokens.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::MetaNameValue {
    fn clone(&self) -> Self {
        crate::MetaNameValue {
            path: self.path.clone(),
            eq_token: self.eq_token.clone(),
            value: self.value.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ParenthesizedGenericArguments {
    fn clone(&self) -> Self {
        crate::ParenthesizedGenericArguments {
            paren_token: self.paren_token.clone(),
            inputs: self.inputs.clone(),
            output: self.output.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Pat {
    fn clone(&self) -> Self {
        match self {
            crate::Pat::Const(v0) => crate::Pat::Const(v0.clone()),
            crate::Pat::Ident(v0) => crate::Pat::Ident(v0.clone()),
            crate::Pat::Lit(v0) => crate::Pat::Lit(v0.clone()),
            crate::Pat::Macro(v0) => crate::Pat::Macro(v0.clone()),
            crate::Pat::Or(v0) => crate::Pat::Or(v0.clone()),
            crate::Pat::Paren(v0) => crate::Pat::Paren(v0.clone()),
            crate::Pat::Path(v0) => crate::Pat::Path(v0.clone()),
            crate::Pat::Range(v0) => crate::Pat::Range(v0.clone()),
            crate::Pat::Reference(v0) => crate::Pat::Reference(v0.clone()),
            crate::Pat::Rest(v0) => crate::Pat::Rest(v0.clone()),
            crate::Pat::Slice(v0) => crate::Pat::Slice(v0.clone()),
            crate::Pat::Struct(v0) => crate::Pat::Struct(v0.clone()),
            crate::Pat::Tuple(v0) => crate::Pat::Tuple(v0.clone()),
            crate::Pat::TupleStruct(v0) => crate::Pat::TupleStruct(v0.clone()),
            crate::Pat::Type(v0) => crate::Pat::Type(v0.clone()),
            crate::Pat::Verbatim(v0) => crate::Pat::Verbatim(v0.clone()),
            crate::Pat::Wild(v0) => crate::Pat::Wild(v0.clone()),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatIdent {
    fn clone(&self) -> Self {
        crate::PatIdent {
            attrs: self.attrs.clone(),
            by_ref: self.by_ref.clone(),
            mutability: self.mutability.clone(),
            ident: self.ident.clone(),
            subpat: self.subpat.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatOr {
    fn clone(&self) -> Self {
        crate::PatOr {
            attrs: self.attrs.clone(),
            leading_vert: self.leading_vert.clone(),
            cases: self.cases.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatParen {
    fn clone(&self) -> Self {
        crate::PatParen {
            attrs: self.attrs.clone(),
            paren_token: self.paren_token.clone(),
            pat: self.pat.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatReference {
    fn clone(&self) -> Self {
        crate::PatReference {
            attrs: self.attrs.clone(),
            and_token: self.and_token.clone(),
            mutability: self.mutability.clone(),
            pat: self.pat.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatRest {
    fn clone(&self) -> Self {
        crate::PatRest {
            attrs: self.attrs.clone(),
            dot2_token: self.dot2_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatSlice {
    fn clone(&self) -> Self {
        crate::PatSlice {
            attrs: self.attrs.clone(),
            bracket_token: self.bracket_token.clone(),
            elems: self.elems.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatStruct {
    fn clone(&self) -> Self {
        crate::PatStruct {
            attrs: self.attrs.clone(),
            qself: self.qself.clone(),
            path: self.path.clone(),
            brace_token: self.brace_token.clone(),
            fields: self.fields.clone(),
            rest: self.rest.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatTuple {
    fn clone(&self) -> Self {
        crate::PatTuple {
            attrs: self.attrs.clone(),
            paren_token: self.paren_token.clone(),
            elems: self.elems.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatTupleStruct {
    fn clone(&self) -> Self {
        crate::PatTupleStruct {
            attrs: self.attrs.clone(),
            qself: self.qself.clone(),
            path: self.path.clone(),
            paren_token: self.paren_token.clone(),
            elems: self.elems.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatType {
    fn clone(&self) -> Self {
        crate::PatType {
            attrs: self.attrs.clone(),
            pat: self.pat.clone(),
            colon_token: self.colon_token.clone(),
            ty: self.ty.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PatWild {
    fn clone(&self) -> Self {
        crate::PatWild {
            attrs: self.attrs.clone(),
            underscore_token: self.underscore_token.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Path {
    fn clone(&self) -> Self {
        crate::Path {
            leading_colon: self.leading_colon.clone(),
            segments: self.segments.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PathArguments {
    fn clone(&self) -> Self {
        match self {
            crate::PathArguments::None => crate::PathArguments::None,
            crate::PathArguments::AngleBracketed(v0) => {
                crate::PathArguments::AngleBracketed(v0.clone())
            }
            crate::PathArguments::Parenthesized(v0) => {
                crate::PathArguments::Parenthesized(v0.clone())
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PathSegment {
    fn clone(&self) -> Self {
        crate::PathSegment {
            ident: self.ident.clone(),
            arguments: self.arguments.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PointerMutability {
    fn clone(&self) -> Self {
        match self {
            crate::PointerMutability::Const(v0) => {
                crate::PointerMutability::Const(v0.clone())
            }
            crate::PointerMutability::Mut(v0) => {
                crate::PointerMutability::Mut(v0.clone())
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PreciseCapture {
    fn clone(&self) -> Self {
        crate::PreciseCapture {
            use_token: self.use_token.clone(),
            lt_token: self.lt_token.clone(),
            params: self.params.clone(),
            gt_token: self.gt_token.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PredicateLifetime {
    fn clone(&self) -> Self {
        crate::PredicateLifetime {
            lifetime: self.lifetime.clone(),
            colon_token: self.colon_token.clone(),
            bounds: self.bounds.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::PredicateType {
    fn clone(&self) -> Self {
        crate::PredicateType {
            lifetimes: self.lifetimes.clone(),
            bounded_ty: self.bounded_ty.clone(),
            colon_token: self.colon_token.clone(),
            bounds: self.bounds.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::QSelf {
    fn clone(&self) -> Self {
        crate::QSelf {
            lt_token: self.lt_token.clone(),
            ty: self.ty.clone(),
            position: self.position.clone(),
            as_token: self.as_token.clone(),
            gt_token: self.gt_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Copy for crate::RangeLimits {}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::RangeLimits {
    fn clone(&self) -> Self {
        *self
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Receiver {
    fn clone(&self) -> Self {
        crate::Receiver {
            attrs: self.attrs.clone(),
            reference: self.reference.clone(),
            mutability: self.mutability.clone(),
            self_token: self.self_token.clone(),
            colon_token: self.colon_token.clone(),
            ty: self.ty.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::ReturnType {
    fn clone(&self) -> Self {
        match self {
            crate::ReturnType::Default => crate::ReturnType::Default,
            crate::ReturnType::Type(v0, v1) => {
                crate::ReturnType::Type(v0.clone(), v1.clone())
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Signature {
    fn clone(&self) -> Self {
        crate::Signature {
            constness: self.constness.clone(),
            asyncness: self.asyncness.clone(),
            unsafety: self.unsafety.clone(),
            abi: self.abi.clone(),
            fn_token: self.fn_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            paren_token: self.paren_token.clone(),
            inputs: self.inputs.clone(),
            variadic: self.variadic.clone(),
            output: self.output.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::StaticMutability {
    fn clone(&self) -> Self {
        match self {
            crate::StaticMutability::Mut(v0) => crate::StaticMutability::Mut(v0.clone()),
            crate::StaticMutability::None => crate::StaticMutability::None,
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Stmt {
    fn clone(&self) -> Self {
        match self {
            crate::Stmt::Local(v0) => crate::Stmt::Local(v0.clone()),
            crate::Stmt::Item(v0) => crate::Stmt::Item(v0.clone()),
            crate::Stmt::Expr(v0, v1) => crate::Stmt::Expr(v0.clone(), v1.clone()),
            crate::Stmt::Macro(v0) => crate::Stmt::Macro(v0.clone()),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::StmtMacro {
    fn clone(&self) -> Self {
        crate::StmtMacro {
            attrs: self.attrs.clone(),
            mac: self.mac.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TraitBound {
    fn clone(&self) -> Self {
        crate::TraitBound {
            paren_token: self.paren_token.clone(),
            modifier: self.modifier.clone(),
            lifetimes: self.lifetimes.clone(),
            path: self.path.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Copy for crate::TraitBoundModifier {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TraitBoundModifier {
    fn clone(&self) -> Self {
        *self
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TraitItem {
    fn clone(&self) -> Self {
        match self {
            crate::TraitItem::Const(v0) => crate::TraitItem::Const(v0.clone()),
            crate::TraitItem::Fn(v0) => crate::TraitItem::Fn(v0.clone()),
            crate::TraitItem::Type(v0) => crate::TraitItem::Type(v0.clone()),
            crate::TraitItem::Macro(v0) => crate::TraitItem::Macro(v0.clone()),
            crate::TraitItem::Verbatim(v0) => crate::TraitItem::Verbatim(v0.clone()),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TraitItemConst {
    fn clone(&self) -> Self {
        crate::TraitItemConst {
            attrs: self.attrs.clone(),
            const_token: self.const_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            colon_token: self.colon_token.clone(),
            ty: self.ty.clone(),
            default: self.default.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TraitItemFn {
    fn clone(&self) -> Self {
        crate::TraitItemFn {
            attrs: self.attrs.clone(),
            sig: self.sig.clone(),
            default: self.default.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TraitItemMacro {
    fn clone(&self) -> Self {
        crate::TraitItemMacro {
            attrs: self.attrs.clone(),
            mac: self.mac.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TraitItemType {
    fn clone(&self) -> Self {
        crate::TraitItemType {
            attrs: self.attrs.clone(),
            type_token: self.type_token.clone(),
            ident: self.ident.clone(),
            generics: self.generics.clone(),
            colon_token: self.colon_token.clone(),
            bounds: self.bounds.clone(),
            default: self.default.clone(),
            semi_token: self.semi_token.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Type {
    fn clone(&self) -> Self {
        match self {
            crate::Type::Array(v0) => crate::Type::Array(v0.clone()),
            crate::Type::BareFn(v0) => crate::Type::BareFn(v0.clone()),
            crate::Type::Group(v0) => crate::Type::Group(v0.clone()),
            crate::Type::ImplTrait(v0) => crate::Type::ImplTrait(v0.clone()),
            crate::Type::Infer(v0) => crate::Type::Infer(v0.clone()),
            crate::Type::Macro(v0) => crate::Type::Macro(v0.clone()),
            crate::Type::Never(v0) => crate::Type::Never(v0.clone()),
            crate::Type::Paren(v0) => crate::Type::Paren(v0.clone()),
            crate::Type::Path(v0) => crate::Type::Path(v0.clone()),
            crate::Type::Ptr(v0) => crate::Type::Ptr(v0.clone()),
            crate::Type::Reference(v0) => crate::Type::Reference(v0.clone()),
            crate::Type::Slice(v0) => crate::Type::Slice(v0.clone()),
            crate::Type::TraitObject(v0) => crate::Type::TraitObject(v0.clone()),
            crate::Type::Tuple(v0) => crate::Type::Tuple(v0.clone()),
            crate::Type::Verbatim(v0) => crate::Type::Verbatim(v0.clone()),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeArray {
    fn clone(&self) -> Self {
        crate::TypeArray {
            bracket_token: self.bracket_token.clone(),
            elem: self.elem.clone(),
            semi_token: self.semi_token.clone(),
            len: self.len.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeBareFn {
    fn clone(&self) -> Self {
        crate::TypeBareFn {
            lifetimes: self.lifetimes.clone(),
            unsafety: self.unsafety.clone(),
            abi: self.abi.clone(),
            fn_token: self.fn_token.clone(),
            paren_token: self.paren_token.clone(),
            inputs: self.inputs.clone(),
            variadic: self.variadic.clone(),
            output: self.output.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeGroup {
    fn clone(&self) -> Self {
        crate::TypeGroup {
            group_token: self.group_token.clone(),
            elem: self.elem.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeImplTrait {
    fn clone(&self) -> Self {
        crate::TypeImplTrait {
            impl_token: self.impl_token.clone(),
            bounds: self.bounds.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeInfer {
    fn clone(&self) -> Self {
        crate::TypeInfer {
            underscore_token: self.underscore_token.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeMacro {
    fn clone(&self) -> Self {
        crate::TypeMacro {
            mac: self.mac.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeNever {
    fn clone(&self) -> Self {
        crate::TypeNever {
            bang_token: self.bang_token.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeParam {
    fn clone(&self) -> Self {
        crate::TypeParam {
            attrs: self.attrs.clone(),
            ident: self.ident.clone(),
            colon_token: self.colon_token.clone(),
            bounds: self.bounds.clone(),
            eq_token: self.eq_token.clone(),
            default: self.default.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeParamBound {
    fn clone(&self) -> Self {
        match self {
            crate::TypeParamBound::Trait(v0) => crate::TypeParamBound::Trait(v0.clone()),
            crate::TypeParamBound::Lifetime(v0) => {
                crate::TypeParamBound::Lifetime(v0.clone())
            }
            #[cfg(feature = "full")]
            crate::TypeParamBound::PreciseCapture(v0) => {
                crate::TypeParamBound::PreciseCapture(v0.clone())
            }
            crate::TypeParamBound::Verbatim(v0) => {
                crate::TypeParamBound::Verbatim(v0.clone())
            }
            #[cfg(not(feature = "full"))]
            _ => unreachable!(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeParen {
    fn clone(&self) -> Self {
        crate::TypeParen {
            paren_token: self.paren_token.clone(),
            elem: self.elem.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypePath {
    fn clone(&self) -> Self {
        crate::TypePath {
            qself: self.qself.clone(),
            path: self.path.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypePtr {
    fn clone(&self) -> Self {
        crate::TypePtr {
            star_token: self.star_token.clone(),
            const_token: self.const_token.clone(),
            mutability: self.mutability.clone(),
            elem: self.elem.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeReference {
    fn clone(&self) -> Self {
        crate::TypeReference {
            and_token: self.and_token.clone(),
            lifetime: self.lifetime.clone(),
            mutability: self.mutability.clone(),
            elem: self.elem.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeSlice {
    fn clone(&self) -> Self {
        crate::TypeSlice {
            bracket_token: self.bracket_token.clone(),
            elem: self.elem.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeTraitObject {
    fn clone(&self) -> Self {
        crate::TypeTraitObject {
            dyn_token: self.dyn_token.clone(),
            bounds: self.bounds.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::TypeTuple {
    fn clone(&self) -> Self {
        crate::TypeTuple {
            paren_token: self.paren_token.clone(),
            elems: self.elems.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Copy for crate::UnOp {}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::UnOp {
    fn clone(&self) -> Self {
        *self
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::UseGlob {
    fn clone(&self) -> Self {
        crate::UseGlob {
            star_token: self.star_token.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::UseGroup {
    fn clone(&self) -> Self {
        crate::UseGroup {
            brace_token: self.brace_token.clone(),
            items: self.items.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::UseName {
    fn clone(&self) -> Self {
        crate::UseName {
            ident: self.ident.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::UsePath {
    fn clone(&self) -> Self {
        crate::UsePath {
            ident: self.ident.clone(),
            colon2_token: self.colon2_token.clone(),
            tree: self.tree.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::UseRename {
    fn clone(&self) -> Self {
        crate::UseRename {
            ident: self.ident.clone(),
            as_token: self.as_token.clone(),
            rename: self.rename.clone(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::UseTree {
    fn clone(&self) -> Self {
        match self {
            crate::UseTree::Path(v0) => crate::UseTree::Path(v0.clone()),
            crate::UseTree::Name(v0) => crate::UseTree::Name(v0.clone()),
            crate::UseTree::Rename(v0) => crate::UseTree::Rename(v0.clone()),
            crate::UseTree::Glob(v0) => crate::UseTree::Glob(v0.clone()),
            crate::UseTree::Group(v0) => crate::UseTree::Group(v0.clone()),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Variadic {
    fn clone(&self) -> Self {
        crate::Variadic {
            attrs: self.attrs.clone(),
            pat: self.pat.clone(),
            dots: self.dots.clone(),
            comma: self.comma.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Variant {
    fn clone(&self) -> Self {
        crate::Variant {
            attrs: self.attrs.clone(),
            ident: self.ident.clone(),
            fields: self.fields.clone(),
            discriminant: self.discriminant.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::VisRestricted {
    fn clone(&self) -> Self {
        crate::VisRestricted {
            pub_token: self.pub_token.clone(),
            paren_token: self.paren_token.clone(),
            in_token: self.in_token.clone(),
            path: self.path.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::Visibility {
    fn clone(&self) -> Self {
        match self {
            crate::Visibility::Public(v0) => crate::Visibility::Public(v0.clone()),
            crate::Visibility::Restricted(v0) => {
                crate::Visibility::Restricted(v0.clone())
            }
            crate::Visibility::Inherited => crate::Visibility::Inherited,
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::WhereClause {
    fn clone(&self) -> Self {
        crate::WhereClause {
            where_token: self.where_token.clone(),
            predicates: self.predicates.clone(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "clone-impls")))]
impl Clone for crate::WherePredicate {
    fn clone(&self) -> Self {
        match self {
            crate::WherePredicate::Lifetime(v0) => {
                crate::WherePredicate::Lifetime(v0.clone())
            }
            crate::WherePredicate::Type(v0) => crate::WherePredicate::Type(v0.clone()),
        }
    }
}
