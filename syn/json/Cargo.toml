[package]
name = "syn-codegen"
version = "0.4.2" # also update html_root_url
authors = ["<PERSON> <<EMAIL>>"]
categories = ["development-tools::procedural-macro-helpers"]
description = "Syntax tree describing <PERSON><PERSON>'s syntax tree"
documentation = "https://docs.rs/syn-codegen"
edition = "2021"
keywords = ["syn"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/dtolnay/syn"

[features]
default = ["serde"]
serde = ["dep:serde", "dep:serde_derive", "indexmap/serde", "semver/serde"]

[dependencies]
indexmap = "2"
semver = "1"
serde = { version = "1.0.88", optional = true }
serde_derive = { version = "1.0.88", optional = true }

[dev-dependencies]
serde_json = "1"

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]
rustdoc-args = [
    "--generate-link-to-definition",
    "--extern-html-root-url=core=https://doc.rust-lang.org",
    "--extern-html-root-url=alloc=https://doc.rust-lang.org",
    "--extern-html-root-url=std=https://doc.rust-lang.org",
]

[workspace]
