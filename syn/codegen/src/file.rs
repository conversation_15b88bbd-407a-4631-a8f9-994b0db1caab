use crate::workspace_path;
use anyhow::Result;
use proc_macro2::TokenStream;
use std::fs;
use std::io::Write;
use std::path::Path;

pub fn write(relative_to_workspace_root: impl AsRef<Path>, content: TokenStream) -> Result<()> {
    let mut formatted = Vec::new();
    writeln!(
        formatted,
        "// This file is @generated by syn-internal-codegen."
    )?;
    writeln!(formatted, "// It is not intended for manual editing.")?;
    writeln!(formatted)?;

    let syntax_tree: syn::File = syn::parse2(content).unwrap();
    let pretty = prettyplease::unparse(&syntax_tree);
    write!(formatted, "{}", pretty)?;

    let path = workspace_path::get(relative_to_workspace_root);
    if path.is_file() && fs::read(&path)? == formatted {
        return Ok(());
    }

    fs::write(path, formatted)?;
    Ok(())
}
