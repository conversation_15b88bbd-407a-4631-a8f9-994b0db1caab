[package]
name = "syn-internal-codegen"
version = "0.0.0"
authors = ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"]
edition = "2021"

publish = false # this is an internal crate which should never be published

[features]
default = ["json"]
json = ["syn-codegen/serde"]

[dependencies]
anyhow = "1"
color-backtrace = "0.7"
indexmap = { version = "2", features = ["serde"] }
indoc = "2"
inflections = "1.1"
prettyplease = "0.2.3"
proc-macro2 = { version = "1.0.20", features = ["span-locations"] }
quote = "1"
semver = { version = "1", features = ["serde"] }
serde = "1.0.88"
serde_json = "1.0.38"
syn = { version = "2", features = ["derive", "full", "parsing", "printing"], default-features = false }
syn-codegen = { path = "../json", default-features = false }
toml = { version = "0.9", default-features = false, features = ["parse", "serde"] }

[workspace]
[patch.crates-io]
syn = { path = ".." }
