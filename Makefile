# MIT/Apache2 License

.POSIX:
.SUFFIXES: .o .c

PARSEOBJ = src/parser/lex.o src/parser/parser.o src/parser/token.o src/parser/expr.o
OBJS     = src/main.o src/utils/util.o $(PARSEOBJ)
TESTOBJ  = test_expr.o

SRC = $(OBJS:.o=.c)

CC     = cc
CFLAGS = -std=c99 -g -Wall -Wextra -Wpedantic

all: dozer

dozer: $(OBJS)
	$(CC) $(LDFLAGS) $(OBJS) -o $@

test_expr: $(TESTOBJ) $(PARSEOBJ) src/utils/util.o
	$(CC) $(LDFLAGS) $(TESTOBJ) $(PARSEOBJ) src/utils/util.o -o $@

test: test_expr
	./test_expr

.c.o:
	$(CC) $(CFLAGS) -c $< -o $@

$(PARSEOBJ): src/parser/internal.h src/parser/parser.h src/utils/util.h
src/main.o: src/parser/parser.h src/utils/util.h
test_expr.o: src/parser/internal.h src/parser/parser.h

clean:
	rm -f dozer test_expr $(OBJS) $(TESTOBJ)

format:
	clang-format -i $(SRC) src/**/*.h
