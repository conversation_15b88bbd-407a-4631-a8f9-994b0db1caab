# MIT/Apache2 License

.POSIX:
.SUFFIXES: .o .c

PARSEOBJ = src/parser/lex.o src/parser/parser.o src/parser/token.o
OBJS     = src/main.o src/utils/util.o $(PARSEOBJ)

SRC = $(OBJS:.o=.c)

CC     = cc
CFLAGS = -std=c99 -g -Wall -Wextra -Wpedantic

all: dozer
dozer: $(OBJS)
	$(CC) $(LDFLAGS) $(OBJS) -o $@

.c.o:
	$(CC) $(CFLAGS) -c $< -o $@

$(PARSEOBJ): src/parser/internal.h src/parser/parser.h src/utils/util.h
src/main.o: src/parser/parser.h src/utils/util.h

clean:
	rm -f dozer $(OBJS)

format:
	clang-format -i $(SRC) src/**/*.h
