// MIT/Apache2 License

#include "util.h"

#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define ARRAY_MAGIC 0xFA57C0DE

static const char *programname = "dozer";

struct U_Arena {
  void **ptr;
  size_t cursor, len;
};

typedef struct Array {
  uint32_t magic;
  U_Arena *arena;
  size_t elem_size, len, cap;
  union {
    long long ll;
    long double ld;
    void *ptr;
  } align[];
} Array;

void U_setname(const char *name) { programname = name; }

void U_die(const char *fmt, ...) {
  va_list ap;

  fprintf(stderr, "%s: ", programname);
  va_start(ap, fmt);
  vfprintf(stderr, fmt, ap);
  va_end(ap);

  // Print errno as well.
  if (fmt[0] && fmt[strlen(fmt) - 1] == ':') {
    putc(' ', stderr);
    perror(NULL);
  } else
    putc('\n', stderr);

  exit(1);
}

void *U_xmalloc(size_t sz) {
  void *result;

  result = malloc(sz);
  if (!result)
    U_die("out of memory");
  return result;
}

void U_xfree(void *ptr) {
  if (ptr)
    free(ptr);
}

U_Arena *U_mkarena(size_t sz) {
  U_Arena *arena;

  arena = U_xmalloc(sizeof(U_Arena));
  arena->ptr = U_xmalloc(sz * sizeof(void *));
  arena->ptr[0] = NULL;
  arena->cursor = 1;
  arena->len = sz;

  return arena;
}

void U_freearena(U_Arena *arena) {
  void **pool;

  while (1) {
    // Free everything in the pool.
    for (pool = &arena->ptr[1]; pool < &arena->ptr[arena->cursor]; pool++)
      U_xfree(*pool);

    // Move onto the next pool.
    pool = arena->ptr[0];
    U_xfree(arena->ptr);
    if (!pool)
      break;

    arena->ptr = pool;
    arena->cursor = arena->len;
  }

  U_xfree(arena);
}

void *U_alloc(U_Arena *arena, size_t sz) {
  void **pool;

  if (!arena)
    return U_xmalloc(sz);
  if (sz == 0)
    return NULL;

  if (arena->cursor >= arena->len) {
    pool = U_xmalloc(arena->len * sizeof(void *));
    pool[0] = arena->ptr;
    arena->ptr = pool;
    arena->cursor = 1;
  }

  return arena->ptr[arena->cursor++] = U_xmalloc(sz);
}

char *U_strdup(U_Arena *arena, const char *str) {
  char *buf;
  size_t len;

  len = strlen(str) + 1;
  buf = U_alloc(arena, len);
  memcpy(buf, str, len);
  return buf;
}

static size_t next_pow2(size_t i) {
  size_t x;

  for (x = 2; x < i; x *= 2)
    ;
  return x;
}

static void *mkarray(U_Arena *arena, size_t elem_size, size_t capacity,
                     size_t len) {
  Array *array;

  capacity = next_pow2(capacity);
  array = U_alloc(arena, (elem_size * capacity) + sizeof(Array));
  array->magic = ARRAY_MAGIC;
  array->arena = arena;
  array->elem_size = elem_size;
  array->len = len;
  array->cap = capacity;

  return array + 1;
}

void *U_mkarray(U_Arena *arena, size_t elem_size, size_t capacity) {
  return mkarray(arena, elem_size, capacity, 0);
}

size_t U_len(void *array) {
  Array *arr;

  arr = (Array *)array - 1;
  if (arr->magic != ARRAY_MAGIC)
    U_die("invalid array magic");

  return arr->len;
}

void U_freearray(void *array) {
  Array *arr;

  arr = (Array *)array - 1;
  if (arr->magic != ARRAY_MAGIC)
    U_die("invalid array magic");

  arr->magic = 0;

  if (arr->arena)
    return;
  U_xfree(arr);
}

void *U_expand(void *array, size_t new_len) {
  Array *arr;
  void *new_arr;
  size_t original_len;

  arr = *(Array **)array - 1;
  if (arr->magic != ARRAY_MAGIC)
    U_die("invalid array magic");

  // Expand the pool.
  original_len = arr->len;
  arr->len += new_len;
  if (arr->cap < arr->len) {
    // Expand array and copy over the previous elements.
    new_arr = U_mkarray(arr->arena, arr->elem_size, next_pow2(arr->len));
    memcpy(new_arr, arr + 1, arr->elem_size * original_len);
    U_freearray(arr + 1);
    *(Array **)array = new_arr;
    arr = new_arr;
  }

  return (uint8_t *)(arr + 1) + (original_len * arr->elem_size);
}

static size_t elemsize(void *array) {
  Array *arr;

  arr = (Array *)array - 1;
  if (arr->magic != ARRAY_MAGIC)
    U_die("invalid array magic");

  return arr->elem_size;
}

void *U_addmem(void *array, const void *data, size_t len) {
  void *mem;

  mem = U_expand(array, len);
  memcpy(mem, data, len * elemsize(array));
  return mem;
}

void U_shrink(void *array, size_t subtracted) {
  Array *arr;

  arr = (Array *)array - 1;
  if (arr->magic != ARRAY_MAGIC)
    U_die("invalid array magic");

  arr->len -= subtracted;
}

void U_clear(void *array) {
  Array *arr;

  arr = (Array *)array - 1;
  if (arr->magic != ARRAY_MAGIC)
    U_die("invalid array magic");

  arr->len = 0;
}
