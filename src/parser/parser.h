// MIT/Apache2 License

#ifndef DOZER_PARSER_H
#define DOZER_PARSER_H

#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>

// Location of a token.
typedef struct T_Loc {
  const char *source_file;
  size_t line, column;
} T_Loc;

// Forward declarations for recursive types
struct E_Expr;

// Binary operators
typedef enum {
  BOP_ADD,        // +
  BOP_SUB,        // -
  BOP_MUL,        // *
  BOP_DIV,        // /
  BOP_REM,        // %
  BOP_AND,        // &&
  BOP_OR,         // ||
  BOP_BITXOR,     // ^
  BOP_BITAND,     // &
  BOP_BITOR,      // |
  BOP_SHL,        // <<
  BOP_SHR,        // >>
  BOP_EQ,         // ==
  BOP_LT,         // <
  BOP_LE,         // <=
  BOP_NE,         // !=
  BOP_GE,         // >=
  BOP_GT,         // >
  BOP_ADD_ASSIGN, // +=
  BOP_SUB_ASSIGN, // -=
  BOP_MUL_ASSIGN, // *=
  BOP_DIV_ASSIGN, // /=
  BOP_REM_ASSIGN, // %=
  BOP_BITXOR_ASSIGN, // ^=
  BOP_BITAND_ASSIGN, // &=
  BOP_BITOR_ASSIGN,  // |=
  BOP_SHL_ASSIGN,    // <<=
  BOP_SHR_ASSIGN,    // >>=
} E_BinOp;

// Unary operators
typedef enum {
  UOP_DEREF,  // *
  UOP_NOT,    // !
  UOP_NEG,    // -
} E_UnOp;

// Literal types
typedef enum {
  LIT_STR,      // "string"
  LIT_BYTE_STR, // b"string"
  LIT_C_STR,    // c"string"
  LIT_CHAR,     // 'c'
  LIT_BYTE,     // b'c'
  LIT_INT,      // 123
  LIT_FLOAT,    // 1.23
  LIT_BOOL,     // true/false
} E_LitKind;

// Expression kinds
typedef enum {
  EK_LITERAL,     // 1, "foo", true
  EK_PATH,        // std::mem::replace, x
  EK_BINARY,      // a + b
  EK_UNARY,       // !x, *x, -x
  EK_ASSIGN,      // a = b
  EK_CALL,        // func(args)
  EK_METHOD_CALL, // obj.method(args)
  EK_FIELD,       // obj.field, obj.0
  EK_INDEX,       // arr[index]
  EK_PAREN,       // (expr)
  EK_REFERENCE,   // &expr, &mut expr
  EK_GROUP,       // invisible grouping for precedence
} E_ExprKind;

// Literal value
typedef struct E_Literal {
  E_LitKind kind;
  char *value;      // String representation
  char *suffix;     // Type suffix (e.g., "u32", "f64")
} E_Literal;

// Identifier
typedef struct E_Ident {
  char *name;
} E_Ident;

// Path segment
typedef struct E_PathSegment {
  E_Ident ident;
  // TODO: Add generic arguments support later
} E_PathSegment;

// Path (e.g., std::mem::replace)
typedef struct E_Path {
  E_PathSegment *segments;
  size_t segment_count;
  bool leading_colon;  // ::std::mem::replace vs std::mem::replace
} E_Path;

// Attribute (e.g., #[derive(Debug)])
typedef struct E_Attribute {
  // Simplified for now - just store the raw tokens
  char *content;
} E_Attribute;

// Expression list for function arguments, etc.
typedef struct E_ExprList {
  struct E_Expr **exprs;
  size_t count;
  size_t capacity;
} E_ExprList;

// Expression in Rust code
typedef struct E_Expr {
  E_ExprKind kind;
  E_Attribute *attrs;
  size_t attr_count;

  union {
    // EK_LITERAL
    E_Literal literal;

    // EK_PATH
    E_Path path;

    // EK_BINARY
    struct {
      struct E_Expr *left;
      E_BinOp op;
      struct E_Expr *right;
    } binary;

    // EK_UNARY
    struct {
      E_UnOp op;
      struct E_Expr *expr;
    } unary;

    // EK_ASSIGN
    struct {
      struct E_Expr *left;
      struct E_Expr *right;
    } assign;

    // EK_CALL
    struct {
      struct E_Expr *func;
      E_ExprList args;
    } call;

    // EK_METHOD_CALL
    struct {
      struct E_Expr *receiver;
      E_Ident method;
      E_ExprList args;
      // TODO: Add turbofish support later
    } method_call;

    // EK_FIELD
    struct {
      struct E_Expr *base;
      union {
        E_Ident named;    // .field
        size_t index;     // .0
      } member;
      bool is_named;
    } field;

    // EK_INDEX
    struct {
      struct E_Expr *expr;
      struct E_Expr *index;
    } index;

    // EK_PAREN, EK_GROUP
    struct {
      struct E_Expr *expr;
    } paren;

    // EK_REFERENCE
    struct {
      struct E_Expr *expr;
      bool is_mut;
    } reference;
  } data;
} E_Expr;

#endif
