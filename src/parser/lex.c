// MIT/Apache2 License

#include "../utils/util.h"
#include "internal.h"

#include <stdbool.h>
#include <stdio.h>
#include <string.h>

#define U_EOF (UINT32_MAX)
#define U_PEEK (UINT32_MAX - 1)

typedef enum NoEStart { ALLOW_E = 0, FORBID_E } NoEStart;

typedef enum AllowedEscapes {
  ASCII_ESCAPES = 0x01,
  BYTE_ESCAPES = 0x02,
  UNICODE_ESCAPES = 0x4,
  STRING_CONTINUE = 0x8,
  FORBID_ZEROES = 0x10
} AllowedEscapes;

typedef enum StrLitType { REGULAR_STR = 0, BYTE_STR, C_STR } StrLitType;

typedef enum CharLitType { CHAR_LITERAL = 0, BYTE_LITERAL } CharLitType;

static struct {
  const char *tag;
  StrLitType str_type;
  T_Kind token_kind;
  bool raw;
} stringtypes[] = {{"r", REGULAR_STR, TK_RAW_STRING_LITERAL, true},
                   {"b", BYTE_STR, TK_BYTE_STRING_LITERAL, false},
                   {"br", BYTE_STR, TK_RAW_BYTE_STRING_LITERAL, true},
                   {"c", C_STR, TK_C_STRING_LITERAL, false},
                   {"cr", C_STR, TK_RAW_C_STRING_LITERAL, true}};

// State of the lexer.
struct L_Lexer {
  FILE *file;
  T_Edition edition;

  // Current char.
  int by;
  uint32_t chr, peek;
  T_Loc loc;

  // Buffering for literals.
  uint8_t *buf;
  bool capture, trim_last_dot;
};

// Read the next byte from the lexer.
static void readbyte(L_Lexer *lexer) {
  if (lexer->capture)
    U_addmem(&lexer->buf, &lexer->by, 1);

  lexer->by = fgetc(lexer->file);
}

// Read the next UTF-8 char from the lexer.
static int readchar(L_Lexer *lexer) {
  int i, len;

  readbyte(lexer);
  if (lexer->by == EOF || lexer->by == 0)
    return -1;
  else if (lexer->by < 0x80) {
    // This is ASCII.
    lexer->chr = lexer->by;
    return 1;
  }

  // Read continuations.
  if ((lexer->by & 0xE0) == 0xC0) {
    lexer->chr = lexer->by & 0x1F;
    len = 1;
  } else if ((lexer->by & 0xF0) == 0xE0) {
    lexer->chr = lexer->by & 0x0F;
    len = 2;
  } else if ((lexer->by & 0xF8) == 0xF0) {
    lexer->chr = lexer->by & 0x07;
    len = 3;
  } else
    len = -1;

  // Read remaining bytes.
  for (i = 0; i < len; i++) {
    readbyte(lexer);
    if (lexer->by == EOF || ((lexer->by & 0xC0) != 0x80))
      return -1;

    lexer->chr = (lexer->chr << 6) | (lexer->by & 0x3F);
  }

  if (lexer->chr >= 0x110000 || lexer->chr - 0xD800 < 0x0200)
    return -1;

  return len;
}

// Read a char from the file.
static void next(L_Lexer *lexer) {
  int len;

  // Read out peeked char if needed.
  if (lexer->peek != U_PEEK) {
    lexer->chr = lexer->peek;
    lexer->peek = U_PEEK;
    return;
  }

  // Ignore all CRs, makes things less complicated.
  do {
    len = readchar(lexer);
    if (len == -1)
      lexer->chr = U_EOF;
    else if (lexer->chr == '\n') {
      lexer->loc.line++;
      lexer->loc.column = 0;
    } else
      lexer->loc.column += len;
  } while (lexer->chr == '\r');
}

// Peek a char from the file.
static void peek(L_Lexer *lexer) {
  uint32_t prev;

  if (lexer->peek != U_PEEK)
    return;

  prev = lexer->chr;
  next(lexer);
  lexer->peek = lexer->chr;
  lexer->chr = prev;
}

// Read past the end of the line.
static void eol(L_Lexer *lexer) {
  while (lexer->chr != '\n' && lexer->chr != U_EOF)
    next(lexer);
}

// Read a comment.
//
// Skips doc comments sicne we don't care about them.
static bool comment(L_Lexer *lexer) {
  uint32_t last, level;

  level = 1;

  switch (lexer->chr) {
  case '/':
    eol(lexer);
    break;
  case '*':
    // Nested comments.
    next(lexer);
    do {
      last = lexer->chr;
      next(lexer);

      if (lexer->chr == U_EOF)
        T_error(&lexer->loc, "unterminated comment");
      else if (last == '*' && lexer->chr == '/')
        level--;
      else if (last == '/' && lexer->chr == '*')
        level++;
    } while (level > 0);
    next(lexer);
    break;
  default:
    return false;
  }

  return true;
}

// Punctuation token that starts with a char, and maybe an equals sign.
static T_Kind maybe_eq(L_Lexer *lexer, T_Kind no_eq, T_Kind has_eq) {
  next(lexer);

  if (lexer->chr != '=')
    return no_eq;
  next(lexer);
  return has_eq;
}

// Punctuation token that starts with a char, and either the same char after
// or an quals sign.
static T_Kind maybe_eq_or_repeat(L_Lexer *lexer, T_Kind no_repeat,
                                 T_Kind has_eq, T_Kind has_repeat) {
  uint32_t ch;

  ch = lexer->chr;
  next(lexer);
  if (lexer->chr == ch) {
    next(lexer);
    return has_repeat;
  } else if (lexer->chr != '=')
    return no_repeat;

  next(lexer);
  return has_eq;
}

// Punctuation token that is one of:
//
// - Just a char
// - A repeated char
// - Then an equals sign
// - Both
static T_Kind maybe_eq_or_repeat_or_both(L_Lexer *lexer, T_Kind blank,
                                         T_Kind has_equals, T_Kind has_repeat,
                                         T_Kind has_both) {
  uint32_t ch;

  ch = lexer->chr;
  next(lexer);
  if (lexer->chr == '=') {
    next(lexer);
    return has_equals;
  } else if (lexer->chr != ch)
    return blank;

  return maybe_eq(lexer, has_repeat, has_both);
}

// Does this character start an identifier?
static bool is_xid_start(uint32_t chr) {
  // TODO: Full library.
  return ('A' <= chr && chr <= 'Z') || ('a' <= chr && chr <= 'z');
}

// Does this character continue an identifier?
static bool is_xid_continue(uint32_t chr) {
  // TODO: Full library.
  return is_xid_start(chr) || ('0' <= chr && chr <= '9') || chr == '_';
}

// Does a number match a base?
static bool number_matches_base(uint32_t chr, uint8_t base) {
  int ch;

  ch = chr;
  if (base > 10 && ('a' <= ch && ch < 'a' + base - 10))
    return true;
  if (base > 10 && ('A' <= ch && ch < 'A' + base - 10))
    return true;
  return '0' <= ch && ch < '0' + base;
}

// Read an identifier.
static void ident(L_Lexer *lexer) {
  lexer->capture = true;

  while (is_xid_continue(lexer->chr))
    next(lexer);
}

// Read a literal suffix.
static void suffix(L_Lexer *lexer, NoEStart start) {
  if (!is_xid_start(lexer->chr) ||
      (start == FORBID_E && (lexer->chr == 'e' || lexer->chr == 'E')))
    return;

  ident(lexer);
}

// Read the exponent for a float.
static bool float_exponent(L_Lexer *lexer) {
  T_Loc start;
  bool non_underscore;

  start = lexer->loc;
  non_underscore = false;

  if (lexer->chr == 'e' || lexer->chr == 'E') {
    next(lexer);

    // Look for suffix.
    if (lexer->chr == '+' || lexer->chr == '-')
      next(lexer);

    while (number_matches_base(lexer->chr, 10) || lexer->chr == '_') {
      non_underscore |= lexer->chr != '_';
      next(lexer);
    }

    if (!non_underscore)
      T_error(&start, "invalid numeric literal");

    return true;
  }

  return false;
}

// Read a number from the file.
static T_Kind number(L_Lexer *lexer) {
  T_Kind kind;
  NoEStart forbid_e;
  uint32_t base;
  bool non_underscore;
  T_Loc start;

  kind = TK_INTEGER;
  forbid_e = FORBID_E;
  base = 10;
  non_underscore = true;
  start = lexer->loc;

  lexer->capture = true;

  // Check for a different base.
  if (lexer->chr == '0') {
    next(lexer);
    switch (lexer->chr) {
    case 'x':
      base = 16;
      break;
    case 'o':
      base = 8;
      break;
    case 'b':
      base = 2;
      break;
    }
    if (base != 10) {
      next(lexer);
      non_underscore = false;
    }
  }

  // Read digits.
  while (number_matches_base(lexer->chr, base) || lexer->chr == '_') {
    non_underscore |= lexer->chr != '_';
    next(lexer);
  }

  if (!non_underscore)
    T_error(&start, "invalid numeric literal");

  // Check for E suffix.
  if (base == 10 && float_exponent(lexer)) {
    suffix(lexer, ALLOW_E);
    return TK_FLOAT;
  }

  // Handle floats.
  if (base == 10 && lexer->chr == '.') {
    // Ensure it's a valid float.
    peek(lexer);
    if (lexer->peek == '.' || lexer->peek == '_' || is_xid_start(lexer->peek)) {
      // This form is either a range (2..3) or someone calling a method on an
      // integer literal. Either way, trim the last dot and return the integer
      // literal.
      lexer->trim_last_dot = true;
      return TK_INTEGER;
    } else if (number_matches_base(lexer->peek, 10)) {
      // This is a float literal.
      next(lexer);
      kind = TK_FLOAT;

      // Read a decimal literal.
      while (number_matches_base(lexer->chr, 10) || lexer->chr == '_')
        next(lexer);

      // Read an exponent.
      if (float_exponent(lexer))
        forbid_e = ALLOW_E;
    }
  }

  suffix(lexer, forbid_e);
  return kind;
}

// Handle a unicode escape.
static void unicode_escape(L_Lexer *lexer, bool forbid_zero) {
  bool seen_non_zero;
  size_t i;

  seen_non_zero = false;
  i = 0;

  next(lexer);
  if (lexer->chr != '{')
    T_error(&lexer->loc, "expected '{', found '%c'", lexer->chr);
  next(lexer);

  if (!number_matches_base(lexer->chr, 16))
    T_error(&lexer->loc, "invalid \\u character escape");

  // Read interior characters.
  while (1) {
    if (lexer->chr == '}') {
      next(lexer);
      if (forbid_zero && !seen_non_zero)
        T_error(&lexer->loc, "forbidden \\u{0} sequence in C string");
      else if (i == 0)
        T_error(&lexer->loc, "empty character code in \\u{}");
      return;
    } else if (lexer->chr == '_')
      next(lexer);
    else if (number_matches_base(lexer->chr, 16)) {
      i++;
      if (i > 6)
        T_error(&lexer->loc, "invalid unicode escape, too many digits");
      if (lexer->chr != '0')
        seen_non_zero = true;
      next(lexer);
    } else
      T_error(&lexer->loc, "invalid unicode escape");
  }
}

// Handle an escape code.
static void escape(L_Lexer *lexer, AllowedEscapes escapes) {
  bool first_digit_was_zero;

  next(lexer);

  if ((escapes & ASCII_ESCAPES) && strchr("'\"nrt\\0", lexer->chr)) {
    if ((escapes & FORBID_ZEROES) && lexer->chr == '0')
      T_error(&lexer->loc, "forbidden '\\0' escape sequence in C string");
    next(lexer);
  } else if ((escapes & ASCII_ESCAPES) && lexer->chr == 'x') {
    next(lexer);

    // Evaluate first digit.
    if (lexer->chr == '0')
      first_digit_was_zero = true;
    if (((escapes & BYTE_ESCAPES) && number_matches_base(lexer->chr, 16)) ||
        number_matches_base(lexer->chr, 8))
      next(lexer);
    else
      T_error(&lexer->loc, "invalid numeric character escape");

    // Evaluate second digit.
    if (lexer->chr == '0' && first_digit_was_zero && (escapes & BYTE_ESCAPES))
      T_error(&lexer->loc, "forbidden zero after `\\x`");
    if (!number_matches_base(lexer->chr, 16))
      T_error(&lexer->loc, "invalid numeric character escape");

    next(lexer);
  } else if ((escapes & UNICODE_ESCAPES) && lexer->chr == 'u')
    unicode_escape(lexer, (escapes & FORBID_ZEROES) != 0);
  else
    T_error(&lexer->loc, "unknown character escape: '\\%c'", lexer->chr);
}

// Read a character or lifetime.
static T_Kind char_literal_or_lifetime(L_Lexer *lexer, CharLitType type) {
  bool maybe_lifetime;
  AllowedEscapes escapes;

  escapes = type == CHAR_LITERAL ? (ASCII_ESCAPES | UNICODE_ESCAPES)
                                 : (ASCII_ESCAPES | BYTE_ESCAPES);

  lexer->capture = true;
  next(lexer);

  // Read one char.
  switch (lexer->chr) {
  case '\\':
    escape(lexer, escapes);
    maybe_lifetime = false;
    break;
  case '\'':
    T_error(&lexer->loc, "empty character literal");
    break;
  case '\n':
    T_error(&lexer->loc, "newline in character literal");
    break;
  case U_EOF:
    T_error(&lexer->loc, "unexpected EOF in character literal");
    break;
  default:
    if (type == BYTE_LITERAL && lexer->chr > 127)
      T_error(&lexer->loc, "invalid byte literal");
    next(lexer);
    maybe_lifetime = type != BYTE_LITERAL && is_xid_start(lexer->chr);
    break;
  }

  // See if this is the end of the literal.
  if (lexer->chr != '\'') {
    if (maybe_lifetime) {
      ident(lexer);
      return TK_LIFETIME;
    } else
      T_error(&lexer->loc, "expected `\'`, found `%c`", lexer->chr);
  }

  next(lexer);
  suffix(lexer, ALLOW_E);
  return type == CHAR_LITERAL ? TK_CHAR_LITERAL : TK_BYTE_LITERAL;
}

// Read a string literal.
static void string(L_Lexer *lexer, StrLitType type) {
  AllowedEscapes escapes;
  bool was_escape;

  was_escape = false;
  lexer->capture = true;

  // Assign escapes.
  escapes = ASCII_ESCAPES | STRING_CONTINUE;
  switch (type) {
  case C_STR:
    escapes |= UNICODE_ESCAPES | FORBID_ZEROES;
    // fallthrough
  case BYTE_STR:
    escapes |= BYTE_ESCAPES;
    break;
  case REGULAR_STR:
    escapes |= UNICODE_ESCAPES;
    break;
  }

  // Read to the end of the string.
  do {
    if (!was_escape)
      next(lexer);
    else
      was_escape = false;

    // Read next char.
    if (lexer->chr == '\\') {
      escape(lexer, escapes);
      was_escape = true;
    } else if (type == C_STR && lexer->chr == '\0')
      T_error(&lexer->loc, "unexpected null character in C string");
    else if (type == BYTE_STR && lexer->chr > 127)
      T_error(&lexer->loc, "invalid byte in byte string");
    else if (lexer->chr == U_EOF)
      T_error(&lexer->loc, "unexpected EOF in string literal");
  } while (lexer->chr != '"');

  next(lexer);
  suffix(lexer, ALLOW_E);
}

// Read a raw string.
static void raw_string(L_Lexer *lexer, size_t hashtag_count, StrLitType type) {
  size_t i;
  bool just_read_hashtag;

  lexer->capture = true;
  just_read_hashtag = false;

  while (1) {
    if (!just_read_hashtag)
      next(lexer);
    else
      just_read_hashtag = false;

    if (type == C_STR && lexer->chr == '\0')
      T_error(&lexer->loc, "unexpected null character in C string");
    else if (type == BYTE_STR && lexer->chr > 127)
      T_error(&lexer->loc, "invalid byte in byte string");
    else if (lexer->chr == '"') {
      for (i = 0; i < hashtag_count; i++) {
        next(lexer);
        if (lexer->chr != '#')
          break;
      }

      if (hashtag_count <= i) {
        next(lexer);
        return;
      }

      just_read_hashtag = true;
    } else if (lexer->chr == U_EOF)
      T_error(&lexer->loc, "unexpected EOF in raw string literal");
  }
}

// Tell if we've captured anything so far.
static bool captured(const L_Lexer *lexer, const char *str,
                     size_t hashtag_count) {
  size_t len, i;
  uint8_t *buf;

  len = strlen(str);
  buf = lexer->buf;
  if (len + hashtag_count != U_len(buf))
    return false;

  if (memcmp(str, buf, len) != 0)
    return false;

  for (i = len; i < len + hashtag_count; i++)
    if (buf[i] != '#')
      return false;

  return true;
}

// Copy the buffer out.
static char *copy_buffer(uint8_t **buffer) {
  char *copied;
  uint8_t by;

  by = 0;
  U_addmem(buffer, &by, 1);
  copied = U_strdup(NULL, (char *)*buffer);
  U_clear(buffer);

  return copied;
}

// Read a token kind from the file.
static T_Kind lexkind(L_Lexer *lexer) {
  T_Kind kind;
  size_t hashtag_count, i;

  while (1) {
    switch (lexer->chr) {
    case U_EOF:
      return TK_EOF;
    case ' ':
    case '\t':
    case '\n':
    case 0x000B:
    case 0x000C:
    case 0x000D:
    case 0x0085:
    case 0x200E:
    case 0x200F:
    case 0x2028:
    case 0x2029:
      // Whitespace.
      next(lexer);
      break;
    case '+':
      return maybe_eq(lexer, TK_PLUS, TK_PLUSEQ);
    case '-':
      kind = maybe_eq(lexer, TK_MINUS, TK_MINUSEQ);

      if (kind == TK_MINUS && lexer->chr == '>') {
        next(lexer);
        return TK_RARROW;
      }
      return kind;
    case '*':
      return maybe_eq(lexer, TK_STAR, TK_STAREQ);
    case '/':
      kind = maybe_eq(lexer, TK_SLASH, TK_SLASHEQ);

      if (kind == TK_SLASH && comment(lexer))
        // Eat the comment.
        continue;

      return kind;
    case '%':
      return maybe_eq(lexer, TK_PERCENT, TK_PERCENTEQ);
    case '^':
      return maybe_eq(lexer, TK_CARET, TK_CARETEQ);
    case '!':
      return maybe_eq(lexer, TK_NOT, TK_NEQ);
    case '=':
      kind = maybe_eq(lexer, TK_EQ, TK_EQEQ);

      if (kind == TK_EQ && lexer->chr == '>') {
        next(lexer);
        return TK_FATARROW;
      }
      return kind;
    case '&':
      return maybe_eq_or_repeat(lexer, TK_AND, TK_ANDEQ, TK_ANDAND);
    case '|':
      return maybe_eq_or_repeat(lexer, TK_OR, TK_OREQ, TK_OROR);
    case '<':
      kind = maybe_eq_or_repeat_or_both(lexer, TK_LT, TK_LE, TK_SHL, TK_SHLEQ);

      if (kind == TK_LT && lexer->chr == '-') {
        next(lexer);
        return TK_LARROW;
      }
      return kind;
    case '>':
      return maybe_eq_or_repeat_or_both(lexer, TK_GT, TK_GE, TK_SHR, TK_SHREQ);
    case '.':
      next(lexer);
      if (lexer->chr != '.')
        return TK_DOT;

      return maybe_eq_or_repeat(lexer, TK_DOTDOT, TK_DOTDOTEQ, TK_DOTDOTDOT);
    case ',':
      next(lexer);
      return TK_COMMA;
    case ';':
      next(lexer);
      return TK_SEMI;
    case ':':
      next(lexer);
      if (lexer->chr != ':')
        return TK_COLON;

      next(lexer);
      return TK_PATHSEP;
    case '@':
      next(lexer);
      return TK_AT;
    case '#':
      next(lexer);
      return TK_POUND;
    case '$':
      next(lexer);
      return TK_DOLLAR;
    case '~':
      next(lexer);
      return TK_TILDE;
    case '{':
      next(lexer);
      return TK_LBRACE;
    case '}':
      next(lexer);
      return TK_RBRACE;
    case '[':
      next(lexer);
      return TK_LBRACKET;
    case ']':
      next(lexer);
      return TK_RBRACKET;
    case '(':
      next(lexer);
      return TK_LPAREN;
    case ')':
      next(lexer);
      return TK_RPAREN;
    case '\'':
      return char_literal_or_lifetime(lexer, CHAR_LITERAL);
    case '"':
      string(lexer, REGULAR_STR);
      return TK_STRING_LITERAL;
    default:
      // Could be a number.
      if (number_matches_base(lexer->chr, 10))
        return number(lexer);

      // Ensure it's not an identifier.
      if (!is_xid_start(lexer->chr) && lexer->chr != '_')
        T_error(&lexer->loc, "unexpected character '%c' (0x%x)", lexer->chr,
                lexer->chr);

      // Read the identifier.
      ident(lexer);

      // Read any hashtags if this is a raw literal.
      hashtag_count = 0;
      if (lexer->chr == '#' && lexer->buf[U_len(lexer->buf) - 1] == 'r') {
        do {
          hashtag_count += 1;
          next(lexer);
        } while (lexer->chr == '#');
      }

      // Look for a string or a char.
      if (lexer->chr == '\'') {
        if (captured(lexer, "b", 0))
          return char_literal_or_lifetime(lexer, BYTE_LITERAL);
        else
          T_error(&lexer->loc, "unknown char prefix: %s",
                  copy_buffer(&lexer->buf));
      } else if (lexer->chr == '\"') {
        // Check for various string types.
        for (i = 0; i < sizeof(stringtypes) / sizeof(stringtypes[i]); i++) {
          if (!captured(lexer, stringtypes[i].tag,
                        stringtypes[i].raw ? hashtag_count : 0))
            continue;

          if (stringtypes[i].raw)
            raw_string(lexer, hashtag_count, stringtypes[i].str_type);
          else
            string(lexer, stringtypes[i].str_type);
          return stringtypes[i].token_kind;
        }

        T_error(&lexer->loc, "unknown string prefix: %s",
                copy_buffer(&lexer->buf));
      } else if (captured(lexer, "r", 1) &&
                 (is_xid_start(lexer->chr) || lexer->chr == '_')) {
        // Raw identifier.
        ident(lexer);
      }

      return TK_IDENT;
    }
  }
}

// Create a new lexer.
L_Lexer *L_open(const char *sourcefile, T_Edition edition) {
  L_Lexer *lexer;

  lexer = U_xmalloc(sizeof(L_Lexer));
  lexer->file = fopen(sourcefile, "rb");
  if (!lexer->file)
    U_die("fopen %s:", sourcefile);
  lexer->edition = edition;

  lexer->by = 0;
  lexer->chr = 0;
  lexer->peek = U_PEEK;
  lexer->loc.source_file = sourcefile;
  lexer->loc.line = 1;
  lexer->loc.column = 0;

  lexer->buf = U_mkarray(NULL, 128, 1);
  lexer->capture = false;
  lexer->trim_last_dot = false;

  // Read the first char.
  next(lexer);

  // Read FEFF byte if needed.
  if (lexer->chr == 0xFEFF)
    next(lexer);

  // Read past the shebang.
  if (lexer->chr == '#') {
    peek(lexer);
    if (lexer->peek == '!')
      eol(lexer);
  }

  return lexer;
}

void L_close(L_Lexer *lexer) {
  fclose(lexer->file);
  U_freearray(lexer->buf);
  U_xfree(lexer);
}

void L_lex(L_Lexer *lexer, T_Token *token) {
  size_t len;

  token->loc = lexer->loc;
  token->kind = lexkind(lexer);

  // Read out the buffer.
  if (lexer->capture) {
    token->literal = copy_buffer(&lexer->buf);
    lexer->capture = false;

    // Trim the last dot if needed.
    if (lexer->trim_last_dot) {
      lexer->trim_last_dot = false;
      len = strlen(token->literal);
      if (len > 0 && token->literal[len - 1] == '.')
        token->literal[len - 1] = '\0';
    }
  } else
    token->literal = NULL;

  T_keyword(token, lexer->edition);
}
