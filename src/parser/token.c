// MIT/Apache2 License

//
// Token Management Operations
//

#include "internal.h"

#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

enum {
  NOK = 0,        // Not a keyword.
  STRICT = 0x01,  // Strict keyword.
  RESERVED = 0x2, // Reserved keyword.
  WEAK = 0x3,     // Weak keyword.
  E_2018 = 0x4,   // 2018 edition keyword.
  GROUP = 0x8     // Group token.
};

static struct {
  T_Kind kind;
  const char *name;
  const char *repr;
  uint8_t flags;
} tokens[] = {
#define X(name, str, flags) {TK_##name, #name, str, flags},
    TOKEN_KINDS(X)
#undef X
};

void T_error(const T_Loc *loc, const char *fmt, ...) {
  va_list ap;

  fprintf(stderr, "%s:%zu:%zu: error: ", loc->source_file, loc->line,
          loc->column);
  va_start(ap, fmt);
  vfprintf(stderr, fmt, ap);
  va_end(ap);

  putc('\n', stderr);
  exit(1);
}

void T_keyword(T_Token *token, T_Edition edition) {
  size_t i;

  if (token->kind != TK_IDENT)
    return;

  // TOOD: Binary search would be more efficient.
  for (i = 0; i < sizeof(tokens) / sizeof(tokens[0]); i++) {
    if ((tokens[i].flags & WEAK) == NOK)
      continue;
    if (edition == TE_2015 && (tokens[i].flags & E_2018))
      continue;
    if (strcmp(tokens[i].repr, token->literal) != 0)
      continue;

    token->kind = tokens[i].kind;
    return;
  }
}

const char *T_desc(T_Kind kind) {
  size_t i;

  for (i = 0; i < sizeof(tokens) / sizeof(tokens[0]); i++) {
    if (tokens[i].kind == kind) {
      return tokens[i].name;
    }
  }
  return "TK_NONE";
}

int T_repr(const T_Token *token, char *buf, size_t len) {
  size_t i;

  for (i = 0; i < sizeof(tokens) / sizeof(tokens[0]); i++) {
    if (tokens[i].kind != token->kind)
      continue;
    if (*tokens[i].repr == '\0')
      // Empty string.
      break;
    return snprintf(buf, len, "%s", tokens[i].repr);
  }
  return snprintf(buf, len, "%s", token->literal);
}
