// MIT/Apache2 License

#ifndef DOZER_PARSER_INTERNAL_H
#define DOZER_PARSER_INTERNAL_H

#include "parser.h"

#include <stdbool.h>

//
// Tokens (token.c)
//

/* Token Kind */
// clang-format off
#define TOKEN_KINDS(X) \
  X(NONE,                    "", NOK) \
  X(EOF,                     "", NOK) \
  X(IDENT,                   "", NOK) \
  X(INTEGER,                 "", NOK) \
  X(FLOAT,                   "", NOK) \
  X(LIFETIME,                "", NOK) \
  X(CHAR_LITERAL,            "", NOK) \
  X(BYTE_LITERAL,            "", NOK) \
  X(STRING_LITERAL,          "", NOK) \
  X(RAW_STRING_LITERAL,      "", NOK) \
  X(BYTE_STRING_LITERAL,     "", NOK) \
  X(RAW_BYTE_STRING_LITERAL, "", NOK) \
  X(C_STRING_LITERAL,        "", NOK) \
  X(RAW_C_STRING_LITERAL,    "", NOK) \
  \
  X(PLUS,      "+",   NOK) \
  X(MINUS,     "-",   NOK) \
  X(STAR,      "*",   NOK) \
  X(SLASH,     "/",   NOK) \
  X(PERCENT,   "%",   NOK) \
  X(CARET,     "^",   NOK) \
  X(NOT,       "!",   NOK) \
  X(AND,       "&",   NOK) \
  X(OR,        "|",   NOK) \
  X(ANDAND,    "&&",  NOK) \
  X(OROR,      "||",  NOK) \
  X(SHL,       "<<",  NOK) \
  X(SHR,       ">>",  NOK) \
  X(PLUSEQ,    "+=",  NOK) \
  X(MINUSEQ,   "-=",  NOK) \
  X(STAREQ,    "*=",  NOK) \
  X(SLASHEQ,   "/=",  NOK) \
  X(PERCENTEQ, "%=",  NOK) \
  X(CARETEQ,   "^=",  NOK) \
  X(ANDEQ,     "&=",  NOK) \
  X(OREQ,      "|=",  NOK) \
  X(SHLEQ,     "<<=", NOK) \
  X(SHREQ,     ">>=", NOK) \
  \
  X(EQ,        "=",  NOK) \
  X(EQEQ,      "==", NOK) \
  X(NEQ,       "!=", NOK) \
  X(LT,        "<",  NOK) \
  X(LE,        "<=", NOK) \
  X(GT,        ">",  NOK) \
  X(GE,        ">=", NOK) \
  \
  X(AT,         "@",   NOK) \
  X(UNDERSCORE, "_",   NOK) \
  X(DOT,        ".",   NOK) \
  X(DOTDOT,     "..",  NOK) \
  X(DOTDOTEQ,   "..=", NOK) \
  X(DOTDOTDOT,  "...", NOK) \
  X(COMMA,      ",",   NOK) \
  X(SEMI,       ";",   NOK) \
  X(COLON,      ":",   NOK) \
  X(PATHSEP,    "::",  NOK) \
  X(RARROW,     "->",  NOK) \
  X(FATARROW,   "=>",  NOK) \
  X(LARROW,     "<-",  NOK) \
  X(POUND,      "#",   NOK) \
  X(DOLLAR,     "$",   NOK) \
  X(QUESTION,   "?",   NOK) \
  X(TILDE,      "~",   NOK) \
  \
  X(LBRACE,     "{",   NOK | GROUP) \
  X(RBRACE,     "}",   NOK | GROUP) \
  X(LBRACKET,   "[",   NOK | GROUP) \
  X(RBRACKET,   "]",   NOK | GROUP) \
  X(LPAREN,     "(",   NOK | GROUP) \
  X(RPAREN,     ")",   NOK | GROUP) \
  X(LNONE,      "",    NOK | GROUP) \
  X(RNONE,      "",    NOK | GROUP) \
  \
  X(AS,        "as",       STRICT) \
  X(BREAK,     "break",    STRICT) \
  X(CONST,     "const",    STRICT) \
  X(CONTINUE,  "continue", STRICT) \
  X(CRATE,     "crate",    STRICT) \
  X(ELSE,      "else",     STRICT) \
  X(ENUM,      "enum",     STRICT) \
  X(EXTERN,    "extern",   STRICT) \
  X(FALSE,     "false",    STRICT) \
  X(FN,        "fn",       STRICT) \
  X(FOR,       "for",      STRICT) \
  X(IF,        "if",       STRICT) \
  X(IMPL,      "impl",     STRICT) \
  X(IN,        "in",       STRICT) \
  X(LET,       "let",      STRICT) \
  X(LOOP,      "loop",     STRICT) \
  X(MATCH,     "match",    STRICT) \
  X(MOD,       "mod",      STRICT) \
  X(MUT,       "mut",      STRICT) \
  X(PUB,       "pub",      STRICT) \
  X(REF,       "ref",      STRICT) \
  X(RETURN,    "return",   STRICT) \
  X(SELFVALUE, "self",     STRICT) \
  X(SELFTYPE,  "Self",     STRICT) \
  X(STATIC,    "static",   STRICT) \
  X(STRUCT,    "struct",   STRICT) \
  X(SUPER,     "super",    STRICT) \
  X(TRAIT,     "trait",    STRICT) \
  X(TRUE,      "true",     STRICT) \
  X(TYPE,      "type",     STRICT) \
  X(UNSAFE,    "unsafe",   STRICT) \
  X(USE,       "use",      STRICT) \
  X(WHERE,     "where",    STRICT) \
  X(WHILE,     "while",    STRICT) \
  \
  X(ASYNC, "async", STRICT | E_2018) \
  X(AWAIT, "await", STRICT | E_2018) \
  X(DYN,   "dyn",   STRICT | E_2018) \
  \
  X(ABSTRACT, "abstract", RESERVED) \
  X(BECOME,   "become",   RESERVED) \
  X(BOX,      "box",      RESERVED) \
  X(DO,       "do",       RESERVED) \
  X(FINAL,    "final",    RESERVED) \
  X(MACRO,    "macro",    RESERVED) \
  X(OVVERIDE, "override", RESERVED) \
  X(PRIV,     "priv",     RESERVED) \
  X(TYPEOF,   "typeof",   RESERVED) \
  X(UNSIZED,  "unsized",  RESERVED) \
  X(VIRTUAL,  "virtual",  RESERVED) \
  X(YIELD,    "yield",    RESERVED) \
  \
  X(TRY, "try", RESERVED | E_2018) \
  \
  X(MACRO_RULES,     "macro_rules", WEAK) \
  X(UNION,           "union", WEAK) \
  X(STATIC_LIFETIME, "'static", WEAK)
// clang-format on

typedef enum {
#define X(name, str, flags) TK_##name,
  TOKEN_KINDS(X)
#undef X
} T_Kind;

// Token in the source file.
typedef struct T_Token {
  T_Kind kind;
  T_Loc loc;
  char *literal;
} T_Token;

// Current edition.
typedef enum {
  TE_2015,
  TE_2018,
  TE_2021,
} T_Edition;

// Indicate that an error has occurred.
void T_error(const T_Loc *loc, const char *fmt, ...);

// Convert a token into a keyword token, if need be.
void T_keyword(T_Token *token, T_Edition edition);

// Get a string identifier for the token kind.
const char *T_desc(T_Kind kind);

// Get a string representation of the token.
int T_repr(const T_Token *token, char *buf, size_t len);

//
// Lexer (lexer.c)
//

typedef struct L_Lexer L_Lexer;

// Open a new lexer.
L_Lexer *L_open(const char *sourcefile, T_Edition edition);

// Close a lexer.
void L_close(L_Lexer *lexer);

// Get the next token from a lexer.
void L_lex(L_Lexer *lexer, T_Token *token);

//
// Token Parsing (parser.c)
//

typedef struct P_Parser P_Parser;

// Create a new parser from a lexer.
P_Parser *P_new(L_Lexer *lexer);

// Get the current token.
const T_Token *P_cur(const P_Parser *parser);

// Peek at what the next token is.
const T_Token *P_peek(const P_Parser *parser);

// Peek at what the token after the next token is.
const T_Token *P_peek2(const P_Parser *parser);

// As above, so below, plus one.
const T_Token *P_peek3(const P_Parser *parser);

// Bump to the next token.
void P_next(P_Parser *parser);

// Try to consume a token.
bool P_consume(P_Parser *parser, T_Kind kind);

// Expect a token.
char *P_expect(P_Parser *parser, T_Kind kind);

// Set a parser error.
void P_error(P_Parser *parser, const char *fmt, ...);

// Bail with the current error.
void P_bail(P_Parser *parser);

// Clear the current error.
void P_clear(P_Parser *parser);

// Try to run a parsing function and bail out if it fails.
#define TRY(x) \
  if (!(x)) return false;

E_Expr *E_parse(P_Parser *parser);
void E_print(const E_Expr *expr, T_Token **tokens);

#endif
