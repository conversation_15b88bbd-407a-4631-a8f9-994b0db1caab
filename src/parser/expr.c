// MIT/Apache2 License

#include "internal.h"
#include <stdlib.h>
#include <string.h>
#include <assert.h>

// Simple strdup implementation for C99 compatibility
static char *my_strdup(const char *s) {
  if (!s) return NULL;
  size_t len = strlen(s) + 1;
  char *copy = malloc(len);
  if (copy) {
    memcpy(copy, s, len);
  }
  return copy;
}

//
// Precedence and operator utilities
//

E_Precedence E_binop_precedence(E_BinOp op) {
  switch (op) {
    case BOP_ADD:
    case BOP_SUB:
      return PREC_SUM;

    case BOP_MUL:
    case BOP_DIV:
    case BOP_REM:
      return PREC_PRODUCT;

    case BOP_AND:
      return PREC_AND;

    case BOP_OR:
      return PREC_OR;

    case BOP_BITXOR:
      return PREC_BITXOR;

    case BOP_BITAND:
      return PREC_BITAND;

    case BOP_BITOR:
      return PREC_BITOR;

    case BOP_SHL:
    case BOP_SHR:
      return PREC_SHIFT;

    case BOP_EQ:
    case BOP_LT:
    case BOP_LE:
    case BOP_NE:
    case BOP_GE:
    case BOP_GT:
      return PREC_COMPARE;

    case BOP_ADD_ASSIGN:
    case BOP_SUB_ASSIGN:
    case BOP_MUL_ASSIGN:
    case BOP_DIV_ASSIGN:
    case BOP_REM_ASSIGN:
    case BOP_BITXOR_ASSIGN:
    case BOP_BITAND_ASSIGN:
    case BOP_BITOR_ASSIGN:
    case BOP_SHL_ASSIGN:
    case BOP_SHR_ASSIGN:
      return PREC_ASSIGN;

    default:
      return PREC_MIN;
  }
}

E_BinOp E_parse_binop(P_Parser *parser) {
  const T_Token *token = P_cur(parser);

  switch (token->kind) {
    case TK_PLUSEQ:  return BOP_ADD_ASSIGN;
    case TK_MINUSEQ: return BOP_SUB_ASSIGN;
    case TK_STAREQ:  return BOP_MUL_ASSIGN;
    case TK_SLASHEQ: return BOP_DIV_ASSIGN;
    case TK_PERCENTEQ: return BOP_REM_ASSIGN;
    case TK_CARETEQ: return BOP_BITXOR_ASSIGN;
    case TK_ANDEQ:   return BOP_BITAND_ASSIGN;
    case TK_OREQ:    return BOP_BITOR_ASSIGN;
    case TK_SHLEQ:   return BOP_SHL_ASSIGN;
    case TK_SHREQ:   return BOP_SHR_ASSIGN;
    case TK_ANDAND:  return BOP_AND;
    case TK_OROR:    return BOP_OR;
    case TK_SHL:     return BOP_SHL;
    case TK_SHR:     return BOP_SHR;
    case TK_EQEQ:    return BOP_EQ;
    case TK_LE:      return BOP_LE;
    case TK_NEQ:     return BOP_NE;
    case TK_GE:      return BOP_GE;
    case TK_PLUS:    return BOP_ADD;
    case TK_MINUS:   return BOP_SUB;
    case TK_STAR:    return BOP_MUL;
    case TK_SLASH:   return BOP_DIV;
    case TK_PERCENT: return BOP_REM;
    case TK_CARET:   return BOP_BITXOR;
    case TK_AND:     return BOP_BITAND;
    case TK_OR:      return BOP_BITOR;
    case TK_LT:      return BOP_LT;
    case TK_GT:      return BOP_GT;
    default:
      P_error(parser, "expected binary operator");
      return BOP_ADD; // fallback
  }
}

E_Precedence E_peek_precedence(P_Parser *parser) {
  const T_Token *token = P_cur(parser);

  // Check for assignment (but not =>)
  if (token->kind == TK_EQ && P_peek(parser)->kind != TK_GT) {
    return PREC_ASSIGN;
  }

  // Check for binary operators
  switch (token->kind) {
    case TK_PLUSEQ:
    case TK_MINUSEQ:
    case TK_STAREQ:
    case TK_SLASHEQ:
    case TK_PERCENTEQ:
    case TK_CARETEQ:
    case TK_ANDEQ:
    case TK_OREQ:
    case TK_SHLEQ:
    case TK_SHREQ:
      return PREC_ASSIGN;

    case TK_OROR:
      return PREC_OR;

    case TK_ANDAND:
      return PREC_AND;

    case TK_EQEQ:
    case TK_NEQ:
    case TK_LT:
    case TK_LE:
    case TK_GT:
    case TK_GE:
      return PREC_COMPARE;

    case TK_OR:
      return PREC_BITOR;

    case TK_CARET:
      return PREC_BITXOR;

    case TK_AND:
      return PREC_BITAND;

    case TK_SHL:
    case TK_SHR:
      return PREC_SHIFT;

    case TK_PLUS:
    case TK_MINUS:
      return PREC_SUM;

    case TK_STAR:
    case TK_SLASH:
    case TK_PERCENT:
      return PREC_PRODUCT;

    case TK_AS:
      return PREC_CAST;

    default:
      return PREC_MIN;
  }
}

//
// Memory management and utilities
//

E_Expr *E_alloc(E_ExprKind kind) {
  E_Expr *expr = calloc(1, sizeof(E_Expr));
  if (!expr) {
    return NULL;
  }
  expr->kind = kind;
  return expr;
}

void E_free(E_Expr *expr) {
  if (!expr) return;

  // Free attributes
  for (size_t i = 0; i < expr->attr_count; i++) {
    free(expr->attrs[i].content);
  }
  free(expr->attrs);

  // Free expression-specific data
  switch (expr->kind) {
    case EK_LITERAL:
      free(expr->data.literal.value);
      free(expr->data.literal.suffix);
      break;

    case EK_PATH:
      for (size_t i = 0; i < expr->data.path.segment_count; i++) {
        free(expr->data.path.segments[i].ident.name);
      }
      free(expr->data.path.segments);
      break;

    case EK_BINARY:
      E_free(expr->data.binary.left);
      E_free(expr->data.binary.right);
      break;

    case EK_UNARY:
      E_free(expr->data.unary.expr);
      break;

    case EK_ASSIGN:
      E_free(expr->data.assign.left);
      E_free(expr->data.assign.right);
      break;

    case EK_CALL:
      E_free(expr->data.call.func);
      E_list_free(&expr->data.call.args);
      break;

    case EK_METHOD_CALL:
      E_free(expr->data.method_call.receiver);
      free(expr->data.method_call.method.name);
      E_list_free(&expr->data.method_call.args);
      break;

    case EK_FIELD:
      E_free(expr->data.field.base);
      if (expr->data.field.is_named) {
        free(expr->data.field.member.named.name);
      }
      break;

    case EK_INDEX:
      E_free(expr->data.index.expr);
      E_free(expr->data.index.index);
      break;

    case EK_PAREN:
    case EK_GROUP:
      E_free(expr->data.paren.expr);
      break;

    case EK_REFERENCE:
      E_free(expr->data.reference.expr);
      break;
  }

  free(expr);
}

E_ExprList *E_list_new(void) {
  E_ExprList *list = calloc(1, sizeof(E_ExprList));
  if (!list) return NULL;

  list->capacity = 4;
  list->exprs = malloc(list->capacity * sizeof(E_Expr*));
  if (!list->exprs) {
    free(list);
    return NULL;
  }

  return list;
}

void E_list_push(E_ExprList *list, E_Expr *expr) {
  if (!list || !expr) return;

  if (list->count >= list->capacity) {
    list->capacity *= 2;
    list->exprs = realloc(list->exprs, list->capacity * sizeof(E_Expr*));
    if (!list->exprs) {
      return; // TODO: Better error handling
    }
  }

  list->exprs[list->count++] = expr;
}

void E_list_free(E_ExprList *list) {
  if (!list) return;

  for (size_t i = 0; i < list->count; i++) {
    E_free(list->exprs[i]);
  }
  free(list->exprs);
  list->exprs = NULL;
  list->count = 0;
  list->capacity = 0;
}

//
// Main expression parsing functions
//

E_Expr *E_parse(P_Parser *parser) {
  return E_parse_with_precedence(parser, PREC_MIN);
}

E_Expr *E_parse_with_precedence(P_Parser *parser, E_Precedence min_prec) {
  // Parse the left-hand side (unary expression)
  E_Expr *left = E_parse_unary(parser);
  if (!left) {
    return NULL;
  }

  // Parse binary operators with precedence climbing
  while (true) {
    E_Precedence prec = E_peek_precedence(parser);
    if (prec < min_prec) {
      break;
    }

    const T_Token *op_token = P_cur(parser);

    // Handle assignment separately
    if (op_token->kind == TK_EQ && P_peek(parser)->kind != TK_GT) {
      P_next(parser); // consume '='
      E_Expr *right = E_parse_with_precedence(parser, PREC_ASSIGN);
      if (!right) {
        E_free(left);
        return NULL;
      }

      E_Expr *assign_expr = E_alloc(EK_ASSIGN);
      if (!assign_expr) {
        E_free(left);
        E_free(right);
        return NULL;
      }
      assign_expr->data.assign.left = left;
      assign_expr->data.assign.right = right;
      left = assign_expr;
      continue;
    }

    // Parse binary operator
    E_BinOp binop = E_parse_binop(parser);
    P_next(parser); // consume operator

    // Determine right-hand side precedence
    E_Precedence next_min_prec = prec;
    if (prec != PREC_ASSIGN) {
      next_min_prec = prec + 1; // Left associative
    }

    E_Expr *right = E_parse_with_precedence(parser, next_min_prec);
    if (!right) {
      E_free(left);
      return NULL;
    }

    // Create binary expression
    E_Expr *binary_expr = E_alloc(EK_BINARY);
    if (!binary_expr) {
      E_free(left);
      E_free(right);
      return NULL;
    }
    binary_expr->data.binary.left = left;
    binary_expr->data.binary.op = binop;
    binary_expr->data.binary.right = right;
    left = binary_expr;
  }

  return left;
}

E_Expr *E_parse_unary(P_Parser *parser) {
  const T_Token *token = P_cur(parser);

  // Handle unary operators
  if (token->kind == TK_STAR || token->kind == TK_NOT || token->kind == TK_MINUS) {
    E_UnOp unop;
    switch (token->kind) {
      case TK_STAR:  unop = UOP_DEREF; break;
      case TK_NOT:   unop = UOP_NOT; break;
      case TK_MINUS: unop = UOP_NEG; break;
      default: return NULL; // Should not happen
    }

    P_next(parser); // consume operator
    E_Expr *expr = E_parse_unary(parser);
    if (!expr) {
      return NULL;
    }

    E_Expr *unary_expr = E_alloc(EK_UNARY);
    if (!unary_expr) {
      E_free(expr);
      return NULL;
    }
    unary_expr->data.unary.op = unop;
    unary_expr->data.unary.expr = expr;
    return unary_expr;
  }

  // Handle reference operators
  if (token->kind == TK_AND) {
    P_next(parser); // consume '&'

    bool is_mut = false;
    if (P_cur(parser)->kind == TK_MUT) {
      is_mut = true;
      P_next(parser); // consume 'mut'
    }

    E_Expr *expr = E_parse_unary(parser);
    if (!expr) {
      return NULL;
    }

    E_Expr *ref_expr = E_alloc(EK_REFERENCE);
    if (!ref_expr) {
      E_free(expr);
      return NULL;
    }
    ref_expr->data.reference.expr = expr;
    ref_expr->data.reference.is_mut = is_mut;
    return ref_expr;
  }

  // Parse primary expression and handle postfix operators
  return E_parse_primary(parser);
}

E_Expr *E_parse_primary(P_Parser *parser) {
  const T_Token *token = P_cur(parser);
  E_Expr *expr = NULL;

  // Handle parenthesized expressions
  if (token->kind == TK_LPAREN) {
    P_next(parser); // consume '('
    expr = E_parse(parser);
    if (!expr) {
      return NULL;
    }

    if (!P_consume(parser, TK_RPAREN)) {
      P_error(parser, "expected ')'");
      E_free(expr);
      return NULL;
    }

    E_Expr *paren_expr = E_alloc(EK_PAREN);
    if (!paren_expr) {
      E_free(expr);
      return NULL;
    }
    paren_expr->data.paren.expr = expr;
    expr = paren_expr;
  }
  // Handle literals
  else if (token->kind == TK_INTEGER || token->kind == TK_FLOAT ||
           token->kind == TK_STRING_LITERAL || token->kind == TK_RAW_STRING_LITERAL ||
           token->kind == TK_BYTE_STRING_LITERAL || token->kind == TK_RAW_BYTE_STRING_LITERAL ||
           token->kind == TK_C_STRING_LITERAL || token->kind == TK_RAW_C_STRING_LITERAL ||
           token->kind == TK_CHAR_LITERAL || token->kind == TK_BYTE_LITERAL ||
           token->kind == TK_TRUE || token->kind == TK_FALSE) {
    expr = E_parse_literal(parser);
  }
  // Handle paths and identifiers
  else if (token->kind == TK_IDENT || token->kind == TK_SELFVALUE ||
           token->kind == TK_SELFTYPE || token->kind == TK_SUPER ||
           token->kind == TK_CRATE || token->kind == TK_PATHSEP) {
    expr = E_parse_path(parser);
  }
  else {
    P_error(parser, "expected expression");
    return NULL;
  }

  if (!expr) {
    return NULL;
  }

  // Handle postfix operators (function calls, field access, indexing)
  while (true) {
    const T_Token *cur = P_cur(parser);

    if (cur->kind == TK_LPAREN) {
      // Function call
      expr = E_parse_call_or_method(parser, expr);
      if (!expr) {
        return NULL;
      }
    }
    else if (cur->kind == TK_DOT && P_peek(parser)->kind != TK_DOT) {
      // Field access or method call
      expr = E_parse_field_or_index(parser, expr);
      if (!expr) {
        return NULL;
      }
    }
    else if (cur->kind == TK_LBRACKET) {
      // Array indexing
      P_next(parser); // consume '['
      E_Expr *index = E_parse(parser);
      if (!index) {
        E_free(expr);
        return NULL;
      }

      if (!P_consume(parser, TK_RBRACKET)) {
        P_error(parser, "expected ']'");
        E_free(expr);
        E_free(index);
        return NULL;
      }

      E_Expr *index_expr = E_alloc(EK_INDEX);
      if (!index_expr) {
        E_free(expr);
        E_free(index);
        return NULL;
      }
      index_expr->data.index.expr = expr;
      index_expr->data.index.index = index;
      expr = index_expr;
    }
    else {
      break;
    }
  }

  return expr;
}

//
// Helper functions for creating expressions
//

E_Expr *E_make_literal(E_LitKind kind, const char *value, const char *suffix) {
  E_Expr *expr = E_alloc(EK_LITERAL);
  if (!expr) return NULL;

  expr->data.literal.kind = kind;
  expr->data.literal.value = my_strdup(value);
  expr->data.literal.suffix = suffix ? my_strdup(suffix) : NULL;

  if (!expr->data.literal.value || (suffix && !expr->data.literal.suffix)) {
    E_free(expr);
    return NULL;
  }

  return expr;
}

E_Expr *E_make_path_simple(const char *name) {
  E_Expr *expr = E_alloc(EK_PATH);
  if (!expr) return NULL;

  expr->data.path.segments = malloc(sizeof(E_PathSegment));
  if (!expr->data.path.segments) {
    E_free(expr);
    return NULL;
  }

  expr->data.path.segments[0].ident.name = my_strdup(name);
  if (!expr->data.path.segments[0].ident.name) {
    E_free(expr);
    return NULL;
  }

  expr->data.path.segment_count = 1;
  expr->data.path.leading_colon = false;

  return expr;
}

E_Expr *E_make_binary(E_Expr *left, E_BinOp op, E_Expr *right) {
  E_Expr *expr = E_alloc(EK_BINARY);
  if (!expr) {
    E_free(left);
    E_free(right);
    return NULL;
  }

  expr->data.binary.left = left;
  expr->data.binary.op = op;
  expr->data.binary.right = right;

  return expr;
}

E_Expr *E_make_unary(E_UnOp op, E_Expr *expr) {
  E_Expr *unary_expr = E_alloc(EK_UNARY);
  if (!unary_expr) {
    E_free(expr);
    return NULL;
  }

  unary_expr->data.unary.op = op;
  unary_expr->data.unary.expr = expr;

  return unary_expr;
}

E_Expr *E_make_call(E_Expr *func, E_ExprList *args) {
  E_Expr *expr = E_alloc(EK_CALL);
  if (!expr) {
    E_free(func);
    if (args) E_list_free(args);
    return NULL;
  }

  expr->data.call.func = func;
  if (args) {
    expr->data.call.args = *args;
    free(args); // Free the list structure but not the contents
  } else {
    expr->data.call.args.exprs = NULL;
    expr->data.call.args.count = 0;
    expr->data.call.args.capacity = 0;
  }

  return expr;
}

E_Expr *E_make_field(E_Expr *base, const char *field_name) {
  E_Expr *expr = E_alloc(EK_FIELD);
  if (!expr) {
    E_free(base);
    return NULL;
  }

  expr->data.field.base = base;
  expr->data.field.is_named = true;
  expr->data.field.member.named.name = my_strdup(field_name);
  if (!expr->data.field.member.named.name) {
    E_free(expr);
    return NULL;
  }

  return expr;
}

E_Expr *E_make_index(E_Expr *expr, E_Expr *index) {
  E_Expr *index_expr = E_alloc(EK_INDEX);
  if (!index_expr) {
    E_free(expr);
    E_free(index);
    return NULL;
  }

  index_expr->data.index.expr = expr;
  index_expr->data.index.index = index;

  return index_expr;
}

//
// Placeholder implementations for specific parsers
// These will be implemented in subsequent tasks
//

E_Expr *E_parse_literal(P_Parser *parser) {
  const T_Token *token = P_cur(parser);
  E_Expr *expr = NULL;

  switch (token->kind) {
    case TK_INTEGER:
      expr = E_make_literal(LIT_INT, token->literal ? token->literal : "0", NULL);
      break;
    case TK_FLOAT:
      expr = E_make_literal(LIT_FLOAT, token->literal ? token->literal : "0.0", NULL);
      break;
    case TK_STRING_LITERAL:
      expr = E_make_literal(LIT_STR, token->literal ? token->literal : "", NULL);
      break;
    case TK_RAW_STRING_LITERAL:
      expr = E_make_literal(LIT_STR, token->literal ? token->literal : "", NULL);
      break;
    case TK_BYTE_STRING_LITERAL:
      expr = E_make_literal(LIT_BYTE_STR, token->literal ? token->literal : "", NULL);
      break;
    case TK_RAW_BYTE_STRING_LITERAL:
      expr = E_make_literal(LIT_BYTE_STR, token->literal ? token->literal : "", NULL);
      break;
    case TK_C_STRING_LITERAL:
      expr = E_make_literal(LIT_C_STR, token->literal ? token->literal : "", NULL);
      break;
    case TK_RAW_C_STRING_LITERAL:
      expr = E_make_literal(LIT_C_STR, token->literal ? token->literal : "", NULL);
      break;
    case TK_CHAR_LITERAL:
      expr = E_make_literal(LIT_CHAR, token->literal ? token->literal : "'\\0'", NULL);
      break;
    case TK_BYTE_LITERAL:
      expr = E_make_literal(LIT_BYTE, token->literal ? token->literal : "b'\\0'", NULL);
      break;
    case TK_TRUE:
      expr = E_make_literal(LIT_BOOL, "true", NULL);
      break;
    case TK_FALSE:
      expr = E_make_literal(LIT_BOOL, "false", NULL);
      break;
    default:
      P_error(parser, "expected literal");
      return NULL;
  }

  if (expr) {
    P_next(parser); // consume the literal token
  }

  return expr;
}

E_Expr *E_parse_path(P_Parser *parser) {
  const T_Token *token = P_cur(parser);

  if (token->kind == TK_IDENT) {
    E_Expr *expr = E_make_path_simple(token->literal);
    if (expr) {
      P_next(parser); // consume identifier
    }
    return expr;
  }

  // TODO: Implement full path parsing with :: separators
  P_error(parser, "complex paths not yet implemented");
  return NULL;
}

E_Expr *E_parse_call_or_method(P_Parser *parser, E_Expr *base) {
  // TODO: Implement function call parsing
  P_error(parser, "function calls not yet implemented");
  E_free(base);
  return NULL;
}

E_Expr *E_parse_field_or_index(P_Parser *parser, E_Expr *base) {
  // TODO: Implement field access parsing
  P_error(parser, "field access not yet implemented");
  E_free(base);
  return NULL;
}

//
// Basic printing implementation
//

void E_print(const E_Expr *expr, T_Token **tokens) {
  E_print_with_precedence(expr, tokens, PREC_MIN, false);
}

void E_print_with_precedence(const E_Expr *expr, T_Token **tokens, E_Precedence parent_prec, bool is_left) {
  if (!expr || !tokens) return;

  // TODO: Implement proper precedence-aware printing
  // For now, just print a simple representation

  switch (expr->kind) {
    case EK_LITERAL:
      // TODO: Create and append literal token
      break;

    case EK_PATH:
      // TODO: Create and append path tokens
      break;

    case EK_BINARY:
      E_print_with_precedence(expr->data.binary.left, tokens, PREC_MIN, true);
      // TODO: Add operator token
      E_print_with_precedence(expr->data.binary.right, tokens, PREC_MIN, false);
      break;

    case EK_UNARY:
      // TODO: Add operator token
      E_print_with_precedence(expr->data.unary.expr, tokens, PREC_PREFIX, false);
      break;

    case EK_PAREN:
      // TODO: Add '(' token
      E_print_with_precedence(expr->data.paren.expr, tokens, PREC_MIN, false);
      // TODO: Add ')' token
      break;

    default:
      // TODO: Implement other expression types
      break;
  }
}
