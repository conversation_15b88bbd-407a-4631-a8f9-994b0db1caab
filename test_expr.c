// Test file for expression parsing

#include "src/parser/internal.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

void test_literal_parsing() {
  printf("Testing literal parsing...\n");

  // Create a simple test file with a literal
  FILE *test_file = fopen("test_literal.rs", "w");
  if (!test_file) {
    printf("✗ Failed to create test file\n");
    return;
  }
  fprintf(test_file, "42");
  fclose(test_file);

  L_Lexer *lexer = L_open("test_literal.rs", TE_2021);
  if (!lexer) {
    printf("✗ Failed to open lexer\n");
    remove("test_literal.rs");
    return;
  }

  P_Parser *parser = P_new(lexer);
  if (!parser) {
    printf("✗ Failed to create parser\n");
    L_close(lexer);
    remove("test_literal.rs");
    return;
  }

  E_Expr *expr = E_parse(parser);

  if (expr && expr->kind == EK_LITERAL && expr->data.literal.kind == LIT_INT) {
    printf("✓ Literal parsing test passed\n");
  } else {
    printf("✗ Literal parsing test failed\n");
  }

  if (expr) E_free(expr);
  L_close(lexer);
  remove("test_literal.rs");
}

void test_binary_expression_parsing() {
  printf("Testing binary expression parsing...\n");

  // Create a test file with a binary expression
  FILE *test_file = fopen("test_binary.rs", "w");
  if (!test_file) {
    printf("✗ Failed to create test file\n");
    return;
  }
  fprintf(test_file, "1 + 2");
  fclose(test_file);

  L_Lexer *lexer = L_open("test_binary.rs", TE_2021);
  if (!lexer) {
    printf("✗ Failed to open lexer\n");
    remove("test_binary.rs");
    return;
  }

  P_Parser *parser = P_new(lexer);
  if (!parser) {
    printf("✗ Failed to create parser\n");
    L_close(lexer);
    remove("test_binary.rs");
    return;
  }

  E_Expr *expr = E_parse(parser);

  if (expr && expr->kind == EK_BINARY && expr->data.binary.op == BOP_ADD) {
    printf("✓ Binary expression parsing test passed\n");
  } else {
    printf("✗ Binary expression parsing test failed\n");
  }

  if (expr) E_free(expr);
  L_close(lexer);
  remove("test_binary.rs");
}

void test_precedence() {
  printf("Testing operator precedence...\n");

  // Test that 1 + 2 * 3 parses as 1 + (2 * 3)
  FILE *test_file = fopen("test_precedence.rs", "w");
  if (!test_file) {
    printf("✗ Failed to create test file\n");
    return;
  }
  fprintf(test_file, "1 + 2 * 3");
  fclose(test_file);

  L_Lexer *lexer = L_open("test_precedence.rs", TE_2021);
  if (!lexer) {
    printf("✗ Failed to open lexer\n");
    remove("test_precedence.rs");
    return;
  }

  P_Parser *parser = P_new(lexer);
  if (!parser) {
    printf("✗ Failed to create parser\n");
    L_close(lexer);
    remove("test_precedence.rs");
    return;
  }

  E_Expr *expr = E_parse(parser);

  // Check if we got the expected structure: 1 + (2 * 3)
  bool success = false;
  if (expr && expr->kind == EK_BINARY && expr->data.binary.op == BOP_ADD) {
    // Right side should be (2 * 3)
    if (expr->data.binary.right &&
        expr->data.binary.right->kind == EK_BINARY &&
        expr->data.binary.right->data.binary.op == BOP_MUL) {
      success = true;
    }
  }

  if (success) {
    printf("✓ Precedence test passed\n");
  } else {
    printf("✗ Precedence test failed\n");
  }

  if (expr) E_free(expr);
  L_close(lexer);
  remove("test_precedence.rs");
}

int main() {
  printf("Running expression parser tests...\n\n");
  
  test_literal_parsing();
  test_binary_expression_parsing();
  test_precedence();
  
  printf("\n✓ All tests passed!\n");
  return 0;
}
