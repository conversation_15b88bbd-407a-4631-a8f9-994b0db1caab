// Test file for expression parsing

#include "src/parser/parser.h"
#include <stdio.h>
#include <string.h>
#include <assert.h>

// Forward declarations for functions we need
E_Expr *E_make_literal(E_LitKind kind, const char *value, const char *suffix);
E_Expr *E_make_binary(E_Expr *left, E_BinOp op, E_Expr *right);
void E_free(E_Expr *expr);

void test_literal_parsing() {
  printf("Testing literal parsing...\n");

  // Test creating a literal expression directly
  E_Expr *expr = E_make_literal(LIT_INT, "42", NULL);

  assert(expr != NULL);
  assert(expr->kind == EK_LITERAL);
  assert(expr->data.literal.kind == LIT_INT);
  assert(strcmp(expr->data.literal.value, "42") == 0);

  E_free(expr);

  printf("✓ Literal parsing test passed\n");
}

void test_binary_expression_parsing() {
  printf("Testing binary expression parsing...\n");

  // Test creating a binary expression directly
  E_Expr *left = E_make_literal(LIT_INT, "1", NULL);
  E_Expr *right = E_make_literal(LIT_INT, "2", NULL);
  E_Expr *expr = E_make_binary(left, BOP_ADD, right);

  assert(expr != NULL);
  assert(expr->kind == EK_BINARY);
  assert(expr->data.binary.op == BOP_ADD);

  // Check left operand
  assert(expr->data.binary.left != NULL);
  assert(expr->data.binary.left->kind == EK_LITERAL);
  assert(expr->data.binary.left->data.literal.kind == LIT_INT);
  assert(strcmp(expr->data.binary.left->data.literal.value, "1") == 0);

  // Check right operand
  assert(expr->data.binary.right != NULL);
  assert(expr->data.binary.right->kind == EK_LITERAL);
  assert(expr->data.binary.right->data.literal.kind == LIT_INT);
  assert(strcmp(expr->data.binary.right->data.literal.value, "2") == 0);

  E_free(expr);

  printf("✓ Binary expression parsing test passed\n");
}

void test_precedence() {
  printf("Testing operator precedence...\n");

  // Test that 1 + 2 * 3 is represented as 1 + (2 * 3)
  E_Expr *one = E_make_literal(LIT_INT, "1", NULL);
  E_Expr *two = E_make_literal(LIT_INT, "2", NULL);
  E_Expr *three = E_make_literal(LIT_INT, "3", NULL);

  // Create 2 * 3
  E_Expr *mul_expr = E_make_binary(two, BOP_MUL, three);

  // Create 1 + (2 * 3)
  E_Expr *expr = E_make_binary(one, BOP_ADD, mul_expr);

  assert(expr != NULL);
  assert(expr->kind == EK_BINARY);
  assert(expr->data.binary.op == BOP_ADD);

  // Left should be 1
  assert(expr->data.binary.left->kind == EK_LITERAL);
  assert(strcmp(expr->data.binary.left->data.literal.value, "1") == 0);

  // Right should be (2 * 3)
  assert(expr->data.binary.right->kind == EK_BINARY);
  assert(expr->data.binary.right->data.binary.op == BOP_MUL);
  assert(strcmp(expr->data.binary.right->data.binary.left->data.literal.value, "2") == 0);
  assert(strcmp(expr->data.binary.right->data.binary.right->data.literal.value, "3") == 0);

  E_free(expr);

  printf("✓ Precedence test passed\n");
}

int main() {
  printf("Running expression parser tests...\n\n");
  
  test_literal_parsing();
  test_binary_expression_parsing();
  test_precedence();
  
  printf("\n✓ All tests passed!\n");
  return 0;
}
