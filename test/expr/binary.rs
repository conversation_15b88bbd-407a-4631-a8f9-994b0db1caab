// Test file for binary expressions

// Arithmetic operators
1 + 2
3 - 4
5 * 6
7 / 8
9 % 10

// Comparison operators
1 == 2
3 != 4
5 < 6
7 <= 8
9 > 10
11 >= 12

// Logical operators
true && false
true || false

// Bitwise operators
1 & 2
3 | 4
5 ^ 6
7 << 8
9 >> 10

// Assignment operators
x = 42
y += 1
z -= 2
a *= 3
b /= 4
c %= 5
d &= 6
e |= 7
f ^= 8
g <<= 9
h >>= 10

// Precedence tests
1 + 2 * 3
4 * 5 + 6
7 + 8 * 9 + 10
1 + 2 + 3 * 4 * 5
(1 + 2) * 3
1 * (2 + 3)

// Complex precedence
1 + 2 * 3 - 4 / 5
a && b || c
x < y && y < z
1 << 2 + 3
a + b == c + d

// Chained comparisons (should be errors in some cases)
// 1 < 2 < 3  // This should be an error in Rust
// 1 == 2 == 3  // This should be an error in Rust

// Right associative assignment
a = b = c = 42
