// Test file for path expressions

// Simple identifiers
x
y
variable_name
CONSTANT_NAME
_underscore
_123

// Keywords as identifiers (in some contexts)
self
Self
super
crate

// Simple paths
std::mem::replace
std::collections::HashMap
crate::module::function
super::parent_function
self::local_function

// Paths with leading ::
::std::mem::replace
::core::ptr::null

// Generic paths (for future implementation)
// Vec::<i32>::new
// HashMap::<String, i32>::with_capacity
// Option::<T>::Some

// Method calls on paths
std::mem::size_of::<i32>()
Vec::<i32>::new()
String::from("hello")

// Field access on paths
module::CONSTANT.field
crate::config::DEFAULT.timeout

// Complex path expressions
std::collections::HashMap::<String, Vec<i32>>::new()
crate::utils::math::PI
super::super::root_function
self::inner::deep::function

// Paths in different contexts
let x = std::mem::replace;
std::mem::replace(&mut a, b)
use std::collections::HashMap;
