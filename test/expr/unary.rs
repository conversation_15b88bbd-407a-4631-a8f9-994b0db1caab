// Test file for unary expressions

// Unary operators
!true
!false
-42
-3.14
*ptr
&value
&mut mutable_value

// Nested unary operators
!!true
--42
**ptr
&*ptr
*&value

// Complex unary expressions
!x && y
-(a + b)
&(x + y)
*(&x)

// References with complex expressions
&(1 + 2)
&mut (x * y)
&[1, 2, 3]
&mut vec[0]

// Dereference with complex expressions
*(ptr + offset)
*func()
*obj.field

// Negation with complex expressions
-(x * y + z)
-func()
-obj.method()

// Logical not with complex expressions
!(x == y)
!(a && b)
!func()
!obj.is_valid()

// Precedence with unary operators
-x + y
!x && y
*ptr + 1
&x + &y
