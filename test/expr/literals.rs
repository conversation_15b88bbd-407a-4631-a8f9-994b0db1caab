// Test file for literal expressions

// Integer literals
42
0
123456789
0x1234
0o777
0b1010

// Float literals
3.14
1.0
2.5e10
1.23e-4

// String literals
"hello world"
"escaped \"quotes\""
"unicode: 🦀"

// Raw string literals
r"raw string"
r#"raw string with "quotes""#
r##"raw string with #"quotes"#"##

// Byte string literals
b"byte string"
br"raw byte string"

// C string literals
c"c string"
cr"raw c string"

// Character literals
'a'
'🦀'
'\n'
'\t'
'\''
'\\'

// Byte literals
b'a'
b'\n'
b'\x41'

// Boolean literals
true
false
